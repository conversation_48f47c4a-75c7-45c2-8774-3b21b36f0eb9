import { Component, ViewChild } from '@angular/core';
import { FormGroup, NgForm } from '@angular/forms';
import { NavigationExtras, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'app/services/api/user.service';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import swal from 'sweetalert2';
import { OtpVerificationComponent } from '../otp-verification/otp-verification.component';
import { ToastrService } from 'ngx-toastr';
import { PathConstant } from 'app/shared/constant/PathConstant';

@Component({
    selector: 'app-forgot-password-page',
    templateUrl: './forgot-password-page.component.html',
    styleUrls: ['./forgot-password-page.component.scss']
})

export class ForgotPasswordPageComponent {
  
  @ViewChild('f') forogtPasswordForm: NgForm;
  formObj: FormModel<any>;
  xForm: FormGroup;
  currentDate = new Date();
  
  constructor(
    private router: Router,
    private modalService: NgbModal,
    private userService: UserService,
    private toastrService: ToastrService
  ) { }
  
  ngOnInit(): void {
    this.initView();
  }
  
  initView() {
    this.formObj = {
      name: 'forgotPassword',
      colSize: 12,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        {
          key: 'email',
          label: 'Email',
          placeholder: 'Enter your email',
          controlType: FormConstant.TYPE_TEXT,
          icon: 'ft-mail',
          required: true
        }
      ],
      params: []
    }
  }
  
  onForm(form: FormGroup) {
    this.xForm = form;
  }
  
  onNext(data: any) {
    if (!data['email']) {
      return;
    }

    const email = data['email'];
    
    swal.fire({
      title: email,
      text: 'Is this email address correct?',
      icon: 'warning',
      reverseButtons: true,
      showCancelButton: true,
      showConfirmButton: true,
      cancelButtonText: 'No',
      confirmButtonText: 'Yes'
    }).then(result => {
      if (!result.isConfirmed) {
        return;
      }
      
      this.userService.checkUserForgotPassword(email).subscribe(response => {
        if (response.status.code !== 0) {
          return;
        }
        
        const modal = this.modalService.open(OtpVerificationComponent, { backdrop: 'static', keyboard:false });
        modal.componentInstance.email = email;

        modal.result.then(response => {
          if (!response) {
            return;
          }

          this.toastrService.success('OTP verified!', null, {
            positionClass: 'toast-top-right'
          });

          const extras: NavigationExtras = {
            state: {
              loginId: email,
              otp: response 
            }
          };
          this.router.navigate([PathConstant.RESET_PASSWORD_PAGE], extras);

        });
      });
    });
    
  }

}
