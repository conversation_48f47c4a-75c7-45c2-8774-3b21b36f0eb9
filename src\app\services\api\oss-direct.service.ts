import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";

@Injectable({
    providedIn: 'root'
})
export class OssDirectService {

    constructor(private httpClient: HttpClient) {
    }

    uploadFile(formData: FormData, url: string) {
        const headers = new HttpHeaders({
        });
        return this.httpClient.post<string>(url, formData, {headers});
    }
}