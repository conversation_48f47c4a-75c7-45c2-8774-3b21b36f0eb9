import { ChangeDetectorRef, Component, EventEmitter, Input, NgZone, OnInit, Output, ViewChild } from '@angular/core';
import { Anomalies } from 'app/model/anomalies';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { QuestionDropdown } from 'app/shared/components/ms-form/questions';
import { SearchFilterComponent } from 'app/shared/components/ms-form/widget';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { Page } from 'app/shared/components/msx-datatable/models/page';
import { Table } from 'app/shared/components/msx-datatable/models/table';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { BehaviorSubject } from 'rxjs';
import * as swalFunction from 'app/shared/data/sweet-alerts';
import { DashboardService } from 'app/services/api/dashboard.service';
import { ToastrService } from 'ngx-toastr';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { Router } from '@angular/router';
import { MsxPagingComponent } from 'app/shared/components/msx-view/msx-paging.component';
import { MsxFormControlService } from 'app/shared/components/ms-form/msx-form-control.service';
import { FormGroup } from '@angular/forms';
import { FormModel } from 'app/shared/components/ms-form/models';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AddTransactionGroupComponent } from 'app/pages/dashboard/dashboard-details/add-transaction-group/add-transaction-group.component';

@Component({
  selector: 'app-anomaly-detail-transaction',
  templateUrl: './anomaly-detail-transaction.component.html',
  styleUrls: ['./anomaly-detail-transaction.component.scss']
})
export class AnomalyDetailTransactionComponent implements OnInit {

  isVerticalLayout: boolean;
  swal = swalFunction;
  public WidgetType = WidgetType;
  searchFormObj:any;
  @Input() state: any;
  @Input() formObj: FormModel<any>;
  @Output() isDataChanges = new EventEmitter<boolean>();
  @Output() form: EventEmitter<FormGroup> = new EventEmitter<FormGroup>();
  @Output() result: EventEmitter<any> = new EventEmitter<any>();
  @Output() onClickRowDt = new EventEmitter<any>();
  container: MsxView; 
  @ViewChild(SearchFilterComponent) searchFilterComponent!: SearchFilterComponent;
  @ViewChild(MsxPagingComponent) msxPagingComponent!: MsxPagingComponent;
  serviceUrl = URLConstant.ListAnomaly;
  private datasource: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);
  currPage:number = 0;
  public pageNumber: BehaviorSubject<number> = new BehaviorSubject(1);
  msxForm: FormGroup;
  @Output() onClickRow = new EventEmitter<any>();
  msxDatatableTransactionHistoryComponent: AnomalyDetailTransactionComponent;

  constructor(
      private modalService: NgbModal, 
    private readonly formControlService: MsxFormControlService, 
    private global: GlobalService,
    private toastrService: ToastrService,
    private router: Router,
    private ngZone: NgZone,
    private cdr: ChangeDetectorRef,
    private dashboardService: DashboardService
  ) { }

  async ngOnInit(): Promise<void>  {
    this.searchFormObj = {
      name: 'AddTransactionGroupSearchForm',
      autoload: true,
      direction: FormConstant.DIRECTION_HORIZONTAL,
      colSize: 6,
      components: [ 
        new QuestionDropdown({
          key: 'accountNo',
          label: 'Account No',
          placeholder: 'Select Account No',
          serviceUrl: URLConstant.GetListAccountNo,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'accountNos',
            key: 'accountNo',
            value: 'accountNo'
          }, 
          params: { 
            dashboardName: this.state.dashboardName,
            tenantCode: this.global.user.role.tenantCode, 
            audit:{
              callerId:this.global.user.loginId,
              locale: CommonConstant.EN
            }
          }
        }),
        {
          key: 'description',
          label: 'Description',
          placeholder: 'Type description here',
          controlType: FormConstant.TYPE_TEXT
        },
        {
          key: 'type',
          label: 'Type',
          placeholder: 'Select type',
          controlType: FormConstant.TYPE_DROPDOWN,
          value: '',
          options: [
              {key: '', value: 'All'},
              {key: 'Credit', value: 'Credit'},
              {key: 'Debit', value: 'Debit'},
              {key: 'Header', value: 'Header'},
              {key: 'Summary', value: 'Summary'}
          ]
        },
      ],
      params: [
        {
          key: 'tenantCode',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.global.user.role.tenantCode 
        },
        {
          key: 'dashboardName',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.state.dashboardName 
        }, 
        {
          key: 'page',
          controlType: FormConstant.TYPE_HIDDEN,
          value: 1
        },
        {
          key: 'reason',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.state.reasonCode
        }
      ]
    }

    const AnomalyTable: Table<Anomalies> = {
      name: 'anomalies', 
        list: [],
        columns: [
            {
                type: ColumnType.Text,
                prop: 'no',
                label: 'No',
                width: 70
            },
            {
                type: ColumnType.Text,
                prop: 'file',
                label: 'File',
                width: 100
            },
            {
                type: ColumnType.Text,
                prop: 'accountNo',
                label: 'Account No',
                width: 100
            },
            {
                type: ColumnType.Date,
                format: CommonConstant.FORMAT_DATE,
                prop: 'date',
                label: 'Date',
                width: 100
            },
            {
                type: ColumnType.Text,
                prop: 'description',
                label: 'Description',
                width: 100
            },
            {
                type: ColumnType.Currency,
                prop: 'amount',
                label: 'Amount',
                width: 100
            },
            {
                type: ColumnType.Currency,
                prop: 'endingBalance',
                label: 'Ending Balance',
                width: 100
            },
            {
                type: ColumnType.Text,
                prop: 'type',
                label: 'Type',
                width: 100
            },
            {
              type: ColumnType.Action,
              label: 'Action',
              width: 100,
              action: [  
                  {
                      class: CommonConstant.TEXT_DANGER,
                      icon: 'ft-trash-2',
                      descr: 'Delete',
                      type: Act.Delete, 
                  }
              ]
          }
      ]
    }

    this.container = {
      title: '',
      components: [ 
        {
          type: WidgetType.SearchFilter,
          component: this.searchFormObj
        },
        {
          type: WidgetType.Datatable,
          component: AnomalyTable
        }
      ]
    };
  }

  getResult(data) {    
    console.log("data di datatable", data)
    if (data['status'] && data['status']['code'] === 0) {
      this.datasource.next(data); 
    }
    console.log('Result', this.datasource); 
  }

  getPage(page: Page) {
    console.log('requestPage', page);
    this.currPage = page.pageNumber + 1;
    this.getDataSourceByPage(this.currPage);
  }  

  getDataSourceByPage(pageNum: number) { 
    this.pageNumber.next(pageNum);
  }  

  onItemClick(event:any) {
    const data = event['data'];
    switch (event['act']['type']) {
      case Act.Delete:
        return this.deleteData(data); 
    }
  }

  deleteData(data: any) {
    this.swal.Confirm('Are you sure? This Anomaly will be permanently lost.').then(
      async (result) => {
        if (result.isConfirmed) {
          const request = { 
            anomalyId: data.anomalyId,
            tenantCode: this.global.user.role.tenantCode,
            dashboardName: this.state.dashboardName
          };
          await this.dashboardService.deleteAnomaly(request).toPromise().then(
            async (response) => {
               if (response['status']['code'] == 0) {
                this.toastrService.success('Data successfully deleted!', null, {
                  positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
                });

                const request = { 
                  reason: this.state.reasonCode,
                  dashboardName: this.state.dashboardName,
                  tenantCode: this.global.user.role.tenantCode,
                  page: 1 
                };
                const resultList = await this.dashboardService.getListAnomaly(request).toPromise();
                if (resultList['status']['code'] === 0 && resultList['totalResult'] === 0) {
                  this.goBack();
                  return;
                }
                if (resultList["status"]["code"] == 0) {
                  this.ngZone.run(() => {
                    this.isDataChanges.emit(true);
                    this.searchFilterComponent.initSearchForm();
                    this.getResult(resultList);
                    this.cdr.detectChanges();
                  })
                } 
              }
            }
          )
        }
      }
    );
  }

  goBack() { 
    delete this.state.reason;
    delete this.state.reasonCode;
    delete this.state.stateDashboard;
    this.router.navigate([PathConstant.DASHBOARD_DETAIL], { 
      state: { 
        state: this.state, 
        indexTab: CommonConstant.ANOMALY
      }
    });
  }

  addNewAnomaly(){
    const modal = this.modalService.open(AddTransactionGroupComponent, {
      centered: true,
      backdrop: 'static',
      size: 'xl'
    });  
    modal.componentInstance.formSource = CommonConstant.ANOMALY;
    modal.componentInstance.dashboardName = this.state.dashboardName;  

    modal.componentInstance.result.subscribe(async() => {
      this.isDataChanges.emit(true);
      this.searchFilterComponent.initSearchForm();
      const request = { 
        reason: this.state.reasonCode,
        dashboardName: this.state.dashboardName,
        tenantCode: this.global.user.role.tenantCode,
        page: 1 
      };
      const resultList = await this.dashboardService.getListAnomaly(request).toPromise();
      if (resultList["status"]["code"] == 0) {
        this.ngZone.run(() => {
          this.isDataChanges.emit(true);
          this.searchFilterComponent.initSearchForm();
          this.getResult(resultList);
          this.cdr.detectChanges();
        })
      } 
    }); 
  }

  onClickedRow(event){
    const info = { 
      boxLocation : event.boxLocation,
      boxPage : event.boxPage,
      fileSourcePath: event.fileSourcePath
    }
    this.onClickRow.emit(info); 
  }

}
