import { ChangeDetectorRef, Component, EventEmitter, Input, NgZone, OnInit, Output, ViewChild } from '@angular/core';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { QuestionDropdown, QuestionTextbox } from 'app/shared/components/ms-form/questions';
import { SearchFilterComponent } from 'app/shared/components/ms-form/widget';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { Page } from 'app/shared/components/msx-datatable/models/page';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { BehaviorSubject } from 'rxjs';
import * as swalFunction from 'app/shared/data/sweet-alerts';
import { DashboardService } from 'app/services/api/dashboard.service';
import { ToastrService } from 'ngx-toastr';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { Router } from '@angular/router';
import { MsxPagingComponent } from 'app/shared/components/msx-view/msx-paging.component';
import { FormGroup } from '@angular/forms';
import { FormModel } from 'app/shared/components/ms-form/models';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AddTransactionGroupComponent } from 'app/pages/dashboard/dashboard-details/add-transaction-group/add-transaction-group.component';
import { MetadataTable } from './anomaly-detail-metadata.list.view';
import { AnomalyTable } from '../anomaly-detail.list.view';

@Component({
  selector: 'app-anomaly-detail-transaction',
  templateUrl: './anomaly-detail-transaction.component.html',
  styleUrls: ['./anomaly-detail-transaction.component.scss']
})
export class AnomalyDetailTransactionComponent implements OnInit {

  isVerticalLayout: boolean;
  isMetadataAnomaly: boolean = false;
  swal = swalFunction;
  public WidgetType = WidgetType;
  searchFormObj: any;
  container: MsxView;
  serviceUrl: string = URLConstant.ListAnomaly;
  currPage: number = 0;
  msxForm: FormGroup;
  msxDatatableTransactionHistoryComponent: AnomalyDetailTransactionComponent;

  @Input() state: any;
  @Input() formObj: FormModel<any>;
  
  @Output() isDataChanges = new EventEmitter<boolean>();
  @Output() form: EventEmitter<FormGroup> = new EventEmitter<FormGroup>();
  @Output() result: EventEmitter<any> = new EventEmitter<any>();
  @Output() onClickRowDt = new EventEmitter<any>();
  @Output() onClickRow = new EventEmitter<any>();
  
  @ViewChild(SearchFilterComponent) searchFilterComponent!: SearchFilterComponent;
  @ViewChild(MsxPagingComponent) msxPagingComponent!: MsxPagingComponent;
  
  private datasource: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);
  public pageNumber: BehaviorSubject<number> = new BehaviorSubject(1);

  constructor(
    private modalService: NgbModal, 
    private global: GlobalService,
    private toastrService: ToastrService,
    private router: Router,
    private ngZone: NgZone,
    private cdr: ChangeDetectorRef,
    private dashboardService: DashboardService
  ) {
    // this.isMetadataAnomaly = this.state.reasonCode === 'FRAUD_RULE_INCONSISTENT_METADATA';
  }

  async ngOnInit(): Promise<void>  {
    
    this.isMetadataAnomaly = this.state.reasonCode === 'FRAUD_RULE_INCONSISTENT_METADATA';
    this.serviceUrl = this.isMetadataAnomaly ? URLConstant.ListAnomalyMetadata : URLConstant.ListAnomaly;
    this.searchFormObj = this.isMetadataAnomaly ? this.getMetadataSearchFilter() : this.getDefaultSearchFilter();

    this.container = {
      title: '',
      components: [ 
        {
          type: WidgetType.SearchFilter,
          component: this.searchFormObj
        },
        {
          type: WidgetType.Datatable,
          component: this.isMetadataAnomaly ? MetadataTable : AnomalyTable
        }
      ]
    };
  }

  getMetadataSearchFilter() {
    return {
      name: 'AddTransactionGroupSearchForm',
      autoload: true,
      direction: FormConstant.DIRECTION_HORIZONTAL,
      colSize: 6,
      components: [
        new QuestionTextbox({
          key: 'reasonX',
          label: 'Reason',
          value: this.state.reason,
          readonly: true
        })
      ],
      params: [
        {
          key: 'tenantCode',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.global.user.role.tenantCode 
        },
        {
          key: 'dashboardName',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.state.dashboardName 
        }, 
        {
          key: 'page',
          controlType: FormConstant.TYPE_HIDDEN,
          value: 1
        },
        {
          key: 'reason',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.state.reasonCode
        }
      ]
    } 
  }

  getDefaultSearchFilter() {
    return {
      name: 'AddTransactionGroupSearchForm',
      autoload: true,
      direction: FormConstant.DIRECTION_HORIZONTAL,
      colSize: 6,
      components: [ 
        new QuestionDropdown({
          key: 'accountNo',
          label: 'Account No',
          placeholder: 'Select Account No',
          serviceUrl: URLConstant.GetListAccountNo,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'accountNos',
            key: 'accountNo',
            value: 'accountNo'
          }, 
          params: { 
            dashboardName: this.state.dashboardName,
            tenantCode: this.global.user.role.tenantCode, 
            audit:{
              callerId:this.global.user.loginId,
              locale: CommonConstant.EN
            }
          }
        }),
        {
          key: 'description',
          label: 'Description',
          placeholder: 'Type description here',
          controlType: FormConstant.TYPE_TEXT
        },
        {
          key: 'type',
          label: 'Type',
          placeholder: 'Select type',
          controlType: FormConstant.TYPE_DROPDOWN,
          value: '',
          options: [
              {key: '', value: 'All'},
              {key: 'Credit', value: 'Credit'},
              {key: 'Debit', value: 'Debit'},
              {key: 'Header', value: 'Header'},
              {key: 'Summary', value: 'Summary'}
          ]
        },
      ],
      params: [
        {
          key: 'tenantCode',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.global.user.role.tenantCode 
        },
        {
          key: 'dashboardName',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.state.dashboardName 
        }, 
        {
          key: 'page',
          controlType: FormConstant.TYPE_HIDDEN,
          value: 1
        },
        {
          key: 'reason',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.state.reasonCode
        }
      ]
    } 
  }

  getResult(data) {    
    console.log("data di datatable", data)
    if (data['status'] && data['status']['code'] === 0) {
      this.datasource.next(data); 
    }
    console.log('Result', this.datasource); 
  }

  getPage(page: Page) {
    console.log('requestPage', page);
    this.currPage = page.pageNumber + 1;
    this.getDataSourceByPage(this.currPage);
  }  

  getDataSourceByPage(pageNum: number) { 
    this.pageNumber.next(pageNum);
  }  

  onItemClick(event:any) {
    const data = event['data'];
    switch (event['act']['type']) {
      case Act.Delete:
        return this.deleteData(data); 
    }
  }

  deleteData(data: any) {
    this.swal.Confirm('Are you sure? This Anomaly will be permanently lost.').then(
      async (result) => {
        if (result.isConfirmed) {
          const request = { 
            anomalyId: data.anomalyId,
            tenantCode: this.global.user.role.tenantCode,
            dashboardName: this.state.dashboardName
          };
          await this.dashboardService.deleteAnomaly(request).toPromise().then(
            async (response) => {
               if (response['status']['code'] == 0) {
                this.toastrService.success('Data successfully deleted!', null, {
                  positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
                });

                const request = { 
                  reason: this.state.reasonCode,
                  dashboardName: this.state.dashboardName,
                  tenantCode: this.global.user.role.tenantCode,
                  page: 1 
                };
                const resultList = await this.dashboardService.getListAnomaly(request).toPromise();
                if (resultList['status']['code'] === 0 && resultList['totalResult'] === 0) {
                  this.goBack();
                  return;
                }
                if (resultList["status"]["code"] == 0) {
                  this.ngZone.run(() => {
                    this.isDataChanges.emit(true);
                    this.searchFilterComponent.initSearchForm();
                    this.getResult(resultList);
                    this.cdr.detectChanges();
                  })
                } 
              }
            }
          )
        }
      }
    );
  }

  goBack() { 
    delete this.state.reason;
    delete this.state.reasonCode;
    delete this.state.stateDashboard;
    this.router.navigate([PathConstant.DASHBOARD_DETAIL], { 
      state: { 
        state: this.state, 
        indexTab: CommonConstant.ANOMALY
      }
    });
  }

  addNewAnomaly(){
    const modal = this.modalService.open(AddTransactionGroupComponent, {
      centered: true,
      backdrop: 'static',
      size: 'xl'
    });  
    modal.componentInstance.formSource = CommonConstant.ANOMALY;
    modal.componentInstance.dashboardName = this.state.dashboardName;  

    modal.componentInstance.result.subscribe(async() => {
      this.isDataChanges.emit(true);
      this.searchFilterComponent.initSearchForm();
      const request = { 
        reason: this.state.reasonCode,
        dashboardName: this.state.dashboardName,
        tenantCode: this.global.user.role.tenantCode,
        page: 1 
      };
      const resultList = await this.dashboardService.getListAnomaly(request).toPromise();
      if (resultList["status"]["code"] == 0) {
        this.ngZone.run(() => {
          this.isDataChanges.emit(true);
          this.searchFilterComponent.initSearchForm();
          this.getResult(resultList);
          this.cdr.detectChanges();
        })
      } 
    }); 
  }

  onClickedRow(event){
    const info = { 
      boxLocation : event.boxLocation,
      boxPage : event.boxPage,
      fileSourcePath: event.fileSourcePath
    }
    this.onClickRow.emit(info); 
  }

}
