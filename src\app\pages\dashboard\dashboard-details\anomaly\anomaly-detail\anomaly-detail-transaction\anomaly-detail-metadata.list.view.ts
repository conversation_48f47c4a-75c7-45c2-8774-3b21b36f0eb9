import { Act } from "app/shared/components/msx-datatable/enums/act";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { CommonConstant } from "app/shared/constant/common.constant";

export const MetadataTable: Table<any> = {
    name: 'anomalies',
    list: [],
    columns: [
        {
            type: ColumnType.Number,
            prop: 'no',
            label: 'No',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'file',
            label: 'File Name',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'creator',
            label: 'Creator',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'producer',
            label: 'Producer',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'createdDateFile',
            label: 'Create Date',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'modificationDateFile',
            label: 'Modification Date',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'type',
            label: 'Type',
            width: 100
        },
        {
            type: ColumnType.Action,
            label: 'Action',
            width: 100,
            action: [
                {
                    class: CommonConstant.TEXT_DANGER,
                    icon: 'ft-trash-2',
                    descr: 'Delete',
                    type: Act.Delete
                }
            ]
        }
    ]
}