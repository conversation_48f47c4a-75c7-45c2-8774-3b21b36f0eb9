
import { OcrR<PERSON><PERSON> } from "app/model/ocr-result";
import { Act } from "app/shared/components/msx-datatable/enums/act";
import { Align } from "app/shared/components/msx-datatable/enums/align";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { CommonConstant } from "app/shared/constant/common.constant";

export const OcrResultTable: Table<OcrResult> = {
    name: 'results',  
    list: [],
    columns: [
        {
            type: ColumnType.Number, 
            prop: 'rowNum',
            label: 'No',
            width: 50
        },
        {
            type: ColumnType.Text, 
            prop: 'filename',
            label: 'File Name',
            width: 50
        },
        {
            type: ColumnType.Text,
            prop: 'bank',
            label: 'Bank',
            width: 50
        },
        {
            type: ColumnType.Text,
            prop: 'accountNo',
            label: 'Account No',
            width: 50
        },  
        {
            type: ColumnType.Text,
            prop: 'accountName',
            label: 'Account Name',
            width: 50
        },
        {
            type: ColumnType.Text,
            prop: 'page',
            label: 'Total Page',
            width: 50
        },
        {
            type: ColumnType.Text,
            prop: 'uploadDate',
            label: 'Upload Date',
            width: 50
        },
        {
            type: ColumnType.Text,
            prop: 'lastUpdated',
            label: 'Last Updated',
            width: 50
        },
        {
            type: ColumnType.Text,
            prop: 'status',
            label: 'Status',
            width: 50
        },
        {
            type: ColumnType.Text,
            prop: 'redPercentage',
            label: 'Low Confidence',
            width: 50, 
            condition: true,
            conditionVariable: 'redPercentageWarning',
            conditionExpected: '1',
            conditionedClass: 'text-danger'
        },
        {
            type: ColumnType.Action,
            label: 'Action',
            width: 50,
            action: [ 
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: 'ft-edit',
                    descr: 'Edit',
                    type: Act.Edit, 
                    condition: true,
                    conditionVariable: 'editable',
                    conditionExpected: '0',
                    conditionedClass: 'd-none'
                },
                {
                    class: CommonConstant.TEXT_DANGER,
                    icon: 'ft-trash-2',
                    descr: 'Delete',
                    type: Act.Delete, 
                    condition: true,
                    conditionVariable: 'status',
                    conditionExpected: 'Pending',
                    conditionedClass: 'd-none'
                }
            ]
        }
    ]
}