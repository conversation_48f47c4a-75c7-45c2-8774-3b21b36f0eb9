import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {
  CheckboxComponent,
  CurrencyComponent,
  DateComponent,
  EmailComponent,
  NumberComponent,
  PasswordComponent, SelectComponent,
  TextComponent,
  UrlComponent
} from './components';
import {FormComponent, SearchFilterComponent} from './widget';
import {NgbModule} from '@ng-bootstrap/ng-bootstrap';
import {NgSelectModule} from '@ng-select/ng-select';
import {QuestionComponent} from './components/dynamic-question/question.component';
import {MsxFormControlService} from './msx-form-control.service';
import {InputComponent} from './components/input/input.component';
import { FileComponent } from './components/file/file.component';
import {IConfig, NgxMaskModule} from 'ngx-mask';
import { TextareaComponent } from './components/textarea/textarea.component';
import { ImageComponent } from './components/image/image.component';
import { SharedModule } from 'app/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { SearchFilterTransactionComponent } from './widget/search-filter-transaction/search-filter-transaction.component';
import { SelectV2Component } from './components/select-v2/select-v2.component';

const maskConfig: Partial<IConfig> = {
  validation: false,
};

@NgModule({
  declarations: [
    TextComponent,
    DateComponent,
    NumberComponent,
    EmailComponent,
    PasswordComponent,
    UrlComponent,
    CurrencyComponent,
    SelectComponent,
    CheckboxComponent,
    SearchFilterComponent,
    InputComponent,
    FormComponent,
    QuestionComponent,
    FileComponent,
    TextareaComponent,
    ImageComponent,
    SearchFilterTransactionComponent,
    SelectV2Component,
  ],
  exports: [
    TextComponent,
    TextareaComponent,
    SearchFilterComponent,
    SearchFilterTransactionComponent, 
    FormComponent,
    ImageComponent,
    QuestionComponent,
    DateComponent,
    SelectComponent,
    SelectV2Component,
    CurrencyComponent,
    FileComponent
  ],
  imports: [
    CommonModule,
    NgxMaskModule.forRoot(maskConfig),
    FormsModule,
    ReactiveFormsModule,
    NgbModule,
    NgSelectModule,
    TranslateModule
  ],
  providers: [
    MsxFormControlService
  ]
})
export class MsFormModule { }
