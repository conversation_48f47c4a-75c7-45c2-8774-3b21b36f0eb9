import { Injectable } from "@angular/core";
import { AppService } from "./app.service";
import { AddDashboardRequest } from "app/model/api/dashboard/add-dashboard.request";
import { URLConstant } from "app/shared/constant/URLConstant";
import { BaseResponse } from "app/model/api/base.response";
import { GetListNonBusinessTransactionGroupRequest } from "app/model/api/NonBusinessTransactionGroup/get-list-non-business-transaction-group.request"; 
import { AddTransactionGroupRequest } from "app/model/api/NonBusinessTransactionGroup/add-transaction-group.request";
import { GetListSubGroupRequest } from "app/model/api/SupplierBuyerGroup/get-list-sub-group";
import { EditMainGroupDetailRequest } from "app/model/api/SupplierBuyerGroup/edit-main-group-detail-request";
import { BankStatementHeaderTransactionRequest } from "app/model/api/OcrResult/bank-statement-header-transaction";
import { ListBankStatementTransactionDetailRequest } from "app/model/api/OcrResult/list-bank-statement-header-transaction-detail-request";
import { SaveBankStatementHeaderRequest } from "app/model/api/OcrResult/save-bank-statement-header-request";
import { DeleteBankStatementRequest } from "app/model/api/OcrResult/delete-bank-statement-request";
import { AddAnomalyRequest } from "app/model/api/Anomaly/add-anomaly.request";
import { GetCalculatedListBankStatementSummaryRequest } from "app/model/api/OcrResult/get-calculated-list-bank-statement-summary.request";
import { SaveBankStatementSummaryRequest } from "app/model/api/OcrResult/save-bank-statement-summary-request";
import { SaveBankStatementTransactionDetailRequest } from "app/model/api/OcrResult/save-bank-statement-transaction-detail-request";
import { DeleteBankStatementTransactionDetailRequest } from "app/model/api/OcrResult/delete-bank-statement-transaction-detail-request";
import { Subject } from "rxjs";
import { WarningStatusResponse } from "app/model/api/dashboard/warning-status.response";
import { AddTenantRequest } from "app/shared/dto/tenant/add-tenant.request";
import { HitlActionRequest } from "app/model/api/hitl/hitl-action-request";

@Injectable({
    providedIn: 'root'
})
export class DashboardService extends AppService {
    private functionCallSource = new Subject<{
        date: string;
        request: any;
      }>();
    functionCall$ = this.functionCallSource.asObservable();

    callGetCalculatedListBankStatementSummary(date:string,request: BankStatementHeaderTransactionRequest) {
      this.functionCallSource.next({ date, request }); // Emit the request object
    }

    // Dashboard
    addDashboard(request: AddDashboardRequest) {
        return this.client.post<BaseResponse>(URLConstant.AddDashboard, request);   
    } 

    addNewBankStatement(request: AddDashboardRequest) {
        return this.client.post<BaseResponse>(URLConstant.AddNewBankStatement, request);   
    } 

    deletedDashboard(request: any) {
        return this.client.post<BaseResponse>(URLConstant.DeleteDashboard, request);   
    }
    consolidateStatus(request: any) {
        return this.client.post<BaseResponse>(URLConstant.ConsolidateStatus, request);   
    }  

    startConsolidate(request: any) {
        return this.client.post<BaseResponse>(URLConstant.StartConsolidate, request);   
    } 

    getWarningStatus(request: any) {
        return this.client.post<WarningStatusResponse>(URLConstant.WarningStatus, request);   
    } 

    // Business Transaction Group
    getListBusinessTransactionGroup(request: GetListNonBusinessTransactionGroupRequest) {
        return this.client.post<BaseResponse>(URLConstant.ListBusinessTransactionGroup, request);
    }

    addBusinessTransactionGroup(request: any) {
        return this.client.post<BaseResponse>(URLConstant.AddBusinessTransactionGroup, request);   
    }

    deleteBusinessTransactionGroup(request: any) {
        return this.client.post<BaseResponse>(URLConstant.DeleteBusinessTransactionGroup, request);   
    }

    getListDetailBusinessTransactionGroup(request: any) {
        return this.client.post<BaseResponse>(URLConstant.ListDetailBusinessTransactionGroup, request);
    }
 
    deleteDetailBusinessTransactionGroup(request: any) {
        return this.client.post<BaseResponse>(URLConstant.DeleteDetailBusinessTransactionGroup, request);
    } 
    
    // Non Business Transaction Group
    getListNonBusinessTransactionGroup(request: GetListNonBusinessTransactionGroupRequest) {
        return this.client.post<BaseResponse>(URLConstant.ListNonBusinessTransactionGroup, request);
    }

    addNonBusinessTransactionGroup(request: any) {
        return this.client.post<BaseResponse>(URLConstant.AddNonBusinessTransactionGroup, request);   
    }

    deleteNonBusinessTransactionGroup(request: any) {
        return this.client.post<BaseResponse>(URLConstant.DeleteNonBusinessTransactionGroup, request);   
    }
    
    getListDetailNonBusinessTransactionGroup(request: any) {
        return this.client.post<BaseResponse>(URLConstant.ListDetailNonBusinessTransactionGroup, request);
    }

    addTransactionGroup(url:string, request: AddTransactionGroupRequest) {
        return this.client.post<BaseResponse>(url, request);   
    }

    deleteDetailNonBusinessTransactionGroup(request: any) {
        return this.client.post<BaseResponse>(URLConstant.DeleteDetailNonBusinessTransactionGroup, request);
    } 

    // Supplier Buyer Group
    addMainSupplierBuyerGroup(request: any) {
        return this.client.post<BaseResponse>(URLConstant.AddMainSupplierBuyerGroup, request);   
    } 
  
    deleteMainSupplierBuyerGroup(request: any) {
        return this.client.post<BaseResponse>(URLConstant.DeleteMainSupplierBuyerGroup, request);   
    } 

    addSupplierBuyerSubGroup(request: any) {
        return this.client.post<BaseResponse>(URLConstant.AddSupplierBuyerSubGroup, request);   
    } 

    deleteSupplierBuyerSubGroup(request: any) {
        return this.client.post<BaseResponse>(URLConstant.DeleteSupplierBuyerSubGroup, request);   
    } 

    deleteSubGroupMember(request: any) {
        return this.client.post<BaseResponse>(URLConstant.DeleteSubGroupMember, request);   
    } 

    getListSubGroupDropdown(request: GetListSubGroupRequest) {
        return this.client.post<BaseResponse>(URLConstant.GetListSubGroupDropdown, request);   
    } 

    getListSubGroupOfMainGroup(request: GetListSubGroupRequest) {
        return this.client.post<BaseResponse>(URLConstant.GetListSubGroupOfMainGroup, request);   
    } 

    GetListSubGroupMember(request: any) {
        return this.client.post<BaseResponse>(URLConstant.GetListSubGroupMember, request);
    }

    editMainGroupDetail(request: EditMainGroupDetailRequest) {
        return this.client.post<BaseResponse>(URLConstant.EditMainGroupDetail, request);   
    }  

    //Anomaly
    addAnomaly(request: AddAnomalyRequest) {
        return this.client.post<BaseResponse>(URLConstant.AddAnomaly, request);   
    } 
  
    deleteAnomaly(request: any) {
        return this.client.post<BaseResponse>(URLConstant.DeleteAnomaly, request);   
    } 

    deleteGroupAnomaly(request: any) {
        return this.client.post<BaseResponse>(URLConstant.DeleteGroupAnomaly, request);   
    } 

    getListAnomaly(request: any) {
        return this.client.post<BaseResponse>(URLConstant.ListAnomaly, request);
    }

    // OCR Result  
    getBankStatementHeaderTransaction(request: BankStatementHeaderTransactionRequest) {
        return this.client.post<BaseResponse>(URLConstant.GetBankStatementHeaderTransaction, request);   
    } 

    getBankStatementHeaderTransactionFile(request: BankStatementHeaderTransactionRequest) {
        return this.client.post<any>(URLConstant.GetBankStatementHeaderTransactionFile, request);   
    }  

    getBankStatementHeaderTransactionSummary(request: BankStatementHeaderTransactionRequest) {
        return this.client.post<BaseResponse>(URLConstant.GetBankStatementHeaderTransactionSummary, request);   
    } 

    getCalculatedListBankStatementSummary(request: GetCalculatedListBankStatementSummaryRequest) {
        return this.client.post<BaseResponse>(URLConstant.GetCalculatedListBankStatementSummary, request);   
    } 

    getListBankStatementTransactionDetail(request: ListBankStatementTransactionDetailRequest) {
        return this.client.post<any>(URLConstant.GetListBankStatementTransactionDetail, request);   
    } 

    saveBankStatementHeader(request: SaveBankStatementHeaderRequest) {
        return this.client.post<any>(URLConstant.SaveBankStatementHeader, request);   
    }

    saveBankStatementSummary(request: SaveBankStatementSummaryRequest) {
        return this.client.post<any>(URLConstant.SaveBankStatementSummary, request);   
    }

    saveorEditBankStatementTransactionDetail(request: SaveBankStatementTransactionDetailRequest, url:string) {
        return this.client.post<any>(url, request);   
    } 

    deleteBankStatement(request: DeleteBankStatementRequest) {
        return this.client.post<BaseResponse>(URLConstant.DeleteBankStatement, request);
    }

    deleteBankStatementTransactionDetail(request: DeleteBankStatementTransactionDetailRequest) {
        return this.client.post<BaseResponse>(URLConstant.DeleteBankStatementTransactionDetail, request);
    }

    // Add Transaction
    getListAccountNo(request: any) {
        return this.client.post<BaseResponse>(URLConstant.GetListAccountNo, request);
    }

    getListPeriod(request: any) {
        return this.client.post<BaseResponse>(URLConstant.GetListPeriod, request);
    }

    getListLov(request: any) {
        return this.client.post<BaseResponse>(URLConstant.GetLov, request);
    }

    //INSIGHT
    getInsightBasicInformation(request: any) {
        return this.client.post<BaseResponse>(URLConstant.BasicInformation, request);
    }

    editDashboardName(request: any) {
        return this.client.post<BaseResponse>(URLConstant.EditDashboardName, request);
    }

    consolidatedBankStatementList(request: any) {
        return this.client.post<BaseResponse>(URLConstant.ConsolidatedBankStatementList, request);
    }

    consolidateDocumentUrl(request: any) {
        return this.client.post<BaseResponse>(URLConstant.ConsolidateDocumentUrl, request);
    }

    // INSIGHT - SUPPLIER BUYER
    supplierBuyerInsightHeader(request: any) {
        return this.client.post<any>(URLConstant.SupplierBuyerInsightHeader, request);
    }

    supplierBuyerMonthlyCashflowAndGrowthRate(request: any) {
        return this.client.post<any>(URLConstant.SupplierBuyerMonthlyCashflowAndGrowthRate, request);
    }

    supplierBuyerTopFive(request: any) {
        return this.client.post<any>(URLConstant.SupplierBuyerTopFive, request);
    }

    // INSIGHT - GENERAL DASHBOARD
    generalInsightHeader(request: any) {
        return this.client.post<any>(URLConstant.GeneralInsightHeader, request);
    }

    generalCashFlow(request: any) {
        return this.client.post<any>(URLConstant.GeneralCashFlow, request);
    }

    generalInsightBodyCard(request: any) {
        return this.client.post<any>(URLConstant.GeneralInsightBodyCard, request);
    }

    generalDailyAnalysis(request: any) {
        return this.client.post<any>(URLConstant.GeneralDailyAnalysis, request);
    }

    generalCircularChartData(request: any) {
        return this.client.post<any>(URLConstant.GeneralCircularChartData, request);
    }

    generalCircularHeader(request: any) {
        return this.client.post<any>(URLConstant.GeneralCircularHeader, request);
    }

    //Circular
    getListViewTransactionCircular(request: any) {
        return this.client.post<BaseResponse>(URLConstant.ListViewTransactionCircular, request);
    }

    deleteCircular(request: any) {
        return this.client.post<BaseResponse>(URLConstant.DeleteCircular, request);   
    } 

    checkGroupNameCircular(request: any) {
        return this.client.post<BaseResponse>(URLConstant.CheckGroupNameCircular, request);   
    }

    //Tenant
    addTenant(request: AddTenantRequest) {
        return this.client.post<BaseResponse>(URLConstant.AddTenant, request);
    }

    // HITL (Human In The Loop)
    submitHitlRemark(request: HitlActionRequest) {
        return this.client.post<BaseResponse>(URLConstant.HitlSubmitRemark, request);
    }
}