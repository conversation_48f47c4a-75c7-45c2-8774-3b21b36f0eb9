import { ChangeDetectorRef, Component, EventEmitter, Input, NgZone, OnInit, Output } from '@angular/core';
import { DashboardService } from 'app/services/api/dashboard.service';
import { HttpService } from 'app/services/http.service';
import { FormModel } from 'app/shared/components/ms-form/models';
import { GlobalService } from 'app/shared/data/global.service';
import * as swalFunction from 'app/shared/data/sweet-alerts';  
import { BankStatementHeaderTransactionRequest } from 'app/model/api/OcrResult/bank-statement-header-transaction';
import { FormArray, FormBuilder,  FormGroup,  } from '@angular/forms';
import { GetCalculatedListBankStatementSummaryRequest } from 'app/model/api/OcrResult/get-calculated-list-bank-statement-summary.request';

@Component({
  selector: 'app-transaction-summary-ocr-result',
  templateUrl: './transaction-summary-ocr-result.component.html',
  styleUrls: ['./transaction-summary-ocr-result.component.scss']
})
export class TransactionSummaryOcrResultComponent implements OnInit {
  @Input() state: any;
  @Output() onClickInput = new EventEmitter<any>();

  formObj: FormModel<any>; 
  msxForm: FormGroup; 
  dashboardName: string;
  period: string = '';
  summaries: any[] = [];
  listSummary: any[] = [];
  summariesCombined: any[] = [];

  swal = swalFunction;  

  constructor(
    private global: GlobalService, 
    public http: HttpService, 
    private dashboardService: DashboardService,
    private readonly fb: FormBuilder,) {  
    }

  async ngOnInit() {   
    this.msxForm = this.fb.group({
      summary: this.fb.array([]),
    });   

    const request = new GetCalculatedListBankStatementSummaryRequest();
    request.tenantCode = this.global.user.role.tenantCode;
    request.fileSourcePath = this.state.fileSourcePath; 
    request.dashboardName = this.state.dashboardName;
    await this.getBankStatementHeaderTransactionSummary(request);  
    await this.getCalculatedListBankStatementSummary(request);
    
    this.dashboardService.functionCall$.subscribe(async (param) => { 
      this.period = param.date;  
      if(this.period !== ''){
        this.summariesCombined.forEach(element => { 
          if(this.period === element.period){
            param.request.period = element.period;
            param.request.openingBalance = element.beginningBalanceOcr;
          }
        }); 
      };
      await this.getCalculatedListBankStatementSummary(param.request);
    }); 
  }
 
  
  get summaryArray(): FormArray {
    const sumArray = this.msxForm.get('summary') as FormArray;
    return sumArray;
  } 
 

  async getBankStatementHeaderTransactionSummary(request:GetCalculatedListBankStatementSummaryRequest){   
    await this.dashboardService.getBankStatementHeaderTransactionSummary(request).toPromise().then(
      async (response) => {
         if (response['status']['code'] == 0) { 
          this.summaries = response['summaries'];   
         }
      }
    )
  } 

  async getCalculatedListBankStatementSummary(request:GetCalculatedListBankStatementSummaryRequest){   
    await this.dashboardService.getCalculatedListBankStatementSummary(request).toPromise().then(
      async (response) => {
         if (response['status']['code'] == 0) {    
          this.listSummary = response['listSummary'];
           if(this.period === ''){  
              this.summariesCombined = this.combineLists(this.summaries, this.listSummary);
           } else{ 
             this.updateCombinedLists();
           }
         }
      }
    )
  } 

  combineLists(summaries: any[], listSummary: any[]): any[] {
    return summaries.map(summary => {
      const matchingListSummary = listSummary.find(item => item.period === summary.period);
  
      return {
        period: summary.period,
        beginningBalanceOcr: summary.beginningBalance,
        endingBalanceOcr: summary.endingBalance,
        endingBalanceHistory: matchingListSummary ? parseFloat(matchingListSummary.endingBalance) : null
      };
    });
  } 

  updateCombinedLists() {
    this.summariesCombined.forEach(element => { 
      const matchingListSummary = this.listSummary.find(item => item.period === element.period);
      element.endingBalanceHistory = matchingListSummary ? parseFloat(matchingListSummary.endingBalance) : element.endingBalanceHistory
    }); 
  } 

  formatPeriod(period: string): string {
    const [year, month] = period.split('-');
    const date = new Date(+year, +month - 1); // Month is zero-based
    return date.toLocaleString('en-US', { month: 'short', year: 'numeric' }).toUpperCase();
  }

  formatNumber(value: number): string {
    return value.toLocaleString();
  } 
  
  getCssClass(summary: any): { [key: string]: boolean } {
    const endingBalanceOcr = parseFloat(summary.endingBalanceOcr);
    const endingBalanceHistory = parseFloat(summary.endingBalanceHistory); 
    return {
      'text-success': endingBalanceOcr == endingBalanceHistory,
      'text-danger': endingBalanceOcr != endingBalanceHistory
    };
  }  

}
