import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { DashboardService } from 'app/services/api/dashboard.service';
import { HttpService } from 'app/services/http.service';
import { FormModel } from 'app/shared/components/ms-form/models';
import { GlobalService } from 'app/shared/data/global.service';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject } from 'rxjs';
import { CircularTransactionTable } from './view-transaction-circular.list.view';
import { PathConstant } from 'app/shared/constant/PathConstant';
import * as swalFunction from 'app/shared/data/sweet-alerts'; 
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { QuestionTextbox } from 'app/shared/components/ms-form/questions';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { Page } from 'app/shared/components/msx-datatable/models/page';

@Component({
  selector: 'app-view-transaction-circular',
  templateUrl: './view-transaction-circular.component.html',
  styleUrls: ['./view-transaction-circular.component.scss']
})
export class ViewTransactionCircularComponent implements OnInit {
  table = CircularTransactionTable;
  datasource: BehaviorSubject<any> = new BehaviorSubject<any>([]);
  formObj: FormModel<any>;
  msxForm: FormGroup;
  state: any;
  dashboardName: string; 
  swal = swalFunction;

  title: string = CommonConstant.CIRCULAR_TRANSACTION_TITLE + ' - ' + CommonConstant.VIEW_GROUP_TRANSACTION;

  constructor(
      private global: GlobalService,
      private router: Router,
      public http: HttpService,
      private ngZone: NgZone,
      private cdr: ChangeDetectorRef,
      private toastrService: ToastrService,
      private dashboardService: DashboardService,
      private readonly formBuilder: FormBuilder,) { 
    this.state = this.router.getCurrentNavigation().extras?.state; 
  }

  async ngOnInit(): Promise<void> {
    if(!this.state){
          this.router.navigate([PathConstant.DASHBOARD]);
    }
    this.dashboardName = this.state.dashboardName;

    this.msxForm = this.formBuilder.group({
       groupName: [this.state.groupName || '', [Validators.required]],
       transactionWindow: [this.state.transactionWindow || '', [Validators.required]],
    });
    this.setupQuestion();

    //table
    await this.getListViewTransactionCircular().then(() => {
      this.ngZone.run(() => {
        this.cdr.markForCheck();
      })
    });
  }

  async getPage(page: Page) {
    console.log('requestPage', page);
    await this.getListViewTransactionCircular(page.pageNumber + 1).then(() => {
      this.ngZone.run(() => {
        this.cdr.markForCheck();
      })
    });
  }

  async getListViewTransactionCircular(pageNumber: number = 1) {
    const request = { 
      groupName: this.state.groupName,
      dashboardName: this.state.dashboardName,
      tenantCode: this.global.user.role.tenantCode,
      page: pageNumber
    };
    await this.dashboardService.getListViewTransactionCircular(request).toPromise().then(
      (response) => {
        if (response['status']['code'] == 0) {
          this.datasource.next(response);
        }
        this.cdr.detectChanges();
      }
    )
  }

  goBack() { 
    delete this.state.groupName;
    delete this.state.transactionWindow;
    this.router.navigate([PathConstant.DASHBOARD_DETAIL], { 
      state: { 
        state: this.state, 
        indexTab: CommonConstant.CIRCULAR_TAB_INDEX
      }
    });
  }

  setupQuestion() { 
    this.formObj = {
      name: 'groupNameForm',
      mode: CommonConstant.MODE_EDIT,
      colSize: 6,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        new QuestionTextbox({
          key: 'groupName',
            label: 'Group Name',
            placeholder: 'Type group name here',
            maxLength: 64,
            required: true,
            readonly: true,
            validations: [
              {type: 'required', message: 'Group Name cannot be empty'},
              {type: 'maxlength', message: 'The maximum number of characters for the Group Name Name is 64'}
            ]
        }),
        new QuestionTextbox({
          key: 'transactionWindow',
            label: 'Transaction Window',
            placeholder: 'Type Transaction Window here',
            maxLength: 64,
            required: true,
            readonly: true,
            validations: [
              {type: 'required', message: 'Transaction Window cannot be empty'},
              {type: 'maxlength', message: 'The maximum number of characters for the Transaction Window is 64'}
            ]
        }),
      ],
      params: []
    }
  }  

  getQuestionkey(key:string) {
    return this.formObj.components.find(x => x.key === key);
  }

  deleteData() {
    this.swal.Confirm('Are you sure? This data will be permanently lost.').then(
      async (result) => {
        if (result.isConfirmed) { 

          const request = { 
            groupName: this.state.groupName,
            tenantCode: this.global.user.role.tenantCode,
            dashboardName: this.state.dashboardName
          };

          this.dashboardService.deleteCircular(request).subscribe(response => {
            if (response.status.code !== 0) {
              return;
            }

            this.toastrService.success('Data successfully deleted!', null, {
              positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
            });

            this.goBack();
            this.cdr.detectChanges();
          })
        }
      }
    );
  }
}
