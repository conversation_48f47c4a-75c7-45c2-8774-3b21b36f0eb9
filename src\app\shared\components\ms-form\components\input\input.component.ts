import {AfterViewInit, Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {ComponentModel} from '../../models/component.model';
import {FormGroup} from '@angular/forms';
import {QuestionBase} from '../../questions/question-base';
import {FormConstant} from '../../constants/form.constant';

@Component({
  selector: 'app-input',
  templateUrl: './input.component.html',
  styleUrls: ['./input.component.scss']
})
export class InputComponent implements OnInit, AfterViewInit {
  @Input()
  question: QuestionBase<any>;

  @Input()
  form: FormGroup;

  @Input()
  direction: string;

  @Input()
  validations: {type: string, message: string}[];

  @Output()
  inputX: EventEmitter<any> = new EventEmitter<any>();

  @Output() 
  focus: EventEmitter<void> = new EventEmitter<void>();  // EventEmitter for focus
  
  isVerticalLayout: boolean;

  constructor() {
    this.checkLayoutDirection();
  }

  ngOnInit(): void {
    this.checkLayoutDirection();
  }

  ngAfterViewInit(): void {
    const element = document.getElementById(this.question.key);

    if (this.question.maxLength) {
      element.setAttribute('maxlength', this.question.maxLength.toString());
    }

    if (this.question.minLength) {
      element.setAttribute('minlength', this.question.maxLength.toString());
    }
  }

  checkLayoutDirection() {
    this.isVerticalLayout = this.direction === FormConstant.DIRECTION_VERTICAL;
  }

  initiateValidator() {
    this.validations = this.validations || this.question['validations'];
    console.log('validates', this.validations);
  }

  onInput($event) {
    this.inputX.emit({question: this.question, data: $event.target.value});
  }

  onFocus() {
    this.focus.emit();  // Emit the focus event
  }

}
