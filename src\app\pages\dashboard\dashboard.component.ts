import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from '@angular/core';
import { Button } from 'app/shared/components/msx-view/models/Button';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { DashboardListView } from './dashboard.template-list.view';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { UploadComponent } from './upload/upload.component';
import { Router } from '@angular/router';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { Dashboard } from 'app/model/dashboard';
import { PlatformLocation } from '@angular/common';
import { ConfigService } from 'app/shared/services/config.service';
import { DashboardService } from 'app/services/api/dashboard.service';
import { ToastrService } from 'ngx-toastr';
import * as swalFunction from 'app/shared/data/sweet-alerts';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { GlobalService } from 'app/shared/data/global.service';
import { MsxPagingComponent } from 'app/shared/components/msx-view/msx-paging.component';
@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  config: any = {};
  view: MsxView;
  buttonList: Button[];
  serviceUrl = URLConstant.ListDashboard;
  popStateListener: any;

  @ViewChild(MsxPagingComponent) msxPagingComponent!: MsxPagingComponent;
 
  swal = swalFunction; 

  constructor(
    private modalService: NgbModal, 
    private router: Router,  
    private dashboardService: DashboardService,
    private toastrService: ToastrService,
    private global: GlobalService,
    private cdr: ChangeDetectorRef,
    private configService: ConfigService,) { }

  async ngOnInit(): Promise<void> {
    await this.toggleSidebar();

    this.view = DashboardListView;
    this.buttonList = [
      {name: 'Add New Dashboard', class: 'btn btn-primary', hide: false, icon: 'ft-plus'}
    ];
  }
 
 
  onBtnClickListener($event) {
    const buttonName = $event['name'];
    if (buttonName !== 'Add New Dashboard') {
      return;
    }
    this.openAddModal();
  }

  onItemClickListener(event) {
    const data = event['data'];
    switch (event['act']['type']) {
      case Act.Edit:
        return this.gotoDashboardDetail(data, false);
      case Act.View:
        return this.gotoDashboardDetail(data, true);
      case Act.Delete:
        return this.deleteData(data);
    }
  }

  gotoDashboardDetail(data: Dashboard, isView:boolean) { 
    this.router.navigate([PathConstant.DASHBOARD_DETAIL], { 
      state: { 
        state: data, 
        indexTab: 0,
        isView: isView,
      }
    });
  }

  openAddModal() {
    const modal = this.modalService.open(UploadComponent, {
      centered: true,
      backdrop: 'static',
      size: 'xl'
    });
    modal.componentInstance.isBankStatement = false; 
    modal.componentInstance.result.subscribe(() => {
      this.msxPagingComponent.refreshSearch(); // Refresh data 
    });
  }
 
  deleteData(data: Dashboard) {
    this.swal.Confirm('Are you sure? This dashboard will be permanently lost.').then(
      async (result) => {
        if (result.isConfirmed) {
          const request = {  
            tenantCode: this.global.user.role.tenantCode,
            dashboardName: data.dashboardName
          };
          await this.dashboardService.deletedDashboard(request).toPromise().then(
            async (response) => {
               if (response['status']['code'] == 0) {
                this.toastrService.success('Dashboard successfully deleted!', null, {
                  positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
                });
                //await this.getListNonBusinessTransactionGroup(); // Refresh data 
                this.msxPagingComponent.refreshSearch(); 
                this.cdr.detectChanges();
               }
            }
          )
        }
      }
    );
  }
  

  async toggleSidebar() {
    this.configService.templateConf$.subscribe((templateConf) => {
      if (templateConf) {
        this.config = templateConf;
      } 
    });
    const conf = this.config;
    conf.layout.sidebar.collapsed = false;
    this.configService.applyTemplateConfigChange({ layout: conf.layout });

    setTimeout(() => {
      this.fireRefreshEventOnWindow();
    }, 300);
  }

  fireRefreshEventOnWindow = function () {
    const evt = document.createEvent('HTMLEvents');
    evt.initEvent('resize', true, false);
    window.dispatchEvent(evt);
  };

}
