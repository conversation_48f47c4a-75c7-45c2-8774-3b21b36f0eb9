import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core'; 
import { Dashboard } from 'app/model/dashboard';
import { DashboardService } from 'app/services/api/dashboard.service';
import { GlobalService } from 'app/shared/data/global.service';
import { InsightHeader } from 'app/model/insight-header';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { UtilsService } from 'app/services/api/utils.service';
import { twoBarOneLineChartColors} from 'app/shared/charts/charts-colors';
import { ChartsService } from 'app/services/api/charts.service';
@Component({
  selector: 'app-supplier-buyer-group-insight',
  templateUrl: './supplier-buyer-group-insight.component.html',
  styleUrls: ['./supplier-buyer-group-insight.component.scss']
})
export class SupplierBuyerGroupInsightComponent implements OnInit {
  
  @Input() stateDashboard: Dashboard;
  insightCard: InsightHeader = {
    supplierAmount: 0,
    buyerAmount: 0,
    supplierTrxCount: 0,
    buyerTrxCount: 0,
  };

  //icons
  iconSupplierAmount:string = CommonConstant.ICON_GREEN_DOLLAR_SIGN_IN;
  iconBuyerAmount:string = CommonConstant.ICON_YELLOW_DOLLAR_SIGN_OUT;
  iconSupplierTrxCount:string = CommonConstant.ICON_NAVY_DOC_IN;
  iconBuyerTrxCount:string = CommonConstant.ICON_EMERALD_DOC_OUT; 
  
  MonthChartLabels:any[] = [];

  //Monthly Supplier & Buyer Cash Flow Analysis 
  cashFlowChartType = CommonConstant.BAR_CHART_TYPE; 
  cashFlowChartColors = twoBarOneLineChartColors; 
  cashflowChartOptions:any = {}; 
  cashFlowChartData:any[];
  cashFlowDetails: any = {}; 

  //Monthly Net Cash & Growth Rate Representation
  growthRateChartType = CommonConstant.LINE_CHART_TYPE; 
  growthRateChartData:any[]; 
  growthRateChartOptions:any = {}; 
  growthRateChartColors: any[]; 

  isHide : boolean = true;

  constructor(private global: GlobalService, 
      private dashboardService: DashboardService,  
      private chartsService: ChartsService,
      private utilsService: UtilsService,  
      private cdr: ChangeDetectorRef,) { 

    //setup chart colors
    this.growthRateChartColors = this.chartsService.getTwoLineChartColors({
      0: {yAxisID:CommonConstant.LEFT},  
      1: {yAxisID:CommonConstant.RIGHT, borderDash: [5, 5] }  
    });
  }

  async ngOnInit(): Promise<void> { 
    await this.supplierBuyerInsightHeader();
    await this.supplierBuyerMonthlyCashflowAndGrowthRate();  
    this.setupCashflowChartOptions();
    this.setupGrowthRateChartOptions(); 
  }
  
  setupCashflowChartOptions(){ 
    const cashFlowDetailsData =  this.cashFlowDetails;
    this.cashflowChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        datalabels: {
          display: false,
          color: '#000', // Label text color
          align: 'top', // Align labels above the data points
          anchor: 'end', // Anchor labels to the end of the data point
          formatter: (value: number) => value.toFixed(2), // Format values as needed
          font: {
            size: 10,
            weight: 'bold'
          }
        }
      },
      scales: {
        xAxes: [{ 
          stacked: true ,
          scaleLabel: {
            display: true,
            labelString: 'Year-Month'
          }
        }],
        yAxes: [{
          beginAtZero: true,
          ticks: {
            callback: function (value: number) {
              return UtilsService.abbreviateNumber(value); // Display absolute values on Y-axis
            } 
          },
          scaleLabel: {
            display: true,
            labelString: 'Buyers/Suppliers (IDR)',
            fontColor: 'black'
          }, 
        }]
      },
      legend: {
        display: true,
        position: 'top', 
        padding: 20,
        labels: {
          borderRadius: 10,
        }
      },
      tooltips: {
        callbacks: {
          title: function (tooltipItems: any) {
            // Get the label of the hovered bar
            const label = tooltipItems[0].xLabel;
            return `${label}`;
          },
          beforeLabel: function (tooltipItem: any) {
            // Get additional data for the hovered period
            const label = tooltipItem.xLabel;
            const data = cashFlowDetailsData[label];
    
            // Format and return detailed information
            return [
              `Total Supplier Amount                        : ${data.totalSupplierAmount}`,
              `Avg Supplier Amount                          : ${data.avgSupplierAmount}`,
              `No. of Supplier Transactions              : ${data.noOfSupplierTransactions}`,
              `Freq (days) of Supplier Transactions : ${data.freqDaysSupplier}`,
              `Total Buyer Amount                            : ${data.totalBuyerAmount}`,
              `Avg Buyer Amount                             : ${data.avgBuyerAmount}`,
              `No. of Buyer Transactions                  : ${data.noOfBuyerTransactions}`,
              `Freq (days) of Buyer Transactions     : ${data.freqDaysBuyer}`,
              `Net Cash                                            : ${data.netCash}`,
              `Buyer to Supplier Ratio                      : ${data.buyerToSupplierRatio}`
            ];
          },
          label: function (tooltipItem: any) {
            return ''; // Return an empty label to suppress the default value
          }
        }
      },
      title: {
        display: true,
        fontColor: 'black',   
        fontSize: 14,   
        fontStyle: 'bold',  
        text: 'Monthly Supplier & Buyer Cash Flow Analysis'
      },
    };
  }

  setupGrowthRateChartOptions(){
    const growthRateChartData =  this.growthRateChartData;
    this.growthRateChartOptions = {
      animation: {
        duration: 1000, // general animation time
        easing: 'easeOutBack'
      },
      hover: {
        animationDuration: 1000, // duration of animations when hovering an item
        mode: 'label'
      },
      responsiveAnimationDuration: 1000, // animation duration after a resize
      responsive: true,
      maintainAspectRatio: false,
      legend: {
        position: 'top',
      },
      plugins: {
        datalabels: {
          display: false, 
        }
      },
      scales: {
        xAxes: [{
          display: true,
          scaleLabel: {
            display: true,
            labelString: 'Year-Month'
          }
        }],
        yAxes: [
          {
            id: CommonConstant.LEFT, // Left Y-axis
            type: 'linear',
            position: CommonConstant.LEFT,
            scaleLabel: {
              display: true,
              labelString: 'Net Cash (IDR)',
              fontColor: '#F5B700'
            },
            ticks: {
              fontColor: '#F5B700',
              callback: function (value: number) {
                return UtilsService.abbreviateNumber(value); // Display absolute values on Y-axis
              } 
            },
          },
          {
            id: CommonConstant.RIGHT, // Right Y-axis
            type: 'linear',
            position: CommonConstant.RIGHT,
            scaleLabel: {
              display: true,
              labelString: 'Growth Rate (%)',
              fontColor: '#FE6565'
            },
            ticks: {
              fontColor: '#FE6565' 
            }
          }
        ]
      },
      title: {
        display: true,
        fontColor: 'black',   
        fontSize: 14,   
        fontStyle: 'bold',  
        text: 'Monthly Net Cash & Growth Rate Representation'
      },
      tooltips: {
        callbacks: {
          title: function (tooltipItems: any) {
            const label = tooltipItems[0].xLabel;
            return `${label}`;
          },
          beforeLabel: function (tooltipItem: any) {
            const datasetIndex = tooltipItem.datasetIndex; // Get which dataset is being hovered
            const index = tooltipItem.index; // Get the index of the data point
            
            const dataset = growthRateChartData[datasetIndex]; // Get the dataset based on datasetIndex
            const data = dataset.data[index]; // Get the value for the specific data point using the index
            
            const tooltipText = [];
            
            // Tooltip for Net Cash (IDR) - left Y-axis
            if (datasetIndex === 0) {
              const netCash = data.toLocaleString(); // Format with thousands separator
              tooltipText.push(`Net Cash (IDR): ${netCash}`);
            }
            
            // Tooltip for Growth Rate - right Y-axis
            if (datasetIndex === 1) {
              const growthRate = data.toFixed(2); // Format Growth Rate to 2 decimal places
              tooltipText.push(`Growth Rate: ${growthRate}%`);
            }
            
            return tooltipText;
          },
          label: function (tooltipItem: any) {
            return ''; // Suppress default tooltip value
          }
        }
      }
      
    };
  };

  async supplierBuyerInsightHeader(){
    const request = {
      tenantCode : this.global.user.role.tenantCode,
      dashboardName : this.stateDashboard.dashboardName,
    };
    await this.dashboardService.supplierBuyerInsightHeader(request).toPromise().then(
      (response) => {
        if (response['status']['code'] == 0) {
           this.insightCard.supplierAmount = response.supplierAmount; 
           this.insightCard.buyerAmount = response.buyerAmount; 
           this.insightCard.supplierTrxCount = response.supplierTrxCount; 
           this.insightCard.buyerTrxCount = response.buyerTrxCount; 
        }
      }
    )
  }

  async supplierBuyerMonthlyCashflowAndGrowthRate(){
    const request = {
      tenantCode : this.global.user.role.tenantCode,
      dashboardName : this.stateDashboard.dashboardName,
    };
    await this.dashboardService.supplierBuyerMonthlyCashflowAndGrowthRate(request).toPromise().then(
      (response) => {
        if (response['status']['code'] == 0) {
           this.MonthChartLabels = response.labels;
           this.cashFlowChartData = response.cashFlow;
           this.growthRateChartData = response.growthRate;
           this.mapDataToAdditionalData(response.cashFlowDetails);
        }
      }
    )
  } 

  mapDataToAdditionalData(cashFlowDetails:any) {
    cashFlowDetails.forEach(item => {
      this.cashFlowDetails[item.period] = {
        totalSupplierAmount: this.formatCurrency(item.totalSupplierAmount),
        avgSupplierAmount: this.formatCurrency(item.avgSupplierAmount),
        noOfSupplierTransactions: item.noOfSupplierTrx,
        freqDaysSupplier: item.freqDaysOfSupplierTrx,
        totalBuyerAmount: this.formatCurrency(item.totalBuyerAmount),
        avgBuyerAmount: this.formatCurrency(item.avgBuyerAmount),
        noOfBuyerTransactions: item.noOfBuyerTrx,
        freqDaysBuyer: item.freqDaysOfBuyerTrx,
        netCash: this.formatCurrency(item.netCash),
        buyerToSupplierRatio: item.buyerSupplierRatio
      };
    }); 
  }

  // Utility method to format currency
  formatCurrency(amount: number): string {
    return amount.toLocaleString('en-US', { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    });
  }

  chartClicked(e: any): void {
    //your code here
  }

  chartHovered(e: any): void {
    //your code here
  }
}
