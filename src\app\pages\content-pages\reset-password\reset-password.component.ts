import { Component, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { UserService } from 'app/services/api/user.service';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { ToastrService } from 'ngx-toastr';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { ResetPasswordRequest } from 'app/model/api/User/reset-password.request';
import swal from 'sweetalert2';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss']
})
export class ResetPasswordComponent implements OnInit {

  formObj: FormModel<any>;
  xForm: FormGroup;
  
  // Data from forgot password page
  state: any;
  loginId: string;
  otp: string;

  currentDate = new Date();

  constructor(
    private router: Router,
    private toastService: ToastrService,
    private userService: UserService
  ) {
    this.state = this.router.getCurrentNavigation().extras.state;
    this.loginId = this.state?.loginId;
    this.otp = this.state?.otp;
  }

  ngOnInit(): void {
    this.initView();
  }

  initView() {
    if (!this.loginId || !this.otp) {
      this.router.navigate([PathConstant.FORGOT_PASSWORD_PAGE]);
    }

    this.formObj = {
      name: 'resetPassword',
      colSize: 12,
      direction: FormConstant.DIRECTION_VERTICAL,
      mode: 'Edit',
      components: [
        {
          key: 'newPassword',
          label: 'New Password',
          placeholder: 'Type your new password here',
          controlType: FormConstant.TYPE_PASSWORD,
          icon: 'ft-lock',
          required: true
        },
        {
          key: 'confirmNewPassword',
          label: 'Confirm New Password',
          placeholder: 'Confirm your new password here',
          controlType: FormConstant.TYPE_PASSWORD,
          icon: 'ft-lock',
          required: true
        }
      ],
      params: [
      ]
    }
  }

  onForm(form: FormGroup) {
    this.xForm = form;
  }

  onNext(data: any) {
    if (!data['newPassword']) {
      return;
    }

    if (data['newPassword'] !== data['confirmNewPassword']) {
      this.toastService.error('The new password and confirmation password do not match. Please try again.', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
      return;
    }
    
    const request = new ResetPasswordRequest();
    request.loginId = this.loginId;
    request.otp = this.otp;
    request.newPassword = data['newPassword'];
    request.confirmNewPassword = data['confirmNewPassword'];
    request.audit = {
      callerId: this.loginId,
      locale: 'en'
    };

    this.userService.resetPassword(request).subscribe(response => {
      if (response.status.code !== 0) {
        return;
      }

      swal.fire({
        text: 'Your password has been successfully changed!',
        icon: 'success',
        showConfirmButton: true,
        confirmButtonText: 'OK'
      });

      this.router.navigate([PathConstant.LOGIN]);

    });
  }

}
