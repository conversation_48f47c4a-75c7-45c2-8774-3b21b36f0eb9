import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { Dashboard } from 'app/model/dashboard';
import { InsightGeneralBody } from 'app/model/insight-general-body';
import { DashboardService } from 'app/services/api/dashboard.service';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { GlobalService } from 'app/shared/data/global.service';
import { UtilsService } from 'app/services/api/utils.service';
import { InsightsService } from 'app/services/api/insights.service';
import { twoBarTwoLineChartColors, twoLineChartColors } from 'app/shared/charts/charts-colors';
import { ChartsService } from 'app/services/api/charts.service';
 @Component({
  selector: 'app-general-ending-balance-daily-cash-flow-analysis',
  templateUrl: './general-ending-balance-daily-cash-flow-analysis.component.html',
  styleUrls: ['./general-ending-balance-daily-cash-flow-analysis.component.scss']
})
export class GeneralEndingBalanceDailyCashFlowAnalysisComponent implements OnInit {
  @Input() stateDashboard: Dashboard; 

  insightCard: InsightGeneralBody = { 
    highestBalance: 0,
    highestBalanceDate: '',
    lowestBalance:0,
    lowestBalanceDate: '',
    monthlyAverageBalance: 0,
    dailyAverageBalance: 0,
    growthRate: 0,
  };  
  
  DailyChartLabels:any[] = [];

  //Daily Cash Flow Analysis
  cashFlowChartType = CommonConstant.BAR_CHART_TYPE; 
  cashFlowChartColors = twoBarTwoLineChartColors; 
  cashflowChartOptions:any = {}; 
  cashFlowChartData:any[];
  cashFlowDetails: any = {};   

  //Ending Balance Analysis
  endingBalanceChartType = CommonConstant.LINE_CHART_TYPE; 
  endingBalanceChartData:any[]; 
  endingBalanceChartOptions:any = {}; 
  endingBalanceChartColors:any[]; 
  endingBalanceDetails: any = {}; 

  monthStep: number = 1;

  constructor(private global: GlobalService, private utilsService: UtilsService,
      private insightService: InsightsService,  private chartsService: ChartsService,
      private dashboardService: DashboardService, private cdr: ChangeDetectorRef,) { 
  this.endingBalanceChartColors = this.chartsService.getTwoLineChartColors({
      0: {borderColor:CommonConstant.NAVY_COLOR, pointBackgroundColor:CommonConstant.NAVY_COLOR,},  
      1: {borderDash: [5, 5] }  
  });
  
  }
  

  async ngOnInit(): Promise<void> {
    await this.generalInsightBodyCard();
    await this.generalDailyAnalysis();

    this.setupCashflowChartOptions();
    this.setupEndingBalanceChartOptions();

    this.cdr.detectChanges();
  }

  async generalInsightBodyCard(){
    const request = {
      tenantCode : this.global.user.role.tenantCode,
      dashboardName : this.stateDashboard.dashboardName,
    };
    await this.dashboardService.generalInsightBodyCard(request).toPromise().then(
      (response) => {
        if (response['status']['code'] == 0) { 
          this.insightCard.highestBalance = response.higestBalance;
          this.insightCard.highestBalanceDate = response.highestBalanceDate;
          this.insightCard.lowestBalance = response.lowestBalance;
          this.insightCard.lowestBalanceDate = response.lowestBalanceDate;
          this.insightCard.monthlyAverageBalance = response.monthlyAverageBalance;
          this.insightCard.dailyAverageBalance = response.dailyAverageBalance;
          this.insightCard.growthRate = response.growthRate;
        }
      }
    )
  }

  async generalDailyAnalysis(){
    const request = {
      tenantCode : this.global.user.role.tenantCode,
      dashboardName : this.stateDashboard.dashboardName,
    };
    await this.dashboardService.generalDailyAnalysis(request).toPromise().then(
      (response) => {
        if (response['status']['code'] == 0) {  
          this.DailyChartLabels = response.labels;
          this.calculateMonthStep();
          //daily cashflow chart
          this.cashFlowChartData = response.cashFlow;
          this.mapDataToAdditionalCashFlowData(response.cashFlowDetails);

          //ending balance chart
          this.endingBalanceChartData = response.endingBalance;
          this.mapDataToAdditionalEndingBalanceData(response.endingBalanceDetails);
        }
      }
    )
  }

  calculateMonthStep(){
    const months = new Set(this.DailyChartLabels.map(date => date.substring(0, 7)));
    this.monthStep = (() => {
      return months.size >= 24 ? 6 :
             months.size >= 12 ? 3 :
             months.size >= 6  ? 2 : 1;
    })();

  }
  
  setupCashflowChartOptions(){  
    const cashFlowDetailsData =  this.cashFlowDetails;

    // Separate data for left and right axes
    const rightAxisData = this.cashFlowChartData.filter(d => d.label === "Net Cash" || d.label === "Ending Balance");
    const leftAxisData = this.cashFlowChartData.filter(d => d.label === "Cash In" || d.label === "Cash Out");

    const rightMaxValue = this.insightService.getMaxAbsoluteRoundedValue(rightAxisData);
    const leftMaxValue = this.insightService.getMaxAbsoluteRoundedValue(leftAxisData);

    this.cashflowChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        datalabels: {
          display: false,
          color: '#000', // Label text color
          align: 'top', // Align labels above the data points
          anchor: 'end', // Anchor labels to the end of the data point
          formatter: (value: number) => value.toFixed(2), // Format values as needed
          font: {
            size: 10,
            weight: 'bold'
          }
        }
      },
      scales: {
        xAxes: [{ 
          stacked: true ,
          scaleLabel: {
            display: true,
            labelString: 'Date'
          },
          type: 'time',
          time: {
             unit: 'month',       // Display labels at the month level
             stepSize: this.monthStep,    
             displayFormats: {
             month: 'YYYY-MM'   
          }
          },
          ticks: {
             autoSkip: false,     // Ensure labels appear every 3 months 
          }, 
        }],
        yAxes: [
          {
          id: CommonConstant.LEFT,
          position: CommonConstant.LEFT,
          beginAtZero: true,
          gridLines: {
            drawOnChartArea: true, 
          },
          ticks: {
            min:-leftMaxValue,
            max: leftMaxValue,
            alignToPixels: true,
            callback: function (value: number) {
              return UtilsService.abbreviateNumber(value); // Display absolute values on Y-axis
            } 
          },
          scaleLabel: {
            display: true,
            labelString: 'Cash In/Out (IDR)',
            fontColor: 'black'
          }, 
        },
        { 
          id: CommonConstant.RIGHT,
          position: CommonConstant.RIGHT,
          beginAtZero: true,
          scaleLabel: {
            display: true,
            labelString: 'Balance (IDR)',
            fontColor: 'black'
          },
          gridLines: {
            drawOnChartArea: false, 
          },
          ticks: {
            min:-rightMaxValue,
            max: rightMaxValue,
            alignToPixels: true,
            callback: function (value: number) {
              return UtilsService.abbreviateNumber(value); // Display absolute values on Y-axis
            } 
          },
        } ]
      },
      legend: {
        display: true,
        position: CommonConstant.TOP, 
        padding: 20,
        labels: {
          usePointStyle: true,
          borderRadius: 10,
        }
      },
      tooltips: {
        callbacks: {
          title: function (tooltipItems: any) {
            // Get the label of the hovered bar
            const label = tooltipItems[0].xLabel;
            return `${label}`;
          },
          beforeLabel: function (tooltipItem: any) {
            // Get additional data for the hovered period
            const label = tooltipItem.xLabel;
            const data = cashFlowDetailsData[label];
    
            // Format and return detailed information
            return [
              `Total Credit Amount         : ${data.totalCreditAmount}`,
              `No.of Credit Transaction : ${data.totalCreditCount}`,
              `Total Debit Amount          : ${data.totalDebitAmount}`,
              `No.of Debit Transaction  : ${data.totalDebitCount}`, 
              `Net Cash                         : ${data.netCash}`,
              `Ending Balance               : ${data.endingBalance}`
            ];
          },
          label: function (tooltipItem: any) {
            return ''; // Return an empty label to suppress the default value
          }
        }
      },
      title: {
        display: true,
        fontColor: 'black',   
        fontSize: 14,   
        fontStyle: 'bold',  
        text: 'Daily Cash Flow Analysis'
      },
    };
  }

  setupEndingBalanceChartOptions(){
    const endingBalanceChartData =  this.endingBalanceDetails; 
    this.endingBalanceChartOptions = {
      animation: {
        duration: 1000, // general animation time
        easing: 'easeOutBack'
      },
      hover: {
        animationDuration: 1000, // duration of animations when hovering an item
        mode: 'label'
      },
      responsiveAnimationDuration: 1000, // animation duration after a resize
      responsive: true,
      maintainAspectRatio: false,
      legend: {
        display: true,
        position: CommonConstant.TOP, 
        padding: 20,
        labels: {
          usePointStyle: true,
          borderRadius: 10,
        }
      },
      plugins: {
        datalabels: {
          display: false, 
        }
      },
      scales: {
        xAxes: [{
          display: true,
          scaleLabel: {
            display: true,
            labelString: 'Date'
          },
          type: 'time',
          time: {
             unit: 'month',       // Display labels at the month level
             stepSize: this.monthStep,     
             displayFormats: {
             month: 'YYYY-MM'  
          }
          },
          ticks: {
             autoSkip: false,     // Ensure labels appear every 3 months 
          }, 
        }],
        yAxes: [
          {
            id: CommonConstant.LEFT, // Left Y-axis
            type: 'linear',
            position: 'left',
            scaleLabel: {
              display: true,
              labelString: 'Balance (IDR)',
              fontColor: 'black'
            },
            ticks: {
              fontColor: 'black',
              callback: function (value: number) {
                return UtilsService.abbreviateNumber(value); // Display absolute values on Y-axis
              } 
            },
          } 
        ]
      },
      title: {
        display: true,
        fontColor: 'black',   
        fontSize: 14,   
        fontStyle: 'bold',  
        text: 'Ending Balance Analysis'
      },
      tooltips: {
        callbacks: {
          title: function (tooltipItems: any) {
            // Get the label of the hovered bar
            const label = tooltipItems[0].xLabel;
            return `${label}`;
          },
          beforeLabel: function (tooltipItem: any) {
            // Get additional data for the hovered period
            const label = tooltipItem.xLabel;
            const data = endingBalanceChartData[label];
    
            // Format and return detailed information
            return [
              `Ending Balance               : ${data.endingBalance}`,
              `7 Days Moving Average  : ${data.sevenDaysMovingAvg}`
            ];
          },
          label: function (tooltipItem: any) {
            return ''; // Return an empty label to suppress the default value
          }
        }
      }
      
    };
  };

  mapDataToAdditionalCashFlowData(cashFlowDetails:any) {
    cashFlowDetails.forEach(item => {
      this.cashFlowDetails[item.date] = {
        totalCreditAmount: this.insightService.formatCurrency(item.totalCreditAmount), 
        totalCreditCount: this.insightService.formatNumber(item.totalCreditCount), 
        totalDebitAmount: this.insightService.formatCurrency(item.totalDebitAmount),  
        totalDebitCount: this.insightService.formatNumber(item.totalDebitCount),
        netCash: this.insightService.formatCurrency(item.netCash),
        endingBalance: this.insightService.formatCurrency(item.endingBalance),
      };
    }); 
  } 

  mapDataToAdditionalEndingBalanceData(endingBalanceDetails:any) {
    endingBalanceDetails.forEach(item => {
      this.endingBalanceDetails[item.date] = { 
        endingBalance: this.insightService.formatCurrency(item.endingBalance),
        sevenDaysMovingAvg: this.insightService.formatCurrency(item.sevenDaysMovingAvg),
      };
    }); 
  } 

  chartClicked(e: any): void {
    //your code here
  }

  chartHovered(e: any): void {
    //your code here
  }
}
