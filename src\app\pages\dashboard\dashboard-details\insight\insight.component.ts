import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { Dashboard } from 'app/model/dashboard';
import { DashboardService } from 'app/services/api/dashboard.service';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { GlobalService } from 'app/shared/data/global.service';
import { ToastrService } from 'ngx-toastr';
import * as swalFunction from 'app/shared/data/sweet-alerts';
import { Router } from '@angular/router';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ConsolidatedBankStatementComponent } from './consolidated-bank-statement/consolidated-bank-statement.component';
import { HttpClient } from '@angular/common/http';
import 'chartjs-plugin-datalabels';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { HttpHeaders } from '@angular/common/http';

@Component({
  selector: 'app-insight',
  templateUrl: './insight.component.html',
  styleUrls: ['./insight.component.scss']
})
export class InsightComponent implements OnInit {
  @Input() stateDashboard: Dashboard;
  dashboardName: string;
  excelSrc:string = 'assets/img/icons/icon-excel.png';
  pdfSrc:string = 'assets/img/icons/icon-pdf.png'; 
  alertShow:boolean = false; 

  basicInfo: any;
  isEdited: any = 0; 
  selectedTabIndex:number = 1;

  swal = swalFunction; 

  constructor(private global: GlobalService,
              private toastrService: ToastrService,
              private dashboardService: DashboardService,
              private router: Router,
              private http: HttpClient, 
              private modalService: NgbModal,
              private cdr: ChangeDetectorRef,) { }

  async ngOnInit(): Promise<void> {
    await this.refreshPage();
  }

  async refreshPage(){
    await this.getInsightBasicInformation();
    this.dashboardName = this.stateDashboard.dashboardName;
    this.selectedTabIndex = 0;

    if(this.basicInfo.isDataChangesAfterConsolidate == 1){
      this.getConsolidateStatus();
    }
  }

  async getInsightBasicInformation(){
    const request = {
      tenantCode : this.global.user.role.tenantCode,
      dashboardName : this.stateDashboard.dashboardName,
    };
    await this.dashboardService.getInsightBasicInformation(request).toPromise().then(
      (response) => {
        if (response['status']['code'] == 0) {
           this.basicInfo = response; 
        }
      }
    )
  }

  async getConsolidateStatus() {
    const request = {
      dashboardName : this.stateDashboard.dashboardName,
      tenantCode : this.global.user.role.tenantCode,
      audit: {
        callerId: this.global.user.loginId,
        locale: 'en'
      },
    };
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${this.global.token}` // assuming token is stored in global user object
    });
    await this.http.post(URLConstant.ConsolidateStatus,request,{ headers }).toPromise().then(response => {
      if (response['status'].code === 0) { 
        this.alertShow = true;  
        this.cdr.detectChanges();
        
      } else if (response['status'].code === 2005) {
        this.alertShow = false;
      } 
    })
  }

  onTabChange(event:any){
    console.log('onTabChange', event);
  }

  onClickEdit(){
    this.isEdited = this.isEdited == 1 ? 0 : 1;
  }

  async onSaveClick() {
    this.swal.Confirm('Are you sure? The page will be directed to Dashboard.').then(
      async (result) => {
        if (result.isConfirmed) { 
          const request = {
            tenantCode : this.global.user.role.tenantCode,
            newDashboardName : this.dashboardName,
            dashboardName : this.stateDashboard.dashboardName,
          };
          await this.dashboardService.editDashboardName(request).toPromise().then(
            (response) => {
              if (response['status']['code'] == 0) { 
                this.isEdited = 0;  
                this.router.navigate([PathConstant.DASHBOARD]);
                this.toastrService.success('Dashboard Name Saved!', null, {
                  positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
                }); 
              }
            }
          )
        } else{
          this.isEdited = 0;  
          this.dashboardName = this.stateDashboard.dashboardName;
          this.cdr.detectChanges();
        }
      }
    );
  } 

  onConsolidate(){

  }

  async onClickGetFile(type: string) {
    const request = {
      dashboardName : this.stateDashboard.dashboardName,
      tenantCode : this.global.user.role.tenantCode,
      documentType : type,
    };
    
    await this.dashboardService.consolidateDocumentUrl(request).toPromise().then(response => {
      if (response.status.code !== 0) {
        return;
      }

      if (request.documentType === 'PDF') {
        window.open(response['url'], '_blank');
        return;
      }

      this.getFile(response['url']);
      
    })
  }

  getFile(fileUrl: string) {
    this.http.get(fileUrl, { responseType: 'blob' }).subscribe(blob => {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = this.extractFileNameFromFileUrl(fileUrl);
      a.click();
      window.URL.revokeObjectURL(url);  
    }, error => {
      console.error('Error downloading file:', error);
    });
  }

  /**
   * @param url OSS file URL. Format: {oss_bucket}.{oss_endpoint}/consolidate_documents/{tenant_code}/{filename}.{file_extention}
   * @returns Filename from the URL. Format: {filename}.{file_extention}
   */
  extractFileNameFromFileUrl(url: string) {
    const urlObj = new URL(url);
    const pathName = urlObj.pathname; // Get /consolidate_documents/{tenant_code}/{filename}.{file_extention}
    const filename = pathName.substring(pathName.lastIndexOf('/')+1); // Get {filename}.{file_extention}
    return decodeURIComponent(filename);
  }
  
  clickInfo(){
    const modal = this.modalService.open(ConsolidatedBankStatementComponent, {
      centered: true,
      backdrop: 'static',
      size: 'lg'
    });
    modal.componentInstance.tenantCode = this.global.user.role.tenantCode;
    modal.componentInstance.dashboardName = this.stateDashboard.dashboardName; 
  }

  async startConsolidate() {
    const request = {
      dashboardName : this.stateDashboard.dashboardName,
      tenantCode : this.global.user.role.tenantCode,
    };
    await this.dashboardService.startConsolidate(request).toPromise().then(response => {
      if (response.status.code === 0) {
        this.swal.Success('Consolidate Started!');
        this.router.navigate([PathConstant.DASHBOARD]);
      }  
    })
  }

}
