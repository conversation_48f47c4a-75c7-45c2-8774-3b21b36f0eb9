import { CommonConstant } from "app/shared/constant/common.constant";

const baseChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    datalabels: false, // Disable datalabels plugin
  },
  legend: {
    display: true,
    position: 'top',
    labels: {
      font: {
        // size: 12
      },
    },
  },
  scales: {
    xAxes: [{
      display: true,
      scaleLabel: {
        display: true,
        labelString: 'Year-Month',
      },
    }],
  },
};

export const gpmOptions: any = {
  ...baseChartOptions,
  scales: {
    ...baseChartOptions.scales,
    yAxes: [
      {
        id: CommonConstant.LEFT,
        position: CommonConstant.LEFT,
        scaleLabel: {
          display: true,
          labelString: 'Profit Margin (%)',
          fontColor: CommonConstant.NAVY_COLOR
        },
        ticks: { beginAtZero: true }
      },
      {
        id: CommonConstant.RIGHT,
        position: CommonConstant.RIGHT,
        scaleLabel: {
          display: true,
          labelString: 'Regression',
          fontColor: CommonConstant.RED_COLOR
        },
        ticks: { fontColor: CommonConstant.RED_COLOR, beginAtZero: true }
      }
    ]
  }
}; 

export const wcrOptions: any = {
  ...baseChartOptions,
  scales: {
    ...baseChartOptions.scales,
    yAxes: [
      {
        id: CommonConstant.LEFT,
        position: CommonConstant.LEFT,
        scaleLabel: {
          display: true,
          labelString: 'Working Capital Ratio',
          fontColor: CommonConstant.NAVY_COLOR
        }
      },
      {
        id: CommonConstant.RIGHT,
        position: CommonConstant.RIGHT,
        scaleLabel: {
          display: true,
          labelString: 'Regression',
          fontColor: CommonConstant.RED_COLOR
        },
        ticks: { fontColor: CommonConstant.RED_COLOR }
      }
    ]
  }
};

export const lrOptions: any = {
  ...baseChartOptions,
  scales: {
    ...baseChartOptions.scales,
    yAxes: [
      {
        id: 'liquidityRatio',
        type: 'linear',
        scaleLabel: {
          display: true,
          labelString: 'Liquidity Ratio',
          fontColor: CommonConstant.NAVY_COLOR
        },
        ticks: { beginAtZero: true }
      }
    ]
  }
};