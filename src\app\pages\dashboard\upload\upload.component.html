<div class="uploader-container">
  <!-- Header -->
  <div class="uploader-header">
    <p>Upload Files</p>
  </div>
  <p>Only accept .pdf, .jpeg, .jpg, and .png file extension</p>

  <!-- Dashboard name -->
  <form *ngIf="!isBankStatement" [formGroup]="msxForm" id="msxForm">
    <div class="row">
      <div class="col-12" *ngFor="let question of formObj.components">
        <app-question [question]="question" [form]="msxForm" [direction]="formObj.direction" (inputX)="onInput($event)"></app-question>
      </div>
      <!-- <div class="col-12">
        <div class="d-inline-block font-weight-bold mr-3">Auto Consolidate</div>
        <div class="d-inline-block pt-1 pb-3">
          <ui-switch class="custom-switches"
             uncheckedLabel="Off" checkedLabel="On" size="small"   
             (change)="changeAutoConsolidate($event)"
             [checked]="isAutoConsolidate">
           </ui-switch>
        </div>
      </div> -->
    </div>
  </form>  
  <div>
    <div ng2FileDrop
      [ngClass]="{'nv-file-over': hasBaseDropZoneOver}" [uploader]="uploader"
      (fileOver)="fileOverBase($event)" (onFileDrop)="onFileSelected($event)"
      class="py-3 text-center font-medium-5 grey my-drop-zone" style="border-radius: 5px;">
      <em class="ft-file-plus font-large-2" style="color: #58595b;"></em>
      <p>Drop your files here</p>
      <p>or</p>
      <p>
        <label style="color: #1680ed;">
          <a><input type="file" accept=".pdf, .jpg, .jpeg, .png" ng2FileSelect [uploader]="uploader" (onFileSelected)="onFileSelected($event)" multiple>Browse file</a>
        </label>
        from your computer
      </p>
    </div>
  </div>

  <!-- Number of uplodaed file -->
  <p class="mt-2">Total file(s): {{uploader?.queue?.length}}</p>

  <!-- List of uploaded file -->
  <div *ngIf="uploader?.queue?.length > 0">

    <ngb-progressbar class="mb-3" style="height: 15px;" type="info"
      [value]="calculateProgressBar()" [striped]="true" [animated]="true">
      <i>{{calculateProgressBar()}}%</i>
    </ngb-progressbar>

    <button type="button" class="btn btn-danger mr-1 mb-1 mb-sm-0" (click)="openDeleteAllFileModal()">
      <span class="ft-trash-2 mr-1"></span> Remove All
    </button>
    <button type="button" class="btn btn-success mb-1 mb-sm-0" (click)="uploadAllFile()">
      <span class="ft-upload-cloud mr-1"></span> Upload All
    </button>
    <div class="mt-4"></div>
    <div class="container scroll pr-1" style="padding-left: 0px; padding-right: 0px; overflow-y: scroll; height: 150px;">
      <div class="file-item" *ngFor="let item of uploader?.queue">
        <i class="ft-file file-icon"></i>
        <div class="file-info">
          <div class="file-name">{{ item?.file?.name }} <i *ngIf="item.isSuccess" class="ft-check" style="color: #28a745; size: 20px;"></i></div>
          <div class="file-size">{{ item?.file?.size/1024/1024 | number:'.2' }} MB</div>
          <div *ngIf="item?.isError" class="file-status ml-2" [ngClass]="{'text-success': item?.isReady, 'text-danger': !item?.isReady}">
              {{ item?.isReady ? 'unlocked' : 'locked' }}
           </div>
        </div>
        <div class="file-actions"> 
          <i *ngIf="item?.isError" class="ft-lock file-action checked mr-2" title="password" style="color: #333738;" (click)="openModal(item)"></i>
          <i class="ft-trash-2 file-action checked mr-2" title="Delete" style="color: #333738;" (click)="deleteFile(item)"></i>
          <i class="ft-upload-cloud file-action checked mr-2" title="Upload" style="color: #333738;" (click)="uploadFile(item)"></i>
        </div>
      </div>
    </div>
    <div class="col-sm-4">
      <div class="form-check align-center mb-0">
        <input formControlName="isAutoConsolidate" class="form-check-input fs-5" type="checkbox" id="autoConsolidate" [checked]="false">
        <label for="autoConsolidate" class="form-check-label fs-5">Auto Consolidate</label>
      </div>
      <div class="form-check align-center mb-0">
        <input formControlName="isHitl" class="form-check-input fs-5" type="checkbox" id="hitl" [checked]="false">
        <label for="hitl" class="form-check-label fs-5">HITL</label>
      </div>
    </div>
  </div>

  <!-- Button -->
  <div class="text-center">
    <button class="btn btn-light mr-2 col-1" (click)="closeUploadModal()">{{'Cancel' | translate}}</button>
    <button class="btn btn-info col-1" (click)="saveDashboard()">{{'Save' | translate}}</button>
  </div>

</div>
 
 