import { AuditLog } from "app/model/audit-log";
import { Act } from "app/shared/components/msx-datatable/enums/act";
import { Align } from "app/shared/components/msx-datatable/enums/align";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { CommonConstant } from "app/shared/constant/common.constant";

export const AuditLogTable: Table<AuditLog> = {
    name: 'auditLogs', 
    list: [],
    columns: [
    {
        type: ColumnType.Number,
        prop: 'no',
        label: 'No',
        width: 100
    },
    {
        type: ColumnType.Date,
        format: CommonConstant.FORMAT_DATE,
        prop: 'dateTime',
        label: 'Date/Time',
        width: 100
    },
    {
        type: ColumnType.Text,
        prop: 'fileName',
        label: 'File Name',
        width: 100
    },
    {
        type: ColumnType.Text,
        prop: 'user',
        label: 'User',
        width: 100
    },
    {
        type: ColumnType.Text,
        prop: 'module',
        label: 'Module',
        width: 100
    },
    {
        type: ColumnType.Action,
        label: 'Action',
        width: 100,
        action: [  
        {
            class: CommonConstant.TEXT_PRIMARY,
            icon: 'ft-eye',
            descr: 'View',
            type: Act.View, 
        },
        {
            class: CommonConstant.TEXT_DANGER,
            icon: 'ft-trash-2',
            descr: 'Delete',
            type: Act.Delete, 
        }
        ]
    }
    ]
}
