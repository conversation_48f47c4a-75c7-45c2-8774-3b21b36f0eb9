import { serviceUrl } from 'environments/environment';

export class URLConstant {

  // Dashboard
  public static ListDashboard = serviceUrl + 'dashboard/s/list';
  public static ListBank = serviceUrl + 'dashboard/s/bankList';
  public static AddDashboard = serviceUrl + 'dashboard/s/add';
  public static AddNewBankStatement= serviceUrl + 'dashboard/s/addNewBankStatement';
  public static DeleteDashboard = serviceUrl + 'dashboard/s/delete';
  public static GetListAccountNo = serviceUrl + 'dashboard/s/accountNoList';
  public static GetListPeriod = serviceUrl + 'dashboard/s/periodList';
  public static GetListTransaction = serviceUrl + 'dashboard/s/getListTransaction';
  public static ConsolidateStatus = serviceUrl + 'dashboard/s/consolidateStatus';
  public static StartConsolidate = serviceUrl + 'dashboard/s/startConsolidate';
  public static AccountNoList = serviceUrl + 'dashboard/s/accountNoList';
  public static WarningStatus = serviceUrl + 'dashboard/s/warningStatus';

  // OCR Result
  public static ListOcrResult = serviceUrl + 'ocrResult/s/list';
  public static DeleteBankStatement = serviceUrl + 'ocrResult/s/deleteBankStatement';
  public static DeleteBankStatementTransactionDetail = serviceUrl + 'ocrResult/s/deleteBankStatementTransactionDetail';
  public static GetBankStatementHeaderTransaction = serviceUrl + 'ocrResult/s/getBankStatementHeaderTransaction';
  public static GetBankStatementHeaderTransactionSummary = serviceUrl + 'ocrResult/s/getBankStatementHeaderTransactionSummary';
  public static GetBankStatementHeaderTransactionFile = serviceUrl + 'ocrResult/s/getBankStatementHeaderTransactionFile';
  public static GetListBankStatementTransactionDetail = serviceUrl + 'ocrResult/s/getListBankStatementTransactionDetail';
  public static GetCalculatedListBankStatementSummary = serviceUrl + 'ocrResult/s/getCalculatedListBankStatementSummary';
  public static SaveBankStatementHeader = serviceUrl + 'ocrResult/s/saveBankStatementHeader';
  public static SaveBankStatementSummary = serviceUrl + 'ocrResult/s/saveBankStatementSummary';
  public static SaveBankStatementTransactionDetail = serviceUrl + 'ocrResult/s/saveBankStatementTransactionDetail';
  public static EditBankStatementTransactionDetail = serviceUrl + 'ocrResult/s/editBankStatementTransactionDetail';

  //Business Transaction Group 
  public static ListBusinessTransactionGroup = serviceUrl + 'businessTransactionGroup/s/list';
  public static AddBusinessTransactionGroup = serviceUrl + 'businessTransactionGroup/s/add';
  public static DeleteBusinessTransactionGroup = serviceUrl + 'businessTransactionGroup/s/delete';
  public static ListDetailBusinessTransactionGroup = serviceUrl + 'businessTransactionGroup/s/listDetail';
  public static DeleteDetailBusinessTransactionGroup = serviceUrl + 'businessTransactionGroup/s/deleteDetail';
  public static AddDetailBusinessTransactionGroup = serviceUrl + 'businessTransactionGroup/s/addDetails';


  //Non Business Transaction Group 
  public static ListNonBusinessTransactionGroup = serviceUrl + 'nonBusinessTransactionGroup/s/list';
  public static AddNonBusinessTransactionGroup = serviceUrl + 'nonBusinessTransactionGroup/s/add';
  public static DeleteNonBusinessTransactionGroup = serviceUrl + 'nonBusinessTransactionGroup/s/delete';
  public static ListDetailNonBusinessTransactionGroup = serviceUrl + 'nonBusinessTransactionGroup/s/listDetail';
  public static DeleteDetailNonBusinessTransactionGroup = serviceUrl + 'nonBusinessTransactionGroup/s/deleteDetail';
  public static AddDetailNonBusinessTransactionGroup = serviceUrl + 'nonBusinessTransactionGroup/s/addDetails';

  // Supplier Buyer Group
  public static ListSupplierBuyerGroup = serviceUrl + 'supplierBuyerGroup/s/list';
  public static GetListMasterSubGroup = serviceUrl + 'supplierBuyerGroup/s/getListMasterSubGroup';
  public static AddMainSupplierBuyerGroup = serviceUrl + 'supplierBuyerGroup/s/addMainGroup';
  public static DeleteMainSupplierBuyerGroup = serviceUrl + 'supplierBuyerGroup/s/deleteMainGroup';
  public static AddSupplierBuyerSubGroup = serviceUrl + 'supplierBuyerGroup/s/addSubGroup';
  public static DeleteSupplierBuyerSubGroup = serviceUrl + 'supplierBuyerGroup/s/deleteSubGroup';
  public static DeleteSubGroupMember = serviceUrl + 'supplierBuyerGroup/s/deleteSubGroupMember';
  public static AddSubGroupMembers = serviceUrl + 'supplierBuyerGroup/s/addSubGroupMembers';
  
  public static GetListSubGroupDropdown = serviceUrl + 'supplierBuyerGroup/s/getListSubGroup';
  public static GetListSubGroupOfMainGroup = serviceUrl + 'supplierBuyerGroup/s/getListSubGroupOfMainGroup';
  public static GetListSubGroupMember = serviceUrl + 'supplierBuyerGroup/s/getListSubGroupMember';
  public static EditMainGroupDetail = serviceUrl + 'supplierBuyerGroup/s/editMainGroupDetail';
  
  // Anomaly
  public static ListAnomaly = serviceUrl + 'anomaly/s/list';
  public static ListAnomalyMetadata = serviceUrl + 'anomaly/s/listAnomalyMetadata';
  public static AddAnomaly = serviceUrl + 'anomaly/s/add';
  public static DeleteAnomaly = serviceUrl + 'anomaly/s/delete';
  public static ListGroupAnomaly = serviceUrl + 'anomaly/s/listGroupAnomaly';
  public static DeleteGroupAnomaly = serviceUrl + 'anomaly/s/deleteGroupAnomaly';

  // Insight
  public static BasicInformation = serviceUrl + 'insights/s/basicInformation';
  public static EditDashboardName = serviceUrl + 'insights/s/editDashboardName';
  public static ConsolidatedBankStatementList = serviceUrl + 'insights/s/consolidatedBankStatementList';
  public static ConsolidateDocumentUrl = serviceUrl + 'insights/s/consolidateDocumentUrl';
  
  // Insight - SUPPLIER BUYER
  public static SupplierBuyerGpmWcrLr = serviceUrl + 'insights/s/supplierBuyerGpmWcrLr';
  public static SupplierBuyerInsightHeader = serviceUrl + 'insights/s/supplierBuyerInsightHeader';
  public static SupplierBuyerMonthlyCashflowAndGrowthRate = serviceUrl + 'insights/s/supplierBuyerMonthlyCashflowAndGrowthRate';
  public static SupplierBuyerTopFive = serviceUrl + 'insights/s/supplierBuyerTopFive';

  // Insight - GENERAL DASHBOARD
  public static GeneralInsightHeader = serviceUrl + 'insights/s/generalInsightHeader';
  public static GeneralCashFlow = serviceUrl + 'insights/s/generalCashFlow';
  public static GeneralInsightBodyCard = serviceUrl + 'insights/s/generalInsightBodyCard';
  public static GeneralDailyAnalysis = serviceUrl + 'insights/s/generalDailyAnalysis';
  public static GeneralCircularHeader = serviceUrl + 'insights/s/generalCircularHeader';
  public static GeneralCircularChartData = serviceUrl + 'insights/s/generalCircularChartData';

  // Circular
  public static ListCircular = serviceUrl + 'circular/s/list';
  public static ListViewTransactionCircular = serviceUrl + 'circular/s/listTransaction';
  public static CheckGroupNameCircular = serviceUrl + 'circular/s/checkGroupName';
  public static DeleteCircular = serviceUrl + 'circular/s/delete';
  public static AddCircular = serviceUrl + 'circular/s/add';
  
  // OSS
  public static GenerateOssSignature = serviceUrl + 'oss/s/generateSignature';

  // HITL
  public static HitlBankStatementList = serviceUrl + 'hitl/s/list';
  public static HitlSubmitRemark = serviceUrl + 'hitl/s/saveRemark';

  // Data
  public static GetLov = serviceUrl + 'data/lov';
  public static GetGeneralSetting = serviceUrl + 'data/s/generalSetting';
  public static GetActiveTenants = serviceUrl + 'data/s/activeTenants';
  
  // Users
  public static CheckUserForgotPassword = serviceUrl + 'user/checkUserForgotPassword';
  public static GetProfile = serviceUrl + 'user/s/profiles';
  public static SendOtpForgotPassword = serviceUrl + 'user/sendOtpForgotPassword';
  public static VerifyOtpForgotPassword = serviceUrl + 'user/verifyOtpForgotPassword';
  public static ResetPassword = serviceUrl + 'user/resetPassword';

  // TO BE DELETED (IMPORTED FROM ESIGNHUB)
  
  // OTP
  public static SendOtpByEmail = serviceUrl + 'user/s/sendOtpEmail';
  public static CheckOtpByEmail = serviceUrl + 'user/s/checkOtpEmail';
  public static SendOtpChangeProfile = `${serviceUrl}user/s/sendOTPChangeProfile`;
  public static VerifyOtpChangeProfile = `${serviceUrl}user/s/verifyOtpChangeProfile`;
  
  // Password
  public static ForgotPassword = serviceUrl + 'user/forgotPassword';
  public static VerifyResetCode = serviceUrl + 'user/checkResetCode';
  public static ChangePassword = `${serviceUrl}user/s/changePassword`;
  
  // TEMPLATE
  public static GetTemplate = `${serviceUrl}document/s/getDocumentTemplate`;
  public static ListTemplate = `${serviceUrl}document/s/listDocumentTemplate`;
  public static AddTemplate = `${serviceUrl}document/s/addDocumentTemplate`;
  public static UpdateTemplate = `${serviceUrl}document/s/updateDocumentTemplate`;
  public static CheckIsDocumentExist = `${serviceUrl}document/s/checkDocumentTemplateExist`;
  
  // Data
  
  
  
  
  public static getRoleUserManagement = `${serviceUrl}role/s/getrolelist`;
  
  public static getListDataPengguna = `${serviceUrl}user-management/s/getListDataPengguna`;
  public static getDataUserManagementView = `${serviceUrl}user-management/s/getDataUserManagementView`;
  public static updateDataUserManagement = `${serviceUrl}user-management/s/updateUserManagement`;
  
  // Data
  public static GetTenantList = serviceUrl + 'data/s/tenant';
  public static OfficeList = serviceUrl + 'data/s/officeList';
  public static RegionList = serviceUrl + 'data/s/regionList';
  public static BusinessLineList = serviceUrl + 'data/s/businessLineList';
  public static AddBalanceType = serviceUrl + 'data/addBalanceType';
  public static getListDocumentEMateraiType = `${serviceUrl}data/getListDocumentEMateraiType`;
  public static GetStatusStampingOtomatis = `${serviceUrl}data/s/getStatusStampingOtomatisTenant`;
  public static GetListPaymentType = `${serviceUrl}data/s/ListPaymentType`;
  public static ListPeruriDocumentType = `${serviceUrl}data/s/getListPeruriDocumentType`;
  
  // Menu
  public static GetMenu = `${serviceUrl}user/s/menu`;
  
  // Tenant Setting
  public static AddTenant = `${serviceUrl}tenant/s/add`;
  public static GetTenantSetting = `${serviceUrl}tenant/s/getTenantSettings`;
  public static GetTenantSettingEmbed = `${serviceUrl}tenant/getTenantSettingsEmbed`;
  public static UpdateTenantSetting = `${serviceUrl}tenant/s/updateTenantSettings`;
  public static GetAutomaticStampingAfterSign = `${serviceUrl}tenant/getAutomaticStampingAfterSign`;
  public static UpdateAutomaticStampingAfterSign = `${serviceUrl}tenant/updateAutomaticStampingAfterSign`;
  public static UpdateUploadUrl = `${serviceUrl}tenant/updateUploadUrl`;
  
  // Tenant
  public static ListTenant = `${serviceUrl}tenant/s/list`;
  
  // Reset Otp Code
  public static ResetOtpCode = serviceUrl + 'user/s/resetOtpCode';
  
}
