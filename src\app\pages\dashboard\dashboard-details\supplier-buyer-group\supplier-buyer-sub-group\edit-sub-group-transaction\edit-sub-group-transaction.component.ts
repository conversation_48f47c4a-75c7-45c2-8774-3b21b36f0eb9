import { ChangeDetector<PERSON><PERSON>, Component, <PERSON>Zone, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { HttpService } from 'app/services/http.service';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { Page } from 'app/shared/components/msx-datatable/models/page';
import { GlobalService } from 'app/shared/data/global.service';
import * as swalFunction from 'app/shared/data/sweet-alerts'; 
import { BehaviorSubject } from 'rxjs';
import { FormModel } from 'app/shared/components/ms-form/models';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { QuestionTextbox } from 'app/shared/components/ms-form/questions';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { PathConstant } from 'app/shared/constant/PathConstant'; 
import { ToastrService } from 'ngx-toastr';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DashboardService } from 'app/services/api/dashboard.service';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { EditSubGroupTransactionGroupTable } from './edit-sub-group-transaction.list-view';
import { AddTransactionGroupComponent } from '../../../add-transaction-group/add-transaction-group.component';

@Component({
  selector: 'app-edit-sub-group-transaction',
  templateUrl: './edit-sub-group-transaction.component.html',
  styleUrls: ['./edit-sub-group-transaction.component.scss']
})
export class EditSubGroupTransactionComponent implements OnInit {
  table = EditSubGroupTransactionGroupTable;
  datasource: BehaviorSubject<any> = new BehaviorSubject<any>([]);
  formObj: FormModel<any>;
  msxForm: FormGroup;
  state: any;
  dashboardName: string;
  swal = swalFunction;

  title: string = CommonConstant.SUPPLIER_BUYER_TITLE + ' - ' + CommonConstant.EDIT_GROUP_TRANSACTION_TITLE;

  constructor(
    private global: GlobalService,
    private router: Router,
    public http: HttpService,
    private ngZone: NgZone,
    private cdr: ChangeDetectorRef,
    private modalService: NgbModal, 
    //public activeModal: NgbActiveModal,
    private toastrService: ToastrService,
    private dashboardService: DashboardService,
    private readonly formBuilder: FormBuilder,) { 
      this.state = this.router.getCurrentNavigation().extras?.state;
    }

    async ngOnInit() {  
      if(!this.state){
        this.router.navigate([PathConstant.DASHBOARD]);
      }
      this.dashboardName = this.state.dashboardName;
      this.msxForm = this.formBuilder.group({
        subGroupName: [this.state.subGroupName || '', [Validators.required, Validators.maxLength(64)]],
      });
      this.setupQuestion(); 

      //table
      await this.getListSubGroupMember().then(() => {
        this.ngZone.run(() => {
          this.cdr.markForCheck();
        })
      });
    }

  async getPage(page: Page) {
    console.log('requestPage', page);
    await this.getListSubGroupMember(page.pageNumber + 1).then(() => {
      this.ngZone.run(() => {
        this.cdr.markForCheck();
      })
    });
  }

  async getListSubGroupMember(pageNumber: number = 1) {
    const request = {  
      dashboardName: this.state.dashboardName,
      subGroupName: this.state.subGroupName,
      tenantCode: this.global.user.role.tenantCode,
      page: pageNumber
    };
    await this.dashboardService.GetListSubGroupMember(request).toPromise().then(
      (response) => {
        if (response['status']['code'] == 0) {
          this.datasource.next(response);
        }
        this.cdr.detectChanges();
      }
    )
  }

  goToAddTransaction(){  
      const modal = this.modalService.open(AddTransactionGroupComponent, {
        centered: true,
        backdrop: 'static',
        size: 'xl'
      });  
      modal.componentInstance.formSource = CommonConstant.SUPPLIER_BUYER;
      modal.componentInstance.dashboardName = this.dashboardName;
      modal.componentInstance.subGroupName = this.state.subGroupName;
      modal.componentInstance.urlAdd = URLConstant.AddSubGroupMembers;
  
      modal.componentInstance.result.subscribe(() => {
         this.getListSubGroupMember(); // Refresh data  
      }); 
  }
  
  goBack() { 
    delete this.state.subGroupName;
    this.router.navigate([PathConstant.SUPPLIER_BUYER_SUB_GROUP], {state: this.state});
  }
  
  setupQuestion() { 
    this.formObj = {
      name: 'groupNameForm',
      mode: CommonConstant.MODE_EDIT,
      colSize: 6,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        new QuestionTextbox({
          key: 'subGroupName',
            label: 'Sub Group Name',
            placeholder: 'Type group name here',
            maxLength: 64,
            required: true,
            readonly: true,
            validations: [
              {type: 'required', message: 'Sub Group Name cannot be empty'},
              {type: 'maxlength', message: 'The maximum number of characters for the Group Name Name is 64'}
            ]
        }),
      ],
      params: []
    }
  }

  getQuestionkey(key:string) {
    return this.formObj.components.find(x => x.key === key);
  }

   
  onItemClickListener(event) {
    const data = event['data'];
    switch (event['act']['type']) {
      case Act.Delete:
        return this.deleteData(data);
    }

  }

  deleteData(data: any) {
    this.swal.Confirm('Are you sure? This data will be permanently lost.').then(
      async (result) => {
        if (result.isConfirmed) {
          const request = { 
            subGroupName: this.state.subGroupName,
            resultDetailId: data.resultDetailId,
            tenantCode: this.global.user.role.tenantCode,
            dashboardName: this.state.dashboardName
          };
          await this.dashboardService.deleteSubGroupMember(request).toPromise().then(
            async (response) => {
               if (response['status']['code'] == 0) {
                this.toastrService.success('Data successfully deleted!', null, {
                  positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
                });
                await this.getListSubGroupMember(); // Refresh data 
                // Trigger change detection to update UI
                this.cdr.detectChanges();
               }
            }
          )
        }
      }
    );
  }

}

