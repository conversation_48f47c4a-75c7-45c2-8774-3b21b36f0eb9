import { ComponentFixture, TestBed } from '@angular/core/testing';

import { HumanInTheLoopComponent } from './human-in-the-loop.component';

describe('HumanInTheLoopComponent', () => {
  let component: HumanInTheLoopComponent;
  let fixture: ComponentFixture<HumanInTheLoopComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ HumanInTheLoopComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(HumanInTheLoopComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
