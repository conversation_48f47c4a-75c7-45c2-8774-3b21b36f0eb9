// The file contents for the current environment will overwrite these during build.
// The build system defaults to the dev environment which uses `environment.ts`, but if you do
// `ng build --env=prod` then `environment.prod.ts` will be used instead.
// The list of which env maps to which file can be found in `.angular-cli.json`.
//test conflict
// export const serviceUrl = 'http://gdkwebsvr:7030/adins/bsa/services/';
// export const serviceUrl = 'http://gdkwebsvr.ad-ins.com:7030/adins/bsa/services/';
export const serviceUrl = 'http://localhost:8095/services/';

// Test from local VSC

// Ini comment axel
// test conflict kelvin
export const environment = {
//test conflict
  production: false,
  api: {
    auth: 'oauth/token',
    aesKey: 'aesKey',
//test conflict
    login: 'login',
  },
  interval: {
    otp: 120
  },
  excludeStatusCode: []
//test conflict
};

//test conflict
//test conflict


//test conflict