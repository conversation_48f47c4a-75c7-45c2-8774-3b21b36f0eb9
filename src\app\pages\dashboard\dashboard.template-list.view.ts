import { Dashboard } from "app/model/dashboard";
import { FormConstant } from "app/shared/components/ms-form/constants/form.constant";
import { FormModel } from "app/shared/components/ms-form/models";
import { Act } from "app/shared/components/msx-datatable/enums/act";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { MsxView } from "app/shared/components/msx-view/models/MsxView";
import { WidgetType } from "app/shared/components/msx-view/models/WidgetType";
import { CommonConstant } from "app/shared/constant/common.constant";

const DashboardSearchFilter: FormModel<string> = {
    name: 'dashboardSearchForm',
    direction: FormConstant.DIRECTION_HORIZONTAL,
    autoload: true,
    colSize: 6,
    components: [
        {
            key: 'dashboardName',
            label: 'Dashboard Name',
            placeholder: 'Type dashboard name here',
            controlType: FormConstant.TYPE_TEXT,
            value: ''
        },
        {
            key: 'filterCurrentUser',
            label: 'Dashboard Creator',
            placeholder: 'Select dashboard creator',
            controlType: FormConstant.TYPE_DROPDOWN,
            value: '0',
            options: [
                {key: '0', value: 'All'},
                {key: '1', value: 'Current User'}
            ]
        },
        {
            key: 'uploadDateStart',
            label: 'Create Date Start',
            placeholder: CommonConstant.FORMAT_DATE,
            controlType: FormConstant.TYPE_DATE
        },
        {
            key: 'uploadDateEnd',
            label: 'Create Date End',
            placeholder: CommonConstant.FORMAT_DATE,
            controlType: FormConstant.TYPE_DATE
        }
    ],
    params: [
        {
            key: 'page',
            controlType: FormConstant.TYPE_HIDDEN,
            value: 1
        }
    ]
}

const DashboardTable: Table<Dashboard> = {
    name: 'dashboards',
    list: [],
    columns: [
        {
            type: ColumnType.Text,
            prop: 'dashboardName',
            label: 'Dashboard Name',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'createdBy',
            label: 'Created By',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'uploadDate',
            label: 'Create Date',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'consolidateDate',
            label: 'Consolidate Date',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'lastUpdated',
            label: 'Last Updated',
            width: 100
        },
        {
            type: ColumnType.Action,
            label: 'Action',
            width: 100,
            action: [
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: 'ft-eye',
                    descr: 'View',
                    type: Act.View,
                    condition: true,
                    conditionVariable: 'editable',
                    conditionExpected: '1',
                    conditionVariable2: 'isConsolidating',
                    conditionExpected2: '1',
                    conditionedClass: 'd-none'
                },
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: 'ft-edit',
                    descr: 'Edit',
                    type: Act.Edit,
                    condition: true,
                    conditionVariable: 'editable',
                    conditionExpected: '0',
                    conditionedClass: 'd-none'
                },
                {
                    class: CommonConstant.TEXT_DANGER,
                    icon: 'ft-trash-2',
                    descr: 'Delete',
                    type: Act.Delete,
                    condition: true,
                    conditionVariable: 'deletable',
                    conditionExpected: '0',
                    conditionedClass: 'd-none'
                },
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: 'ft-loader',
                    descr: 'Consolidating',
                    type: Act.Loading,
                    condition: true,
                    conditionVariable: 'isConsolidating',
                    conditionExpected: '0',
                    conditionedClass: 'd-none'
                }
            ]
        }
    ]
}

export const DashboardListView: MsxView = {
    title: 'Dashboard',
    components: [
        {
            type: WidgetType.SearchFilter,
            component: DashboardSearchFilter
        },
        {
            type: WidgetType.Datatable,
            component: DashboardTable
        }
    ]
}