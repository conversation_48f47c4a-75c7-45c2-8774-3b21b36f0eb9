<div class="row text-left">
    
    <!-- Gross Profit Margin Starts -->
    <div class="col-md-12 col-lg-4" *ngIf="gpmData">
        <div class="card card-chart">
            <div class="card-header">
                <h4 class="card-title font-weight-bold text-center">Gross Profit Margin
                    <a class="ml-1" (click)="openGlossary('GPM', $event)"><i class="icon-info danger font-medium-1"></i></a>
                </h4>
                <!-- Hovering Glossary Card -->
               <div *ngIf="gpmGlossaryVisible" class="glossary-card" style="top:-170px; left: 280px;" #glossaryCardGPM>
                  <h5 class="font-weight-bold">Gross Profit Margin = (Total Buyer Amount - Total Supplier Amount) / Total Buyer Amount X 100 %</h5>
                  <p>Gross profit margin indicates the percentage of revenue retained after covering the cost of goods sold, with a higher margin reflecting a company's ability to cover operational costs and generate surplus funds for growth.</p>
                </div>
            </div> 
            <div class="card-body">
                <div class="card-block">
                    <div id="bar-bar1" class="height-300">
                        <canvas baseChart class="chart" 
                            [datasets]="gpmData"
                            [labels]="labels"
                            [options]="gpmOptions"
                            [colors]="oneBarOneLineChartColors"
                            [legend]="barChartLegend"
                            [chartType]="barChartType"
                            (chartHover)="chartHovered($event)"
                            (chartClick)="chartClicked($event)">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
    </div> 
    <!-- Gross Profit Margin Ends -->
     
    <!-- Working Capital Ratio Starts -->
    <div class="col-md-12 col-lg-4" *ngIf="wcrData">
        <div class="card card-chart">
            <div class="card-header">
                <h4 class="card-title font-weight-bold text-center">Working Capital Ratio
                    <a class="ml-1" (click)="openGlossary('WCR', $event)"><i class="icon-info danger font-medium-1"></i></a>
                </h4>
                <!-- Hovering Glossary Card -->
               <div *ngIf="wcrGlossaryVisible" class="glossary-card" style="top:-250px; left: 290px;" #glossaryCardWCR>
                 <h5 class="font-weight-bold">Working Capital Ratio = (Total Buyer Amount - Ending Balance) / Total Supplier Amount</h5>
                  <p>The Working Capital Ratio assesses a business's ability to cover short- term obligations with its net cash flow, where a ratio above 1 indicates sufficient liquidity and strong financial health, while a ratio below 1 suggests potential struggles with meeting liabilities, and a ratio below 0 indicates that current liabilities exceed current assets, signaling significant financial risk.</p>
               </div>
            </div>
            <div class="card-body">
                <div class="card-block">
                    <div id="bar-bar1" class="height-300">
                        <canvas baseChart class="chart"
                            [datasets]="wcrData"
                            [labels]="labels"
                            [options]="wcrOptions"
                            [colors]="oneBarOneLineChartColors"
                            [legend]="barChartLegend"
                            [chartType]="barChartType"
                            (chartHover)="chartHovered($event)"
                            (chartClick)="chartClicked($event)">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Working Capital Ratio Ends -->
     
    <!-- Liquidity Ratio Starts -->
    <div class="col-md-12 col-lg-4" *ngIf="lrData">
        <div class="card card-chart">
            <div class="card-header">
                <h4 class="card-title font-weight-bold text-center">Liquidity Ratio
                    <a class="ml-1" (click)="openGlossary('LR', $event)"><i class="icon-info danger font-medium-1"></i></a>
                </h4>
                <!-- Hovering Glossary Card -->
                <div *ngIf="lrGlossaryVisible" class="glossary-card" style="top:-210px; left: -210px;" #glossaryCardLR>
                  <h5 class="font-weight-bold">Liquidity Ratio = (Total Buyer Amount + Total Supplier Amount) / Ending Balance</h5>
                   <p>Liquidity ratio analysis evaluates a company's efficiency in using cash to conduct operations, indicating liquidity and business activity levels. A higher ratio suggests effective cash management and robust sales, while a declining ratio may signal cash flow issues or slower business activity.</p>
                </div>
            </div>
            <div class="card-body">
                <div class="card-block">
                    <div class="height-300">
                        <canvas height="300" baseChart class="chart"
                            [datasets]="lrData"
                            [labels]="labels"
                            [options]="lrOptions"
                            [colors]="lrColors"
                            [legend]="lineChartLegend"
                            [chartType]="lineChartType"
                            (chartHover)="chartHovered($event)"
                            (chartClick)="chartClicked($event)">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Line Chart middle right Ends -->

</div>