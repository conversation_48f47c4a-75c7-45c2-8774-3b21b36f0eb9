import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { CircularTransactionHeader } from 'app/model/circular-transaction-header';
import { Dashboard } from 'app/model/dashboard';
import { DashboardService } from 'app/services/api/dashboard.service'; 
import { UtilsService } from 'app/services/api/utils.service';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { GlobalService } from 'app/shared/data/global.service';
import * as chartsData from './general-circular-transaction-analysis.chart';
import { InsightsService } from 'app/services/api/insights.service';

@Component({
  selector: 'app-general-circular-transaction-analysis',
  templateUrl: './general-circular-transaction-analysis.component.html',
  styleUrls: ['./general-circular-transaction-analysis.component.scss']
})
export class GeneralCircularTransactionAnalysisComponent implements OnInit {
  @Input() stateDashboard: Dashboard; 
  isShowWarning:boolean = false;

  circularHeader: CircularTransactionHeader = {
    realCircularCount: 0,
    circularCount: 0,
    circularCountRatio: 0,
    realCircularAmount: 0,
    circularAmount: 0,
    circularAmountRatio: 0
  };

  MonthChartLabels:any[] = [];

  //Comparison Transaction Chart 
  monthlyPercentageCircularChartType = CommonConstant.BAR_CHART_TYPE; 
  monthlyPercentageCircularChartData:any[]; 
  monthlyPercentageCircularChartOptions:any = {}; 
  //monthlyPercentageCircularChartColors = chartsData.monthlyPercentageCircularColors;
  monthlyPercentageCircularChartColors:any[]; 

  //Comparison Transaction Chart 
  comparisonTransactionChartType = CommonConstant.BAR_CHART_TYPE; 
  comparisonTransactionChartData:any[]; 
  comparisonTransactionChartOptions:any = {}; 
  comparisonTransactionChartColors = chartsData.comparisonTransactionChartColors;    
  circularPercentageDetails:any = {}; 

  constructor(private global: GlobalService, private utilsService: UtilsService,
        private insightService: InsightsService,  
        private dashboardService: DashboardService, private cdr: ChangeDetectorRef,) { }

  async ngOnInit(): Promise<void> {
    this.generalCircularHeader();
    await this.generalCircularChartData();
    this.setupComparisonTransactionChartOptions();
    this.setupMonthlyPercentageCircularChartOptions();
    this.setmonthlyPercentageCircularColors();
     
  }

  setmonthlyPercentageCircularColors(){
    this.monthlyPercentageCircularChartColors= [
      {
        backgroundColor: this.getPercentageCircularColors(this.monthlyPercentageCircularChartData[0].data),
        borderColor: 'rgba(148,159,177,1)',
        pointBackgroundColor: 'rgba(148,159,177,1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(148,159,177,0.8)'
      }, 
    ]
  }

  generalCircularHeader(){
    const request = {
      tenantCode: this.global.user.role.tenantCode,
      dashboardName: this.stateDashboard.dashboardName
    }
    this.dashboardService.generalCircularHeader(request).toPromise().then( 
      (response) => {
        this.circularHeader.realCircularCount = response.realCircularCount != undefined ? response.realCircularCount : 0 ;
        this.circularHeader.circularCount = response.circularCount != undefined ? response.circularCount : 0 ;
        this.circularHeader.circularCountRatio = response.circularCountRatio != undefined ? response.circularCountRatio : 0 ;
        this.circularHeader.realCircularAmount = response.realCircularAmount != undefined ? response.realCircularAmount : 0 ;
        this.circularHeader.circularAmount = response.circularAmount != undefined ? response.circularAmount : 0 ;
        this.circularHeader.circularAmountRatio = response.circularAmountRatio != undefined ? response.circularAmountRatio : 0 ;
        this.isShowWarning = this.checkWarning(this.circularHeader.circularCountRatio, this.circularHeader.circularAmountRatio);
 
      }); 
    } 

  async generalCircularChartData(){
    const request = {
      tenantCode: this.global.user.role.tenantCode,
      dashboardName: this.stateDashboard.dashboardName
    }
    await this.dashboardService.generalCircularChartData(request).toPromise().then(  
      (response) => {
        this.MonthChartLabels = response.labels;
        this.monthlyPercentageCircularChartData = response.circularPercentage;  
        this.comparisonTransactionChartData = response.amountComparison;  
        this.mapDataToAdditionalData(response.circularPercentageDetails);
    }); 
  } 

  mapDataToAdditionalData(Transaction:any) {
    Transaction.forEach(item => {
      this.circularPercentageDetails[item.period] = {
        circularAmount: this.insightService.formatCurrency(item.circularAmount),
        totalAmount: this.insightService.formatCurrency(item.totalAmount), 
      };
    }); 
  }

  checkWarning(circularCountRatio:number, circularAmountRatio:number){
    return circularCountRatio >= 25 || circularAmountRatio >= 25;
  }

  setupMonthlyPercentageCircularChartOptions(){ 
    const circularPercentageDetails = this.circularPercentageDetails;
    this.monthlyPercentageCircularChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      legend: {
        display: false,
      },
      plugins: { 
        datalabels: { 
          display: true,
          color: '#000', // Label color
          font: {
            weight: 'bold'
          },
          anchor: 'end',
          align: 'end',
          clamp: true,
        },
      },
      scales: {
        xAxes: [{
          display: true,
          scaleLabel: {
            display: true,
            labelString: 'Period'
          }
        }],
        yAxes: [
          {
            id: 'netcash', // Left Y-axis
            type: 'linear',
            position: 'left',
            scaleLabel: {
              display: true,
              labelString: 'Circular (%)',
              fontColor: 'black'
            },
            ticks: {
              fontColor: 'black',
              callback: function (value: number) {
                return UtilsService.abbreviateNumber(value); // Display absolute values on Y-axis
              } 
            },
          }
        ]
      },
      tooltips: {
        callbacks: {
          title: function (tooltipItems: any) {
            // Get the label of the hovered bar
            const label = tooltipItems[0].xLabel;
            return `${label}`;
          },
          beforeLabel: function (tooltipItem: any) {
            // Get additional data for the hovered period
            const label = tooltipItem.xLabel;
            const data = circularPercentageDetails[label];
    
            // Format and return detailed information
            return [
              `Circular Transaction Amount                   : ${data.circularAmount}`,
              `Total Transaction Amount                        : ${data.totalAmount}`,
            //  `Circular Percentage                              : ${data.endingBalance} %`
            ];
          },
          label: function (tooltipItem: any) {
            return ''; // Return an empty label to suppress the default value
          }
        }
      },
      title: {
        display: true,
        fontColor: 'black',   
        fontSize: 14,   
        fontStyle: 'bold',  
        text: [
          'Monthly Percentage of Circular Transaction',   // First line
          'Circular Threshold 25%'       // Second line
        ] 
      }, 
      
    };
  };

  setupComparisonTransactionChartOptions(){ 
    const circularPercentageDetails = this.circularPercentageDetails;
    this.comparisonTransactionChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      legend: {
        position: 'top',
      },
      plugins: { 
        datalabels: { 
          display: false,
          color: '#000', // Label color
          font: {
            weight: 'bold'
          },
          anchor: 'end',
          align: 'end',
          clamp: true,
        },
      },
      scales: {
        xAxes: [{
          display: true,
          scaleLabel: {
            display: true,
            labelString: 'Period'
          }
        }],
        yAxes: [
          {
            id: 'netcash', // Left Y-axis
            type: 'linear',
            position: 'left',
            scaleLabel: {
              display: true,
              labelString: 'Amount',
              fontColor: 'black'
            },
            ticks: {
              fontColor: 'black',
              callback: function (value: number) {
                return UtilsService.abbreviateNumber(value); // Display absolute values on Y-axis
              } 
            },
          }
        ]
      },
      tooltips: {
        callbacks: {
          title: function (tooltipItems: any) {
            // Get the label of the hovered bar
            const label = tooltipItems[0].xLabel;
            return `${label}`;
          },
          beforeLabel: function (tooltipItem: any) {
            // Get additional data for the hovered period
            const label = tooltipItem.xLabel;
            const data = circularPercentageDetails[label];
    
            // Format and return detailed information
            return [
              `Circular Transaction Amount                   : ${data.circularAmount}`,
              `Total Transaction Amount                        : ${data.totalAmount}`,
            //  `Circular Percentage                              : ${data.endingBalance} %`
            ];
          },
          label: function (tooltipItem: any) {
            return ''; // Return an empty label to suppress the default value
          }
        }
      },
      title: {
        display: true,
        fontColor: 'black',   
        fontSize: 14,   
        fontStyle: 'bold',  
        text: 'Comparison of Circular Transaction Amount and Total Transaction Amount'
      }, 
      
    };
  };

  getPercentageCircularColors(data: number[]): string[] {
    return data.map(value => {
      if (value < 25) {
        return '#64ca64';  // green
      }
      
      return '#fe6565';  // Red 
    });
  }
    
  chartClicked(e: any): void {
    //your code here
  }
  
  chartHovered(e: any): void {
    //your code here
  }

}
