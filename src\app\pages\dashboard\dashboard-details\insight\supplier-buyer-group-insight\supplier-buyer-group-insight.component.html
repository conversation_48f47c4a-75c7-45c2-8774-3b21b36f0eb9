<div class="tab-background px-3 pb-1">
	<!-- Minimal statistics section start -->
<section id="minimal-statistics">
	<div class="row text-left">
		<div class="col-xl-3 col-lg-6 col-12">
			<div class="card card-info" style="border-color: #64ca64;">
				<div class="card-body">
					<div class="media">
						<div class="media-right align-self"> 
						  <div class="float-right">
							<img [src]="iconSupplierAmount" class="icon-card" alt="Supplier Amount Icon Image">
						  </div>
						</div>
						<div class="media-body text-right">
						  <span class="card-title">Total Supplier Transactions Amount</span> 
						  <h6 class="card-value pt-2">Rp. {{insightCard.supplierAmount | number: '1.2-2'}}</h6>
						</div>  
					</div> 
				</div>
			</div>
		</div>
		<div class="col-xl-3 col-lg-6 col-12">
			<div class="card card-info" style="border-color: #f5b700;">
				<div class="card-body">
					<div class="media">
						<div class="media-right align-self"> 
						  <div class="float-right">
							<img [src]="iconBuyerAmount" class="icon-card" alt="Supplier Amount Icon Image">
						  </div>
						</div>
						<div class="media-body text-right">
						  <span class="card-title">Total Buyer Transactions Amount</span> 
						  <h6 class="card-value pt-2">Rp. {{insightCard.buyerAmount | number: '1.2-2'}}</h6>
						</div>  
					</div> 
				</div>
			</div>
		</div> 

		<div class="col-xl-3 col-lg-6 col-12">
			<div class="card card-info" style="border-color: #2a2e4e;">
				<div class="card-body">
					<div class="media">
						<div class="media-right align-self"> 
						  <div class="float-right">
							<img [src]="iconSupplierTrxCount" class="icon-card" alt="Supplier Amount Icon Image">
						  </div>
						</div>
						<div class="media-body text-right">
						  <span class="card-title">No. of Supplier Transactions</span> 
						  <h6 class="card-value pt-2">{{insightCard.supplierTrxCount | mask: 'separator':'.'}}</h6>
						</div>  
					</div> 
				</div>
			</div>
		</div>

		<div class="col-xl-3 col-lg-6 col-12">
			<div class="card card-info" style="border-color: #387d7a;">
				<div class="card-body">
					<div class="media">
						<div class="media-right align-self"> 
						  <div class="float-right">
							<img [src]="iconBuyerTrxCount" class="icon-card" alt="Supplier Amount Icon Image">
						  </div>
						</div>
						<div class="media-body text-right">
						  <span class="card-title">No. of Buyer Transactions</span> 
						  <h6 class="card-value pt-2">{{insightCard.buyerTrxCount | mask: 'separator':'.'}}</h6>
						</div>  
					</div> 
				</div>
			</div>
		</div>  
	</div>
</section>
<!-- // Minimal statistics section end -->

<div *ngIf="!isHide" class="row text-left">
    <div class="col-md-12 col-lg-6" *ngIf="cashFlowChartData">
        <div class="card card-chart">
            <div class="card-body">
                <div class="card-block">
                    <div id="bar-bar1" class="height-400">
                        <canvas  baseChart class="chart" [datasets]="cashFlowChartData" [labels]="MonthChartLabels" [options]="cashflowChartOptions" 
						[colors]="cashFlowChartColors" [legend]="true" [chartType]="cashFlowChartType" (chartHover)="chartHovered($event)" (chartClick)="chartClicked($event)"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-12 col-lg-6" *ngIf="growthRateChartData">
        <div class="card card-chart"> 
            <div class="card-body">
                <div class="card-block">
                    <div class="height-400">
                            <canvas baseChart class="chart" [datasets]="growthRateChartData" [labels]="MonthChartLabels" [options]="growthRateChartOptions" [colors]="growthRateChartColors"
                            [legend]="true" [chartType]="growthRateChartType" (chartHover)="chartHovered($event)" (chartClick)="chartClicked($event)" ></canvas>
                    </div>
                </div>
            </div>
        </div>
      </div>
</div>

<app-margin-ratio-charts *ngIf="!isHide" [stateDashboard]="stateDashboard"></app-margin-ratio-charts>
<app-diversity-of-supplier-charts [stateDashboard]="stateDashboard"></app-diversity-of-supplier-charts>
</div>