export class CommonConstant {

  // Common Mode Pages
  public static MODE_ADD = 'Add';
  public static MODE_EDIT = 'Edit';
  public static MODE_VIEW = 'View';

  // Status
  public static STATUS_ACTIVE = '1';
  public static STATUS_CODE_SUCCESS = 0;
  public static STATUS_CODE_RESET_PASSWORD_CODE_INVALID = 8117;

  // Image
  public static MAX_IMAGE_WIDTH_HEIGHT = 1483;

  // Toaster position
  public static TOAST_BOTTOM_RIGHT = 'toast-bottom-right';

  // Data
  public static DATA_IMAGE_JPEG_BASE64      = 'data:image/jpeg;base64,';
  public static DATA_IMAGE_JPG_BASE64       = 'data:image/jpg;base64,';
  public static DATA_APPLICATION_PDF_BASE64 =  'data:application/pdf;base64,';
  public static DATA_ACCEPT_APPLICATION_PDF = 'application/pdf';

  // Response Code
  public static RESPONSE_CODE =  'Response code: ';

  // Const
  public static CONST_SELECT_SALDO_TYPE         = 'Pilih Tipe Saldo';
  public static CONST_SELECT_TENANT             = 'Pilih Tenant';
  public static CT_POINT_REGULER                = 'ct-point-regular';
  public static POINTER_EVENTALL_IMPORTANT      = 'pointer-events: all !important';
  public static CT_POINT_CIRCLE                 = 'ct-point-circle';
  public static LABEL_REQUEST_DATE              = 'Request Date';
  public static LABEL_TOTAL_SIGNER_SIGNED       = 'Total Signer Signed';
  public static LABEL_TOTAL_MATERAI             =  'Total Meterai';
  public static LABEL_SIGNING_PROCESS           = 'Signing Process';
  public static LABEL_CHOOSE_FILE               = 'Choose File';
  public static BUTTON_DANGER                   = 'btn btn-danger';
  public static LABEL_SIGNING_DOCUMENT          = 'Signing Document';
  public static TEXT_PRIMARY                    = 'text-primary';
  public static TITLE_INQUIRY_DOCUMENT          = 'Inquiry Document';
  public static LABEL_STAMPING_PROCESS          = 'Stamping Process';
  public static LABEL_COMPLETE_DATE             = 'Complete Date';
  public static TEXT_DANGER                     = 'text-danger';
  public static TEXT_SUCCESS                    = 'text-success';
  public static ICON_FT_DOWNLOAD                = 'ft-download';
  public static LABEL_CUSTOMER_NAME             = 'Customer Name';
  public static LABEL_DOCUMENT_NUMBER           = 'Document Number';
  public static DOCUMENT_TEMPLATE_NAME          = 'Document template name';
  public static FORMAT_DATE                     = 'DD-MMM-YYYY';
  public static FORMAT_DATE_WITHTIME            = 'DD-MMM-YYYY HH:mm';
  public static FORMAT_DATE_WITHTIME_SECOND     = 'DD-MMM-YYYY HH:mm:ss';
  public static FORMAT_DATE_WITHTIME_SECOND_MILISECOND     = 'DD-MMM-YYYY HH:mm:ss.ms';
  public static FORMAT_DATE_YYYY_MM_DD = 'YYYY-MM-DD';
  public static LABEL_STATUS_TTD = "Status TTD";

  public static BTN_BTN_PRIMARY = 'btn btn-primary';
  public static BTN_BTN_DANGER_ML1 = 'btn btn-danger ml-1';
  public static BTN_BTN_INFO = 'btn btn-info';
  public static BTN_BTN_SECONDARY_MR2 =  'btn btn-secondary mr-2';
  public static BTN_BTN_SUCCESS = 'btn btn-success';
  public static CONST_YOURWORK_HAS_BEEN_SAVED = 'Your work has been saved';
  public static CONST_YOUCLICKED_THE_BUTTON = 'You clicked the button!';
  public static TITLE_INQUIRY_DOCUMENT_DETAIL = 'Inquiry Document - Detail';

  public static ICON_FT_MESSAGE_SQUARE = "ft-message-square";
  public static ICON_FT_FILE_TEXT      = "ft-file-text";
  public static ICON_FT_ALERT_TRIANGLE = "ft-alert-triangle";
  public static ICON_FT_SIDEBAR        = "ft-sidebar";

  public static ICON_GREEN_DOLLAR_SIGN_IN = 'assets/img/icons/insight/green-dollar-sign-in.png';
  public static ICON_YELLOW_DOLLAR_SIGN_OUT = 'assets/img/icons/insight/yellow-dollar-sign-out.png';
  public static ICON_NAVY_DOC_IN = 'assets/img/icons/insight/navy-doc-in.png';
  public static ICON_EMERALD_DOC_OUT = 'assets/img/icons/insight/emerald-doc-out.png';
  public static ICON_GENERAL_OPENING_BALANCE = 'assets/img/icons/insight/general-opening-balance.png';
  public static ICON_GENERAL_ENDING_BALANCE = 'assets/img/icons/insight/general-ending-balance.png';
  public static ICON_GENERAL_NO_DEBIT = 'assets/img/icons/insight/general-no-debit.png';
  public static ICON_GENERAL_NO_CREDIT = 'assets/img/icons/insight/general-no-credit.png';

  public static IMAGE_TYPE_JPEG = 'image/jpeg';

  public static EN = 'en';

  // title
  public static INSIGHTS_TITLE = 'Insights';
  public static OCR_RESULT_TITLE = 'OCR Result';
  public static ANOMALY_TITLE = 'Anomaly';
  public static TRANSACTION_TITLE = "Transaction";
  public static SETTINGS_TITLE = "Settings";
  public static AUDIT_LOG_TITLE = "Audit Log";
  public static CIRCULAR_TRANSACTION_TITLE = 'Circular Transaction';
  public static SUPPLIER_BUYER_TITLE = 'Supplier Buyer Mapping';
  public static BUSINESS_TRANSACTION_TITLE = 'Business Transaction';
  public static NON_BUSINESS_TRANSACTION_TITLE = 'Non Business Transaction';
  public static EDIT_GROUP_TRANSACTION_TITLE = 'Edit Group Transaction'; 
  public static ADD_VIEW_SUB_GROUP_TITLE = 'Add/View Master Sub Group';
  public static VIEW_GROUP_TRANSACTION = 'View Group Transaction';

  //index tab
  public static ANOMALY_TAB_INDEX = 0;
  public static OCR_RESULT_TAB_INDEX = 1;
  public static CIRCULAR_TAB_INDEX = 2;
  public static SUPPLIER_BUYER_TAB_INDEX = 3;
  public static BUSINESS_TRANSACTION_TAB_INDEX = 4;
  public static NON_BUSINESS_TRANSACTION_TAB_INDEX = 5;
  public static INSIGHTS_TAB_INDEX = 6;


  //LOV CODE
  public static CATEGORY = 'CATEGORY';
  public static ANOMALY_REASON = 'ANOMALY_REASON';
  public static PROCESS_STATUS = 'PROCESS_STATUS';
  public static PWD_CHANGE_TYPE = 'PWD_CHANGE_TYPE';
  public static ANOMALY_RISK = 'ANOMALY_RISK';

  public static NON_BUSINESS = 'NON BUSINESS';
  public static ANOMALY = 'ANOMALY';
  public static SUPPLIER_BUYER = 'SUPPLIER/BUYER';
  public static CIRCULAR = 'CIRCULAR';

  public static CIRCULAR_TRANSACTION_WINDOW = 'CIRCULAR_TRANSACTION_WINDOW';
 
  //SUPPLIER BUYER TYPE
  public static SUPPLIER_TYPE= 'Supplier';
  public static BUYER_TYPE = 'Buyer';
  public static RELATED_PARTIES_TYPE ='Related Parties';

  //position
  public static TOP = 'top';
  public static BOTTOM = 'bottom';
  public static RIGHT = 'right';
  public static LEFT = 'left';
  public static AUTO = 'auto';

  //OCR 
  public static CAN_CONSOLIDATE = "CAN_CONSOLIDATE";
	public static CANNOT_CONSOLIDATE = "CANNOT_CONSOLIDATE";
	public static CONSOLIDATE_COMPLETE = "CONSOLIDATE_COMPLETE";

  //CHARTS
	public static BAR_CHART_TYPE = "bar";
	public static LINE_CHART_TYPE = "line";
	public static PIE_CHART_TYPE = "pie";
	public static HOR_BAR_CHART_TYPE = "horizontalBar";

	public static GROSS_PROFIT_MARGIN = "GPM";
	public static WORKING_CAPITAL_RATIO = "WCR";
	public static LIQUIDITY_RATIO = "LR";

  //CHARTS COLORS
  public static BLUE_COLOR = "#4ea5d9";
  public static RED_COLOR = "#fe6565";
  public static YELLOW_COLOR = "#ffa500";
  public static BLACK_COLOR = "#2A292D";
  public static NAVY_COLOR = "#2A2E4E";
  public static LIGHT_GREEN_COLOR = "#64ca64";
  public static DARK_GREEN_COLOR = "#387d7a";
 
  //CHARTS POINT STYLE
  public static LINE = "line";
  public static CIRCLE = "circle";
  public static RECTANGLE = "rect";

  //Anomaly Risk
  public static HIGH = "HIGH";
  public static MEDIUM = "MEDIUM";
  public static LOW = "LOW";
}
