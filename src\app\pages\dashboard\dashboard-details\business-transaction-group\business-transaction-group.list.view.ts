import { NonBusinessTransactionGroup } from "app/model/NonBusinessTransactionGroup"; 
import { Act } from "app/shared/components/msx-datatable/enums/act";
import { Align } from "app/shared/components/msx-datatable/enums/align";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table"; 
import { CommonConstant } from "app/shared/constant/common.constant";

export const BusinessTransactionGroupTable: Table<NonBusinessTransactionGroup> = {
    name: 'listBusinessTransactionGroup',  
    list: [],
    columns: [
        {
            type: ColumnType.Number, 
            prop: 'rowNum',
            label: 'No',
            width: 50
        },
        {
            type: ColumnType.Text,
            prop: 'groupName',
            label: 'Group',
            width: 50
        },
        {
            type: ColumnType.Text,
            prop: 'createdDate',
            label: 'Created Date',
            width: 50
        },  
        {
            type: ColumnType.Text,
            prop: 'lastUpdated',
            label: 'Last Updated',
            width: 50
        },
        {
            type: ColumnType.Action,
            label: 'Action',
            width: 50,
            action: [ 
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: 'ft-edit',
                    descr: 'Edit',
                    type: Act.Edit, 
                },
                {
                    class: CommonConstant.TEXT_DANGER,
                    icon: 'ft-trash-2',
                    descr: 'Delete',
                    type: Act.Delete, 
                }
            ]
        }
    ]
}

