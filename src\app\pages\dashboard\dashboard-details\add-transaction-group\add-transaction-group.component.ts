import {ChangeDetector<PERSON>ef, Component, EventEmitter, Input, NgZone, OnInit, Output, ViewChild,} from '@angular/core';  
import {BehaviorSubject} from 'rxjs'; 
import {FormGroup} from '@angular/forms'; 
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { Button } from 'app/shared/components/msx-view/models/Button';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { Page } from 'app/shared/components/msx-datatable/models/page';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { QuestionDropdown } from 'app/shared/components/ms-form/questions';
import { CommonConstant } from 'app/shared/constant/common.constant'; 
import { Table } from 'app/shared/components/msx-datatable/models/table';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type'; 
import { GlobalService } from 'app/shared/data/global.service'; 
import { HttpService } from 'app/services/http.service';
import { Transaction } from 'app/model/transaction';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { DashboardService } from 'app/services/api/dashboard.service';
import { ToastrService } from 'ngx-toastr';
import { AddTransactionGroupRequest } from 'app/model/api/NonBusinessTransactionGroup/add-transaction-group.request';
import { MsxDatatableComponent } from 'app/shared/components/msx-datatable/msx-datatable.component';
import { Align } from 'app/shared/components/msx-datatable/enums/align';
import { MsxDatatableV2Component } from 'app/shared/components/msx-datatable-v2/msx-datatable-v2.component';
import { AddAnomalyRequest } from 'app/model/api/Anomaly/add-anomaly.request';
import { AnomaliesContainer } from 'app/model/custom/anomales.container';

@Component({
  selector: 'app-add-transaction-group',
  templateUrl: './add-transaction-group.component.html',
  styleUrls: ['./add-transaction-group.component.scss']
})
export class AddTransactionGroupComponent implements OnInit { 
  container: MsxView; 
  serviceUrl: string = URLConstant.GetListTransaction; 
  buttonList: Button[];
  AccountNo:string;
  selectedItem: any[] = [];  
  isScrollTopVisible = false;

  searchFormObj: FormModel<any> = new FormModel<any>();
  AddTransactionGroupTable: Table<Transaction>= new Table<Transaction>();
  formtab: FormGroup;

  @Input() dashboardName: string;
  @Input() formSource: string;
  @Input() groupName: string;
  @Input() subGroupName: string;
  @Input() urlAdd: string;
  @Output() result = new EventEmitter<void>();

  @ViewChild(MsxDatatableComponent) msxDatatableComponent!: MsxDatatableComponent;
  @ViewChild(MsxDatatableV2Component) msxDatatableV2Component!: MsxDatatableV2Component;
 
  public WidgetType = WidgetType;
  public CommonConstant = CommonConstant;
  public pageNumber: BehaviorSubject<number> = new BehaviorSubject(1);

  private datasource: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);

  constructor(
    private global: GlobalService,
    public activeModal: NgbActiveModal, 
    public http: HttpService,
    private ngZone: NgZone,
    private cdr: ChangeDetectorRef,
    private dashboardService: DashboardService,
    private toastrService: ToastrService
  ) {  
  }

  ngOnInit(): void {   
    // ---------- SEARCH AND TABLE -------------- 
    this.searchFormObj = {
      name: 'AddTransactionGroupSearchForm',
      direction: FormConstant.DIRECTION_HORIZONTAL,
      colSize: 6,
      components: [
          new QuestionDropdown({
              key: 'accountNo',
              label: 'Account No',
              placeholder: 'Select Account No',
              serviceUrl: URLConstant.GetListAccountNo,
              options: [
                { key: '', value: 'Select One' }
              ],
              value: '',
              args: {
                list: 'accountNos',
                key: 'accountNo',
                value: 'accountNo'
              },
              required: true,
              validations: [
                {type: 'required', message: 'Must Choose Account No!'}
              ],
              params: { 
                dashboardName: this.dashboardName,
                tenantCode: this.global.user.role.tenantCode, 
                audit:{
                  callerId:this.global.user.loginId,
                  locale: CommonConstant.EN
                }
              }
            }),
            new QuestionDropdown({
              key: 'category',
              label: 'Category',
              placeholder: 'Select category',
              serviceUrl: URLConstant.GetLov,
              options: [
                { key: '', value: 'Select One' }
              ], 
              value: '',
              args: {
                list: 'lovList',
                key: 'code',
                value: 'description'
              },
              //required: true,
              validations: [
                 {type: 'required', message: 'Must Choose Category!'}
              ],
              params: { 
                lovGroup: CommonConstant.CATEGORY,  
              }
            }),
            new QuestionDropdown({
              key: 'period',
              label: 'Period',
              placeholder: 'Select Period',
              serviceUrl: URLConstant.GetListPeriod,
              options: [
              //  { key: '1', value: 'Select One' }
              ],
              value: '',
              args: {
                list: 'periods',
                key: 'period',
                value: 'period'
              },
              //required: true,
              validations: [
                {type: 'required', message: 'Must Choose Period!'}
              ],
              params: { 
                dashboardName: this.dashboardName,
                tenantCode: this.global.user.role.tenantCode, 
              }
            }),
          {
              key: 'type',
              label: 'Type',
              placeholder: 'Select type',
              controlType: FormConstant.TYPE_DROPDOWN,
              value: '',
              options: [
                  {key: '', value: 'All'},
                  {key: 'Credit', value: 'Credit'},
                  {key: 'Debit', value: 'Debit'}
              ]
          },  
          {
            key: 'amountMoreThan',
            label: 'Amount >=', 
            placeholder: 'Type amount here',
            controlType: FormConstant.TYPE_CURRENCY,
            value: 0
          }, 
          {
            key: 'amountLessThan',
            label: 'Amount <=', 
            placeholder: 'Type amount here',
            controlType: FormConstant.TYPE_CURRENCY,
            value: 0
          },
          {
            key: 'description',
            label: 'Description',
            placeholder: 'Type description here',
            controlType: FormConstant.TYPE_TEXT
          }
      ],
      params: [ 
          {
            key: 'tenantCode',
            controlType: 'hidden', 
            value: this.global.user.role.tenantCode 
          },
          {
            key: 'dashboardName',
            controlType: 'hidden', 
            value: this.dashboardName 
          } ,
          {
            key: 'formSource',
            controlType: 'hidden', 
            value: this.formSource 
          }
      ]
    }

    this.AddTransactionGroupTable = {
      name: 'listTransactions',
      enableSelection: true,
      disablePagination: true,
      list: [],
      columns: [
          {
              type: ColumnType.Number,
              prop: 'row',
              label: 'No',
              width: 50
          },
          {
              type: ColumnType.Text,
              prop: 'file',
              label: 'File',
              width: 100
          },
          {
              type: ColumnType.Text,
              prop: 'accountNo',
              label: 'Account No',
              width: 100
          },
          {
              type: ColumnType.Date,
              format: CommonConstant.FORMAT_DATE,
              prop: 'date',
              label: 'Date',
              width: 100
          },
          {
              type: ColumnType.Text,
              prop: 'description',
              label: 'Description',
              width: 100
          },
          {
              type: ColumnType.Currency,
              prop: 'amount',
              label: 'Amount', 
              width: 100
          },
          {
              type: ColumnType.Text,
              prop: 'type',
              label: 'Type',
              width: 100
          },
          {
            type: ColumnType.Dropdown,
            prop: 'reason',
            label: 'Reason',
            width: 250
        }  

      ]
    }

    this.container = {
      title: '',
      components: [
        {
          type: WidgetType.SearchFilter,
          component: this.searchFormObj
        },
        {
          type: WidgetType.Datatable,
          component: this.AddTransactionGroupTable
        }
      ]
    };
    // ---------- END SEARCH AND TABLE -------------- 
  }

  onBtnClick() {
    //this.onBtnClickListener.emit($event)
  }

  onItemClick($event) {
    //this.onItemClickListener.emit($event);
  }

  onForm(form: FormGroup) {
    //this.form.emit(form);
  }
  

  getResult(data) {    
    console.log("data di datatable", data)
    if (data['status'] && data['status']['code'] === 0) {
      this.datasource.next(data);
      this.clearSelectedOptions();
    }
    console.log('Result', this.datasource); 
  }

  clearSelectedOptions(){
    this.selectedItem = [];
    if (this.formSource === CommonConstant.ANOMALY) {
      this.msxDatatableV2Component.chkBoxSelected = [];
      this.msxDatatableV2Component.selectedOptions = [];
      this.msxDatatableV2Component.tempSelectedOptions = []; 
    } else{
      this.msxDatatableComponent.chkBoxSelected = [];
    }  
  }

  getPage(page: Page) {
    console.log('requestPage', page);
    this.pageNumber.next(page.pageNumber + 1);
  }

  onSelect(result) {
    this.AccountNo = result.data.value; 
  }

  onChecked(result) {
    this.selectedItem = result; 
    console.log('chkBoxSelected', this.selectedItem);
  }

  async addTransaction() { 
    if (this.formSource === CommonConstant.ANOMALY) {
      if (this.validationAnomaly()) {
        return;
      } 
      this.addAnomalies();
    } else {
      this.addTransactionGroup();
    }
  }

  async addTransactionGroup(){
    const request =  new AddTransactionGroupRequest();
    request.dashboardName = this.dashboardName;
    request.tenantCode = this.global.user.role.tenantCode;
    request.resultDetailIds = this.selectedItem.map(item => item.resultDetailId);
    request.groupName = this.groupName ? this.groupName : request.groupName;
    request.subGroupName = this.subGroupName ? this.subGroupName : request.subGroupName; 

    await this.dashboardService.addTransactionGroup(this.urlAdd,request).toPromise().then(response => {
      if (response["status"]["code"] == 0) {
        this.toastrService.success('Group successfully added!', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        this.result.emit();
        this.closeModal();
      } 
    }); 
  }

  async addAnomalies(){
    const request =  new AddAnomalyRequest();
    request.dashboardName = this.dashboardName;
    request.tenantCode = this.global.user.role.tenantCode;
    request.anomalies = this.selectedItem.map(item => 
      new AnomaliesContainer(item.resultDetailId, item.reason)
    ); 

    await this.dashboardService.addAnomaly(request).toPromise().then(response => {
      if (response["status"]["code"] == 0) {
        this.toastrService.success('Anomalies successfully added!', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        this.result.emit();
        this.closeModal();
      } 
    }); 
  }

  validationAnomaly() {
    let hasError = false; 
    this.selectedItem.forEach(match => {
      if (match.reason === undefined && !hasError) {
        this.toastrService.error('Please select the reason for all selected data!', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        hasError = true; 
      }
    }); 
    return hasError;
  }
 
  onScroll(event: Event) {
    const scrollableElement = event.target as HTMLElement;
    // Check if the scrollable element is scrolled down enough
    if (scrollableElement.scrollTop > 200) {  // Customize the scroll threshold
      this.isScrollTopVisible = true;
    } else {
      this.isScrollTopVisible = false;
    }
  }

  scrollToTop() {
    const scrollableElement = document.querySelector('.modal-body') as HTMLElement;
    if (scrollableElement) {
      scrollableElement.scrollTo({
        top: 0,
        behavior: 'smooth'  // Smooth scrolling
      });
    }
  }
 
  closeModal() { 
    this.activeModal.dismiss("cancel");
  }
}
