<div class="tab-background px-3 pb-1">
<!-- Minimal statistics section start -->
  <section id="minimal-statistics">
	<div class="row text-left">
		<div class="col-xl-2 col-lg-6 col-12 pr-2">
			<div class="card card-info" style="border-color: #4ea5d9;">
				<div class="card-body pl-2">
					<div class="media">
						<div class="media-right align-self"> 
						  <div class="float-right">
							<img [src]="iconOpeningBalance" class="icon-card" alt="Opening Balance Icon">
						  </div>
						</div>
						<div class="media-body text-right">
						  <span class="card-title">Opening<br>Balance</span> 
						  <h6 class="card-value pt-2">Rp. {{insightCard.openingBalance | number: '1.2-2'}}</h6>
						</div>  
					</div> 
				</div>
			</div>
		</div>
		<div class="col-xl-2 col-lg-6 col-12 px-2">
			<div class="card card-info" style="border-color: #fe6565;">
				<div class="card-body pl-2">
					<div class="media">
						<div class="media-right align-self"> 
						  <div class="float-right">
							<img [src]="iconEndingBalance" class="icon-card" alt="Ending Balance Icon">
						  </div>
						</div>
						<div class="media-body text-right">
						  <span class="card-title">Ending<br>Balance</span> 
						  <h6 class="card-value pt-2">Rp. {{insightCard.endingBalance | number: '1.2-2'}}</h6>
						</div>  
					</div> 
				</div>
			</div>
		</div> 

		<div class="col-xl-2 col-lg-6 col-12 px-2">
			<div class="card card-info" style="border-color: #64ca64;">
				<div class="card-body pl-2">
					<div class="media">
						<div class="media-right align-self"> 
						  <div class="float-right">
							<img [src]="iconOverallCredits" class="icon-card" alt="Overall Credits Icon">
						  </div>
						</div>
						<div class="media-body text-right">
						  <span class="card-title">Overall<br>Credits</span> 
						  <h6 class="card-value pt-2">Rp. {{insightCard.overallCredits | mask: 'separator':'.'}}</h6>
						</div>  
					</div> 
				</div>
			</div>
		</div>

		<div class="col-xl-2 col-lg-6 col-12 px-2">
			<div class="card card-info" style="border-color: #f5b700;">
				<div class="card-body pl-2">
					<div class="media">
						<div class="media-right align-self"> 
						  <div class="float-right">
							<img [src]="iconOverallDebits" class="icon-card" alt="No. of Credit Trans Icon">
						  </div>
						</div>
						<div class="media-body text-right">
						  <span class="card-title">Overall<br>Debits</span> 
						  <h6 class="card-value pt-2">Rp. {{insightCard.overallDebits | mask: 'separator':'.'}}</h6>
						</div>  
					</div> 
				</div>
			</div>
		</div>  

		<div class="col-xl-2 col-lg-6 col-12 px-2">
			<div class="card card-info" style="border-color: #2a2e4e;">
				<div class="card-body pl-2">
					<div class="media">
						<div class="media-right align-self"> 
						  <div class="float-right">
							<img [src]="iconTrxCredit" class="icon-card" alt="Supplier Amount Icon">
						  </div>
						</div>
						<div class="media-body text-right">
						  <span class="card-title">No. of Credit Trans</span> 
						  <h6 class="card-value pt-2">{{insightCard.creditCount | mask: 'separator':'.'}}</h6>
						</div>  
					</div> 
				</div>
			</div>
		</div>

		<div class="col-xl-2 col-lg-6 col-12 pl-2">
			<div class="card card-info" style="border-color: #387d7a;">
				<div class="card-body pl-2">
					<div class="media">
						<div class="media-right align-self"> 
						  <div class="float-right">
							<img [src]="iconTrxDebit" class="icon-card" alt="No. of Dedit Trans Icon">
						  </div>
						</div>
						<div class="media-body text-right">
						  <span class="card-title">No. of Dedit Trans</span> 
						  <h6 class="card-value pt-2">{{insightCard.debitCount | mask: 'separator':'.'}}</h6>
						</div>  
					</div> 
				</div>
			</div>
		</div>
	</div>
  </section>
<!-- // Minimal statistics section end -->
 
<div class="row text-left">
    <div class="col-md-12 col-lg-6 pr-2" *ngIf="cashFlowChartData">
        <div class="card card-chart my-0">
            <div class="card-body">
                <div class="card-block">
                    <div id="bar-bar1" class="height-400">
                        <canvas  baseChart class="chart" [datasets]="cashFlowChartData" [labels]="MonthChartLabels" [options]="cashflowChartOptions" 
						[colors]="cashFlowChartColors" [legend]="true" [chartType]="cashFlowChartType" (chartHover)="chartHovered($event)" (chartClick)="chartClicked($event)"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

	<div class="col-md-12 col-lg-6 pl-2" *ngIf="transactionCountChartData">
        <div class="card card-chart my-0"> 
            <div class="card-body">
                <div class="card-block">
                    <div class="height-400">
                            <canvas baseChart class="chart" [datasets]="transactionCountChartData" [labels]="MonthChartLabels" [options]="transactionCountChartOptions" [colors]="transactionCountChartColors"
                            [legend]="true" [chartType]="transactionCountChartType" (chartHover)="chartHovered($event)" (chartClick)="chartClicked($event)" ></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>	
 
</div>

<app-general-ending-balance-daily-cash-flow-analysis [stateDashboard]="stateDashboard"></app-general-ending-balance-daily-cash-flow-analysis>
<!-- <app-general-circular-transaction-analysis [stateDashboard]="stateDashboard"></app-general-circular-transaction-analysis> -->
</div>