/* Style for the floating info card */
.glossary-card {
    position: absolute; 
    width: 300px;
    background-color: #fff; 
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 10px;
    z-index: 1000; 
    font-size: 12px;
    border-radius: 10px;
    border-width: 15px 0px 0px 0px;
    border-style: solid;  
    border-color: #f5b700;
}  

.card-chart{
    border-radius: 10px;
    border-width: 19px 0px 0px 0px;
    border-style: solid; 
    box-shadow: none;
    border-color: #387d7a;
}