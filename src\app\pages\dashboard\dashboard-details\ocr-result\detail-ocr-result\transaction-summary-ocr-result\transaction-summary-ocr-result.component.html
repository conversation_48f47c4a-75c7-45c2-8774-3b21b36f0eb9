
    <div class="row">
        <div class="col-12">
          <div class="content-sub-header px-3"  style="margin-top: 15px !important;"> 
            Transaction Summary
          </div> 
          <div class="px-3">*Ending Balance : OCR must be equal to Transaction History</div>
        </div> 
      </div>  
    <div class="card-body pt-0">
      <div class="row">
         <div class="col-12"> 
                <div class="body-table"> 
                    <table class="table mt-3">
                      <thead>
                        <tr>
                          <th>Period</th>
                          <th>Beginning Balance (OCR)</th>
                          <th>Ending Balance (OCR)</th>
                          <th>Ending Balance (Transaction History)</th>
                        </tr>
                      </thead>
                      <tbody>
                        <ng-container *ngFor="let summary of summariesCombined; let i = index"> 
                          <tr>
                            <td [width]="'50'">
                                <p>{{ formatPeriod(summary.period) }}</p>
                            </td>
                            <td [width]="'150'">
                                <input  [(ngModel)]="summary.beginningBalanceOcr"
                                 type="text" 
                                 mask="separator.2" 
                                 thousandSeparator=","
                                 [allowNegativeNumbers]="true"
                                 class="form-control" 
                                 [placeholder]="'0'"  
                                 [required]="true"> 
                                 
                            </td>
                            <td [width]="'150'">
                                <input  [(ngModel)]="summary.endingBalanceOcr"
                                [ngClass]="getCssClass(summary)" 
                                 type="text" 
                                 mask="separator.2" 
                                 [thousandSeparator]="','"  
                                 [allowNegativeNumbers]="true"
                                 class="form-control" 
                                 [placeholder]="'0'"   
                                 [required]="true"> 
                            </td>
                            <td [width]="'150'"> 
                              <p [ngClass]="getCssClass(summary)">{{ summary.endingBalanceHistory | number: '1.2-2' }}</p> 
                            </td> 
                          </tr>
                        </ng-container>
                      </tbody>
                    </table>
                </div>  
         </div>
      </div>
    </div>  