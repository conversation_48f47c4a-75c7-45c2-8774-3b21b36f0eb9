<ngb-alert *ngIf="alertShow"  type="danger" class="mb-2" [dismissible]="false">
    <div class="row">
      <div class="col-lg-8 col-sm-6 d-flex align-items-center"> 
          <div>More than 25% of the transactions are circular. Please review the transactions.<i class="ml-1 ft-info"></i></div>
      </div> 
    </div>
  </ngb-alert>
<app-msx-paging [container]="view" [serviceUrl]="serviceUrl" [buttonList]="buttonList"
    (onBtnClickListener)="onBtnClickListener($event)" (onItemClickListener)="onItemClickListener($event)"></app-msx-paging>