import { AuditContext } from "../audit.context";
import { BaseRequest } from "./base.request";

export class BalanceMutationRequest extends BaseRequest {
    tenantCode: string;
    vendorCode: string;
    balanceType: string;
    transactionType: string;
    transactionDateStart: string;
    transactionDateEnd: string;
    documentType: string;
    referenceNo: string;
    page: number;
    constructor() {
        super();
        this.audit = new AuditContext();
    }
}