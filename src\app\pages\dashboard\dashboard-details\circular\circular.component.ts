import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Dashboard } from 'app/model/dashboard';
import { DashboardService } from 'app/services/api/dashboard.service';
import { FormModel } from 'app/shared/components/ms-form/models';
import { Button } from 'app/shared/components/msx-view/models/Button';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { MsxPagingComponent } from 'app/shared/components/msx-view/msx-paging.component';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { ToastrService } from 'ngx-toastr';
import * as swalFunction from 'app/shared/data/sweet-alerts'; 
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { QuestionDropdown } from 'app/shared/components/ms-form/questions';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { CircularTable } from './circular.list.view';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { AddCircularComponent } from './add-circular/add-circular.component';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { Circular } from 'app/model/circular';
import { AddTransactionGroupComponent } from '../add-transaction-group/add-transaction-group.component';

@Component({
  selector: 'app-circular',
  templateUrl: './circular.component.html',
  styleUrls: ['./circular.component.scss']
})
export class CircularComponent implements OnInit {
  @Input() stateDashboard: Dashboard;
  @Input() isShowAlert: string;
  @Output() isDataChanges = new EventEmitter<boolean>();
  
  view: MsxView;
  buttonList: Button[];
  serviceUrl = URLConstant.ListCircular;
  searchFilter: FormModel<string>;
  consolidateStatus:string; 
  newGroupName:string; 
  swal = swalFunction;
  alertShow:boolean = false;

  @ViewChild(MsxPagingComponent) msxPagingComponent!: MsxPagingComponent;

  constructor(
    private modalService: NgbModal,
    private router: Router,
    private dashboardService: DashboardService,
    private toastrService: ToastrService,
    private global: GlobalService,
    private cdr: ChangeDetectorRef,
  ) { }  

  ngOnInit(): void {
    this.initSearchFilter();
    this.initView();
    this.buttonList = [
      {name: 'Add New Group', class: 'btn btn-primary', hide: false, icon: 'ft-plus'}
    ];
 
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.isShowAlert && changes.isShowAlert.currentValue !== undefined) {
      this.alertShow = this.isShowAlert == '1' ? true : false;
      this.cdr.detectChanges(); // Ensure UI updates
    }
  }  

  initView() {
      this.view = {
        title: ' - ' + CommonConstant.CIRCULAR_TRANSACTION_TITLE,
        titleDynamic: this.stateDashboard.dashboardName,
        isTabTitle: true,
        components: [
          {
            type: WidgetType.SearchFilter,
            component: this.searchFilter
          },
          {
            type: WidgetType.Datatable,
            component: CircularTable
          }
        ]
      }
  }
  
  
  
  initSearchFilter() {
      this.searchFilter = {
        name: 'ocrResultSearchForm',
        direction: FormConstant.DIRECTION_HORIZONTAL,
        autoload: true,
        colSize: 6,
        components: [
          new QuestionDropdown({
            key: 'accountNo',
            label: 'Account No',
            placeholder: 'Select account no',
            serviceUrl: URLConstant.AccountNoList,
            options: [
              {key: '', value: 'All'}
            ],
            value: '',
            args: {
              list: 'accountNos',
              key: 'accountNo',
              value: 'accountNo'
            },
            params: {
              dashboardName: this.stateDashboard.dashboardName,
              tenantCode: this.global.user.role.tenantCode
            }
          }),
          new QuestionDropdown({
            key: 'transactionWindow',
            label: 'Transaction Window',
            placeholder: 'Select Transaction Window',
              serviceUrl: URLConstant.GetLov,
              options: [
                  {key: '', value: 'All'}
              ],
              value: '',
              args: {
                  list: 'lovList',
                  key: 'code',
                  value: 'description'
              },
              params: {
                  lovGroup: CommonConstant.CIRCULAR_TRANSACTION_WINDOW
              }
          })
        ],
        params: [
          {
            key: 'page',
            controlType: FormConstant.TYPE_HIDDEN,
            value: 1
          },
          {
            key: 'tenantCode',
            controlType: 'hidden', 
            value: this.global.user.role.tenantCode 
          },
          {
            key: 'dashboardName',
            controlType: 'hidden', 
            value: this.stateDashboard.dashboardName 
          }
        ]
      }
  }
  
  onBtnClickListener($event) {
    const buttonName = $event['name'];
    if (buttonName == 'Add New Group') {
       this.openAddModal();
    } 
  }
  
  onItemClickListener(event) {
    const data = event['data'];
      switch (event['act']['type']) {
        case Act.View:
         return this.gotoView(data);
        case Act.Delete:
          return this.deleteData(data);
    }
  }

  openAddModal() {
    const modal = this.modalService.open(AddCircularComponent, {
      centered: true,
      backdrop: 'static',
      size: 'm'
    });
    modal.componentInstance.tenantCode = this.global.user.role.tenantCode;
    modal.componentInstance.dashboardName = this.stateDashboard.dashboardName;

    modal.componentInstance.result.subscribe((value: string) => {
      this.newGroupName = value;
      this.goToAddTransaction();
    });
  }

  gotoView(circular: Circular) {
    const states = {
      ...this.stateDashboard,
      groupName : circular.groupName,
      transactionWindow : circular.transactionWindow,
    } 
    this.router.navigate([PathConstant.VIEW_TRANSACTION_CIRCULAR], {state: states});
  }

  deleteData(data: any) {
    this.swal.Confirm('Are you sure? This data will be permanently lost.').then(
      async (result) => {
        if (result.isConfirmed) { 

          const request = { 
            groupName: data.groupName,
            tenantCode: this.global.user.role.tenantCode,
            dashboardName: this.stateDashboard.dashboardName
          };

          this.dashboardService.deleteCircular(request).subscribe(response => {
            if (response.status.code !== 0) {
              return;
            }

            this.toastrService.success('Data successfully deleted!', null, {
              positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
            });

            this.msxPagingComponent.refreshSearch(); 
            this.isDataChanges.emit(true);
            this.cdr.detectChanges();
          })
        }
      }
    );
  }

  goToAddTransaction(){  
    const modal = this.modalService.open(AddTransactionGroupComponent, {
      centered: true,
      backdrop: 'static',
      size: 'xl'
    });  
    modal.componentInstance.formSource = CommonConstant.CIRCULAR;
    modal.componentInstance.dashboardName = this.stateDashboard.dashboardName;
    modal.componentInstance.groupName = this.newGroupName;
    modal.componentInstance.urlAdd = URLConstant.AddCircular;

    modal.componentInstance.result.subscribe(() => {
       this.msxPagingComponent.refreshSearch(); // Refresh data 
       this.isDataChanges.emit(true);
    }); 
  }
}
