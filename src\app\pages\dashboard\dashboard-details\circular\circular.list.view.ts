import { Circular } from "app/model/circular";
import { Act } from "app/shared/components/msx-datatable/enums/act";
import { Align } from "app/shared/components/msx-datatable/enums/align";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { CommonConstant } from "app/shared/constant/common.constant";

export const CircularTable: Table<Circular> = {
    name: 'circulars',  
    list: [],
    columns: [
        {
            type: ColumnType.Number,
            prop: 'num',
            label: 'No',
            width: 50
        },
        {
            type: ColumnType.Text,
            prop: 'groupName',
            label: 'Group',
            width: 50
        }, 
        {
            type: ColumnType.Text,
            prop: 'accountNo',
            label: 'Account No',
            width: 50
        },  
        {
            type: ColumnType.Text,
            prop: 'accountName',
            label: 'Account Name',
            width: 50
        },
        {
            type: ColumnType.Number,
            prop: 'transactionWindow',
            label: 'Transaction Window',
            width: 50
        },
        {
            type: ColumnType.Text,
            prop: 'createDate',
            label: 'Created Date',
            width: 50
        },   
        {
            type: ColumnType.Action,
            align: Align.CENTER,
            label: 'Action',
            width: 50,
            action: [  
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: 'ft-eye',
                    descr: 'View Related Transaction',
                    type: Act.View,  
                },
                {
                    class: CommonConstant.TEXT_DANGER,
                    icon: 'ft-trash-2',
                    descr: 'Delete',
                    type: Act.Delete,  
                }
            ]
        }
    ]
}