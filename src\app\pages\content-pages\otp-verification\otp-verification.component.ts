import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'app/services/api/user.service';
import { environment } from 'environments/environment';

@Component({
  selector: 'app-otp-verification',
  templateUrl: './otp-verification.component.html',
  styleUrls: ['./otp-verification.component.scss']
})
export class OtpVerificationComponent implements OnInit {

  @Input() email: string;

  templateForm: any;
  interval: any;
  timeLeft = environment.interval.otp;

  constructor(
    private formBuilder: FormBuilder,
    private userService: UserService,
    private activeModal: NgbActiveModal
  ) { }

  ngOnInit(): void {
    
    this.templateForm = this.formBuilder.group({
      otp: ['', Validators.required]
    });

    const lastEmailInput = localStorage.getItem('oldEmail');
    if (!lastEmailInput) {
      this.sendOtpForgotPassword();
      return;
    }
    
    const lastTimer = localStorage.getItem('timeLeft');

    // Kalau ada email lama dan timer 0
    if (!lastTimer || lastTimer === '-1') {
      this.sendOtpForgotPassword();
      return;
    }

    // Kalau ada email lama, timer > 0, email lama != email input
    if (lastEmailInput.toLowerCase() !== this.email.toLowerCase()) {
      this.sendOtpForgotPassword();
      return;
    }

    // Kalau ada email lama, timer > 0, email lama = email input
    this.timeLeft = Number(localStorage.getItem('timeLeft'));
    this.startTimer();

  }

  sendOtpForgotPassword() {
    this.userService.sendOtpForgotPassword(this.email).subscribe(response => {
      if (response.status.code !== 0) {
        return;
      }

      this.timeLeft = environment.interval.otp;
      clearInterval(this.interval);
      this.startTimer();
    });
  }

  startTimer() {
    this.interval = setInterval(() => {
      if (this.timeLeft > 1) {
        this.timeLeft -= 1;        
      } else {
        this.timeLeft = -1;
      }
    }, 1000);
  }

  onSubmit() {
    const otp = this.templateForm.controls.otp.value;
    if (!otp) {
      return;
    }

    this.userService.verifyOtpForgotPassword(this.email, otp).subscribe(response => {
      if (response.status.code !== 0) {
        return;
      }

      this.activeModal.close(otp);
    });
  }

  dismiss() {
    this.activeModal.dismiss('0');
    clearInterval(this.interval);

    localStorage.setItem('timeLeft', this.timeLeft.toString());
    localStorage.setItem('oldEmail', this.email);
  }

}
