import { Injectable } from "@angular/core";
import { AppService } from "./app.service";
import { OssSignatureResponse } from "app/shared/dto/oss/oss-signature.response";
import { URLConstant } from "app/shared/constant/URLConstant";
import { BaseRequest } from "app/model/api/base.request";

@Injectable({
    providedIn: 'root'
})
export class OssService extends AppService {
    generateSignature() {
        return this.client.post<OssSignatureResponse>(URLConstant.GenerateOssSignature, new BaseRequest());
    }
}