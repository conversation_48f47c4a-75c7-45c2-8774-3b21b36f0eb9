import {Component, Input, OnInit} from '@angular/core';
import {Location} from '@angular/common';

@Component({
  selector: 'app-page-header',
  templateUrl: './page-header.component.html',
  styleUrls: ['./page-header.component.scss']
})
export class PageHeaderComponent implements OnInit {

  @Input() title: string;
  @Input() enableBackBtn = false;

  constructor(private readonly location: Location) { }

  ngOnInit(): void {
    // Intentionally empty
  }

  onBack() {
    this.location.back();
  }

}
