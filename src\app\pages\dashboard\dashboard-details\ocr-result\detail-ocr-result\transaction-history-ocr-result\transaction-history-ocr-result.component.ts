import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { SaveBankStatementTransactionDetailRequest } from 'app/model/api/OcrResult/save-bank-statement-transaction-detail-request';
import { TransactionHistory } from 'app/model/transaction-history';
import { DashboardService } from 'app/services/api/dashboard.service';
import { HttpService } from 'app/services/http.service';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { SearchFilterComponent } from 'app/shared/components/ms-form/widget';
import { MsxDatatableTransactionHistoryComponent } from 'app/shared/components/msx-datatable-transaction-history/msx-datatable-transaction-history.component';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { Align } from 'app/shared/components/msx-datatable/enums/align';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { Page } from 'app/shared/components/msx-datatable/models/page';
import { Table } from 'app/shared/components/msx-datatable/models/table';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { GlobalService } from 'app/shared/data/global.service';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject } from 'rxjs';
import * as swalFunction from 'app/shared/data/sweet-alerts';
import { DeleteBankStatementTransactionDetailRequest } from 'app/model/api/OcrResult/delete-bank-statement-transaction-detail-request';
import * as moment from 'moment';
import { TransactionSummaryOcrResultComponent } from '../transaction-summary-ocr-result/transaction-summary-ocr-result.component';
import { GetCalculatedListBankStatementSummaryRequest } from 'app/model/api/OcrResult/get-calculated-list-bank-statement-summary.request';

@Component({
  selector: 'app-transaction-history-ocr-result',
  templateUrl: './transaction-history-ocr-result.component.html',
  styleUrls: ['./transaction-history-ocr-result.component.scss']
})
export class TransactionHistoryOcrResultComponent implements OnInit {
  @Input() state: any;
  @Output() onClickRow = new EventEmitter<any>();

  public WidgetType = WidgetType;
  public CommonConstant = CommonConstant;
  public pageNumber: BehaviorSubject<number> = new BehaviorSubject(1);

  private datasource: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);
  @ViewChild(SearchFilterComponent) searchFilterComponent!: SearchFilterComponent;
  @ViewChild(MsxDatatableTransactionHistoryComponent) msxDatatableTransactionHistoryComponent!: MsxDatatableTransactionHistoryComponent;
  @ViewChild(TransactionSummaryOcrResultComponent) transactionSummaryOcrResultComponent!: TransactionSummaryOcrResultComponent;

  container: MsxView; 
  results: any;
  settings: any;
  url:string;
  isEditable:boolean = false;
  swal = swalFunction; 
  currPage:number = 1;

  searchFormObj:any;
  serviceUrl = URLConstant.GetListBankStatementTransactionDetail;

  constructor(
    private global: GlobalService, 
    public http: HttpService, 
    private toastrService: ToastrService,
    private dashboardService: DashboardService,) {   
  }

  async ngOnInit(): Promise<void> { 
    this.searchFormObj =  {
      name: 'TransactionHistorySearchForm',
      direction: FormConstant.DIRECTION_HORIZONTAL,
      autoload: true,
      colSize: 6,
      components: [ 
          {
              key: 'showStatus',
              label: 'Show Data',
              placeholder: 'Select type',
              controlType: FormConstant.TYPE_DROPDOWN,
              value: '',
              options: [
                  {key: '', value: 'All'},
                  {key: 'RED', value: 'Red'}, 
              ]
          },  
          {
            key: 'type',
            label: 'Type',
            placeholder: 'Select type',
            controlType: FormConstant.TYPE_DROPDOWN,
            value: '',
            options: [
                {key: '', value: 'All'},
                {key: 'CREDIT', value: 'Credit'},
                {key: 'DEBIT', value: 'Debit'}
            ]
          } 
      ],
      params: [ 
        {
          key: 'tenantCode',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.global.user.role.tenantCode 
        },
        {
          key: 'dashboardName',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.state.dashboardName 
        } ,
        {
          key: 'fileSourcePath',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.state.fileSourcePath 
        },
        {
          key: 'page',
          controlType: FormConstant.TYPE_HIDDEN,
          value: 1
        }
      ]
    };
    const TransactionHistoryTable: Table<TransactionHistory> = {
      name: 'results',
      list: [],
      columns: [ 
          {
            type: ColumnType.Date,
            format: CommonConstant.FORMAT_DATE,
            prop: 'date',
            label: 'Date',
            width: 100,
            condition: true,
            conditionVariable: 'dateConfidenceRed',
            conditionExpected: '1',
            conditionedClass: 'text-danger',
            conditionVariable2: 'isEdited',
            conditionExpected2: '0',
          },
          {
            type: ColumnType.Text,
            prop: 'description',
            label: 'Description',
            width: 100,
            condition: true,
            conditionVariable: 'descriptionConfidenceRed',
            conditionExpected: '1',
            conditionedClass: 'text-danger',
            conditionVariable2: 'isEdited',
            conditionExpected2: '0',
          },
          {
            type: ColumnType.Currency,
            prop: 'amount',
            label: 'Amount',
            width: 100,
            condition: true,
            conditionVariable: 'amountConfidenceRed',
            conditionExpected: '1',
            conditionedClass: 'text-danger',
            conditionVariable2: 'isEdited',
            conditionExpected2: '0',
          },
          {
            type: ColumnType.Currency,
            prop: 'endingBalance',
            label: 'Ending Balance',
            width: 100,
            condition: true,
            conditionVariable: 'endingBalanceConfidenceRed',
            conditionExpected: '1',
            conditionedClass: 'text-danger',
            conditionVariable2: 'isEdited',
            conditionExpected2: '0',
            allowNegativeNumber: true
          },
          {
            type: ColumnType.Dropdown,
            prop: 'type',
            label: 'Type',
            width: 100,
            condition: true,
            conditionVariable: 'typeConfidenceRed',
            conditionExpected: '1',
            conditionedClass: 'text-danger',
            conditionVariable2: 'isEdited',
            conditionExpected2: '0',
          }, 
          {
            type: ColumnType.Action,
            label: 'Action',
            width: 100,
            action: [  
              {
                class: CommonConstant.TEXT_PRIMARY,
                icon: 'ft-edit',
                descr: 'Edit',
                type: Act.Edit, 
              },
              {
                class: CommonConstant.TEXT_DANGER,
                icon: 'ft-trash-2',
                descr: 'Delete',
                type: Act.Delete, 
              },
              {
                class: CommonConstant.TEXT_PRIMARY,
                icon: 'ft-check',
                descr: 'Save',
                type: Act.Insert, 
              },
              {
                class: CommonConstant.TEXT_DANGER,
                icon: 'ft-x',
                descr: 'Cancel',
                type: Act.Cancel, 
              }
            ]
        }, 
      ]
    }

    this.container = {
      title: '',
      components: [ 
        {
          type: WidgetType.SearchFilter,
          component: this.searchFormObj
        },
        {
          type: WidgetType.Datatable,
          component: TransactionHistoryTable
        }
      ]
    };
  } 
  
  
  getResult(data) {    
    console.log("data di datatable", data)
    if (data['status'] && data['status']['code'] === 0) {
      this.datasource.next(data); 
      this.msxDatatableTransactionHistoryComponent.isAddClicked = false;
      this.msxDatatableTransactionHistoryComponent.editingRow = null;
    }
    console.log('Result', this.datasource); 
  }

  getPage(page: Page) {
    console.log('requestPage', page);
    this.currPage = page.pageNumber + 1;
    this.getDataSourceByPage(this.currPage);
  }  

  getDataSourceByPage(pageNum: number) { 
    this.pageNumber.next(pageNum);
  }  

  refreshPage(){
    this.getDataSourceByPage(this.currPage);
    this.msxDatatableTransactionHistoryComponent.editingRow = null;
  }

  onItemClick(event:any) {
    const data = event['data'];
    switch (event['act']['type']) {
      case Act.Insert:
        return this.saveData(data); 
      case Act.Delete:
        return this.deleteData(data); 
      case Act.Cancel:
        this.refreshPage();
    }
  }

  onClickedRow(event){
    const info = { 
      boxLocation : event.boxLocation,
      boxPage : event.boxPage,
    }
    this.onClickRow.emit(info); 
  }
 
  formatDate(dateStr: any, format: string) { 
    return moment(dateStr, CommonConstant.FORMAT_DATE_YYYY_MM_DD).isValid() ? moment(dateStr, CommonConstant.FORMAT_DATE_YYYY_MM_DD).format(format) : dateStr;
  } 
 
  async saveData(data:any){ 
    this.url = URLConstant.SaveBankStatementTransactionDetail;
    const request = new SaveBankStatementTransactionDetailRequest();
    request.tenantCode = this.global.user.role.tenantCode;
    request.fileSourcePath = this.state.fileSourcePath;
    request.dashboardName = this.state.dashboardName;
    request.amount = data.amount;
    request.transactionDate = this.formatDate(data.date, CommonConstant.FORMAT_DATE_YYYY_MM_DD);
    request.description = data.description;
    request.endingBalance = data.endingBalance;
    request.transactionType = data.type;

    if(!this.msxDatatableTransactionHistoryComponent.newRow){
      this.url = URLConstant.EditBankStatementTransactionDetail;
      request.resultDetailId = data.resultDetailId;
    }
    
    await this.dashboardService.saveorEditBankStatementTransactionDetail(request, this.url).toPromise().then(
      async (response) => {  
         if (response["status"]["code"] == 0) {
          this.toastrService.success('Data Saved!', null, {
            positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
          }); 
          this.refreshPage();
          this.callGetCalculatedList(data);
        } 
      }
    );
  }   
  
  callGetCalculatedList(data){
    const request = new GetCalculatedListBankStatementSummaryRequest();
    request.tenantCode = this.global.user.role.tenantCode;
    request.fileSourcePath = this.state.fileSourcePath;
    request.dashboardName = this.state.dashboardName; 

    const date = this.formatDate(data.date,'YYYY-MM');
    this.dashboardService.callGetCalculatedListBankStatementSummary(date, request);
  }

  deleteData(data: any) {
    this.swal.Confirm('Are you sure? This Transaction will be permanently lost.').then(
      async (result) => {
        if (result.isConfirmed) {
          const request = new DeleteBankStatementTransactionDetailRequest();
          request.tenantCode = this.global.user.role.tenantCode;
          request.fileSourcePath = this.state.fileSourcePath;
          request.dashboardName = this.state.dashboardName;
          request.resultDetailId = data.resultDetailId; 

          await this.dashboardService.deleteBankStatementTransactionDetail(request).toPromise().then(
            async (response) => {
               if (response['status']['code'] == 0) {
                this.toastrService.success('Data successfully deleted!', null, {
                  positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
                }); 
                this.refreshPage();
                this.callGetCalculatedList(data);
               }
            }
          )
        }
      }
    );
  }
}
