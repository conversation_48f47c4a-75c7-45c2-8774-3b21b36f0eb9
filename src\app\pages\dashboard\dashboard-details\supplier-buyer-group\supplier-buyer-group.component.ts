import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { Dashboard } from 'app/model/dashboard';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { Button } from 'app/shared/components/msx-view/models/Button';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { SupplierBuyerGroupListView, SupplierBuyerGroupSearchFilter } from './supplier-buyer-group.list.view';
import { PlatformLocation } from '@angular/common';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DashboardService } from 'app/services/api/dashboard.service';
import { GlobalService } from 'app/shared/data/global.service';
import { ConfigService } from 'app/shared/services/config.service';
import { ToastrService } from 'ngx-toastr';
import * as swalFunction from 'app/shared/data/sweet-alerts'; 
import { AddSupplierBuyerGroupComponent } from './add-supplier-buyer-group/add-supplier-buyer-group.component';
import { MsxPagingComponent } from 'app/shared/components/msx-view/msx-paging.component';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { ConfigureSubGroupComponent } from './configure-sub-group/configure-sub-group.component';
import { CommonConstant } from 'app/shared/constant/common.constant';

@Component({
  selector: 'app-supplier-buyer-group',
  templateUrl: './supplier-buyer-group.component.html',
  styleUrls: ['./supplier-buyer-group.component.scss']
})
export class SupplierBuyerGroupComponent implements OnInit {
  @Input() stateDashboard: Dashboard;
  @Output() isDataChanges = new EventEmitter<boolean>();
   
  view: MsxView;
  buttonList: Button[];
  serviceUrl = URLConstant.ListSupplierBuyerGroup;
  
  swal = swalFunction;

  @ViewChild(MsxPagingComponent) msxPagingComponent!: MsxPagingComponent;

  constructor(
    private modalService: NgbModal, 
    private router: Router, 
    private platformLocation: PlatformLocation,
    private dashboardService: DashboardService,
    private toastrService: ToastrService,
    private global: GlobalService,
    private cdr: ChangeDetectorRef,
    private configService: ConfigService,) { }

  ngOnInit(): void {
    SupplierBuyerGroupSearchFilter.params.push(
      {
        key: 'tenantCode',
        controlType: 'hidden', 
        value: this.global.user.role.tenantCode 
      },
      {
        key: 'dashboardName',
        controlType: 'hidden', 
        value: this.stateDashboard.dashboardName 
      }
    );
    SupplierBuyerGroupListView.titleDynamic = this.stateDashboard.dashboardName;
    this.view = SupplierBuyerGroupListView;
    
    this.buttonList = [
      {name: 'Add/View Master Sub Group', class: 'btn btn-primary', hide: false, icon: 'ft-plus'},
      {name: 'Add New Group', class: 'btn btn-primary', hide: false, icon: 'ft-plus'}
    ];
  }

  onBtnClickListener($event) {
    const buttonName = $event['name'];
    if (buttonName == 'Add New Group') {
      this.openAddModal();
    } else {
      this.goToMasterSubGroup();
    }
  }

  onItemClickListener(event) {
    const data = event['data'];
    switch (event['act']['type']) {
      case Act.Edit:
       return this.openSubGroupModal(data);
      case Act.Delete:
       return this.deleteData(data);
    }
  }

  goToMasterSubGroup(){
    this.router.navigate([PathConstant.SUPPLIER_BUYER_SUB_GROUP], {state: this.stateDashboard});
  } 

  openAddModal(){
    const modal = this.modalService.open(AddSupplierBuyerGroupComponent, {
      centered: true,
      backdrop: 'static',
      size: 'l'
    });
    modal.componentInstance.tenantCode = this.global.user.role.tenantCode;
    modal.componentInstance.dashboardName = this.stateDashboard.dashboardName;

    modal.componentInstance.result.subscribe(() => {
      this.msxPagingComponent.refreshSearch(); // Refresh data 
      this.isDataChanges.emit(true);
    });
  }

  openSubGroupModal(data:any){
    const modal = this.modalService.open(ConfigureSubGroupComponent, {
      centered: true,
      backdrop: 'static',
      size: 'xl'
    });
    modal.componentInstance.dashboardName = this.stateDashboard.dashboardName; 
    modal.componentInstance.mainGroupName = data.mainGroup; 
    modal.componentInstance.type = data.type; 
    modal.componentInstance.result.subscribe(() => {
      this.msxPagingComponent.refreshSearch(); // Refresh data 
      this.isDataChanges.emit(true);
    });
  }

  deleteData(data: any) {
    this.swal.Confirm('Are you sure? This group will be permanently lost.').then(
      async (result) => {
        if (result.isConfirmed) {
          const request = { 
            mainGroupName: data.mainGroup,
            tenantCode: this.global.user.role.tenantCode,
            dashboardName: this.stateDashboard.dashboardName
          };
          await this.dashboardService.deleteMainSupplierBuyerGroup(request).toPromise().then(
            async (response) => {
               if (response['status']['code'] == 0) {
                this.toastrService.success('Data successfully deleted!', null, {
                  positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
                });
                 this.msxPagingComponent.refreshSearch(); // Refresh data 
                 this.isDataChanges.emit(true);
                // Trigger change detection to update UI
                this.cdr.detectChanges();
               }
            }
          )
        }
      }
    );
  }

}
