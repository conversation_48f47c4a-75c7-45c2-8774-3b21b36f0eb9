import { Transaction } from "app/model/transaction";
import { Act } from "app/shared/components/msx-datatable/enums/act";
import { Align } from "app/shared/components/msx-datatable/enums/align";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table"; 
import { CommonConstant } from "app/shared/constant/common.constant";

export const EditSubGroupTransactionGroupTable: Table<Transaction> = {
    name: 'members',
    list: [],
    columns: [
        {
            type: ColumnType.Number, 
            prop: 'rowNum',
            label: 'No',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'file',
            label: 'File Name',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'accountNo',
            label: 'Account No',
            width: 100
        },  
        {
            type: ColumnType.Text,
            prop: 'date',
            label: 'Date',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'description',
            label: 'Description',
            width: 100
        },
        {
            type: ColumnType.Currency,
            prop: 'amount',
            label: 'Amount',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'type',
            label: 'Type',
            width: 100
        },
        {
            type: ColumnType.Action,
            label: 'Action',
            width: 100,
            action: [  
                {
                    class: CommonConstant.TEXT_DANGER,
                    icon: 'ft-trash-2',
                    descr: 'Delete',
                    type: Act.Delete, 
                }
            ]
        }
    ]
}

