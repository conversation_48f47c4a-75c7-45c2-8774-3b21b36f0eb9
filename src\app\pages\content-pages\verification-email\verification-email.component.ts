import {Component, Input, OnInit} from '@angular/core';
import {NgbActiveModal} from '@ng-bootstrap/ng-bootstrap';
import { FormBuilder, Validators} from '@angular/forms';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import {Router} from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { VerifyResetCodeRequest } from 'app/shared/dto/password/verify-reset-code.request';
import {SendOtpByEmailRequest} from '../../../shared/dto/otp/send-otp-by-email.request';
import {GlobalService} from '../../../shared/data/global.service';
import {CheckOtpByEmailRequest} from '../../../shared/dto/otp/check-otp-by-email.request';
import {PathConstant} from '../../../shared/constant/PathConstant';
import {CommonConstant} from '../../../shared/constant/common.constant';
import {environment} from '../../../../environments/environment';
import { BaseResponse } from 'app/model/api/base.response';

@Component({
  selector: 'app-verification-email',
  templateUrl: './verification-email.component.html',
  styleUrls: ['./verification-email.component.scss']
  
})
export class VerificationEmailComponent implements OnInit {

  @Input() email: string;
  @Input() verificationType: string;
  @Input() fullname: string;
  @Input() msg: string;
  templateForm: any;

  timeLeft = environment.interval.otp;
  interval;

  swal = swalFunction;

  constructor(public activeModal: NgbActiveModal, private formBuilder: FormBuilder, private toastrService: ToastrService, private http: HttpClient, private global: GlobalService) {
  }

  ngOnInit(): void {
    this.templateForm = this.formBuilder.group({
      otp: ['', Validators.required]
    });
    if (localStorage.getItem('timeLeft') != null) {
      this.timeLeft = Number(localStorage.getItem('timeLeft'));
      this.startTimer();
    }
    if (localStorage.getItem('oldEmail') != null) {
      if (this.email.localeCompare(localStorage.getItem('oldEmail'), undefined, { sensitivity: 'base' }) !== 0) {
        this.sendOtp();
      }
    } else {
      this.sendOtp();
    }
  }

  dismiss() {
    this.activeModal.dismiss('0');
    clearInterval(this.interval);
    localStorage.setItem('timeLeft', this.timeLeft.toString());
    localStorage.setItem('oldEmail', this.email);
  }

  openSuccessPopup() {
    this.toastrService.success(`Verifikasi OTP Email berhasil`, null, {
      positionClass: 'toast-top-right'
    });
  }

  openWrongCodePopup() {
    this.swal.Error('Kode OTP tidak sesuai!');
  }

  onSubmit() {
    if (this.verificationType && this.verificationType === 'resetCode') {
      const request = new VerifyResetCodeRequest();
      request.loginId = this.email;
      request.resetCode = this.templateForm.controls.otp.value;
      request.audit = {
        callerId: this.email
      };

      this.http.post<BaseResponse>(URLConstant.VerifyResetCode, request).subscribe(
        (response) => {
          // if (response.status.code !== 0) {
          //   this.swal.Error(response.status.message);
          //   return;
          // }
          // this.openSuccessPopup();
          // this.activeModal.close(request.resetCode);
        }
      );
    }
  }

  sendOtp() {
    // if (this.verificationType && this.verificationType === 'resetCode') {
    //   const request = new ForgotPasswordRequest();
    //   request.audit = {
    //     callerId: this.email
    //   };
    //   request.loginId = this.email;

    //   this.http.post<BaseResponse>(URLConstant.ForgotPassword, request).subscribe(
    //     (response) => {
    //       // if (response.status.code !== 0) {
    //       //   if (response.status.code === 8119) {
    //       //     this.swal.ErrorWithRedirect('Tidak dapat melakukan reset kode akses lagi hari ini', PathConstant.LOGIN);
    //       //   }
    //       //   return;
    //       // }
    //       // this.email = response.recipient;
    //     }
    //   );
    // } else if (!this.verificationType || this.verificationType !== 'resetCode') {
    //   let urlSendOtpByEmail = URLConstant.SendOtpByEmail;
    //   const request = new SendOtpByEmailRequest();
    //   request.audit = { callerId: this.email };
    //   request.loginId = this.email;

    //   this.http.post(urlSendOtpByEmail, request).subscribe(
    //     (response) => {
    //       if (response['status']['code'] !== 0) {
    //         console.log('Error', response);
    //         return;
    //       }
    //     }
    //   );
    // }
    // this.timeLeft = environment.interval.otp;
    // clearInterval(this.interval);
    // this.startTimer();
  }

  startTimer() {
    this.interval = setInterval(() => {
      if (this.timeLeft > 1) {
        this.timeLeft--;
      } else {
        this.timeLeft = -1;
      }
    }, 1000);
  }
}
