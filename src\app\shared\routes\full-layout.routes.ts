import { Routes } from '@angular/router';
import {PermissionGuard} from '../auth/permission.guard';
import {AccessDeniedComponent} from '../../pages/access-denied/access-denied.component';
import { ChangePasswordComponent } from 'app/pages/content-pages/change-password/change-password.component';
import { DashboardComponent } from 'app/pages/dashboard/dashboard.component';
import { HumanInTheLoopComponent } from 'app/pages/human-in-the-loop/human-in-the-loop.component';

// Route for content layout with sidebar, navbar and footer.

export const Full_ROUTES: Routes = [
  {
    path: 'dashboard', 
    children: [
      {
        path: '',
        component: DashboardComponent,
        data: {
          title: 'Dashboard'
        }
      },
      {
        path: '',
        loadChildren: () => import('../../pages/dashboard/dashboard-details/dashboard-details.module').then(m => m.DashboardDetailsModule)
      },
    ]
  },
  {
    path: 'access-denied',
    component: AccessDeniedComponent,
    data: {
      title: 'Access Denied'
    }
  },
  {
    path: 'change-accesscode',
    component: ChangePasswordComponent,
    data: {
      title: 'Change Password',
      showNavbar: false
    }
  },
  {
    path: 'pages',
    loadChildren: () => import('../../pages/full-pages/full-pages.module').then(m => m.FullPagesModule)
  },
  {
    path: 'master',
    loadChildren: () => import('../../pages/master/master.module').then(m => m.MasterModule),
    canActivate: [PermissionGuard]
  },
  {
    path: 'tenant',
    loadChildren: () => import('../../pages/tenant/tenant.module').then(m => m.TenantModule),
    canActivate: [PermissionGuard]
  },
  {
    path: 'hitl',
    component: HumanInTheLoopComponent,
    data: {
      title: 'Human In The Loop'
    }
  }
  // {
  //   path: 'cards',
  //   loadChildren: () => import('../../cards/cards.module').then(m => m.CardsModule)
  // },
  // {
  //   path: 'chat',
  //   loadChildren: () => import('../../chat/chat.module').then(m => m.ChatModule)
  // },
  // {
  //   path: 'chat-ngrx',
  //   loadChildren: () => import('../../chat-ngrx/chat-ngrx.module').then(m => m.ChatNGRXModule)
  // },
  // {
  //   path: 'inbox',
  //   loadChildren: () => import('../../inbox/inbox.module').then(m => m.InboxModule)
  // },
  // {
  //   path: 'taskboard',
  //   loadChildren: () => import('../../taskboard/taskboard.module').then(m => m.TaskboardModule)
  // },
  // {
  //   path: 'taskboard-ngrx',
  //   loadChildren: () => import('../../taskboard-ngrx/taskboard-ngrx.module').then(m => m.TaskboardNGRXModule)
  // }
];
