import { PlatformLocation } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DashboardService } from 'app/services/api/dashboard.service';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { Button } from 'app/shared/components/msx-view/models/Button';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { ConfigService } from 'app/shared/services/config.service';
import { ToastrService } from 'ngx-toastr';
import { OcrResultTable } from './ocr-result.list.view';
import { Dashboard } from 'app/model/dashboard';
import * as swalFunction from 'app/shared/data/sweet-alerts'; 
import { UploadComponent } from '../../upload/upload.component';
import { MsxPagingComponent } from 'app/shared/components/msx-view/msx-paging.component';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { QuestionDropdown } from 'app/shared/components/ms-form/questions';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { DeleteBankStatementRequest } from 'app/model/api/OcrResult/delete-bank-statement-request';
import { CommonConstant } from 'app/shared/constant/common.constant';

@Component({
  selector: 'app-ocr-result',
  templateUrl: './ocr-result.component.html',
  styleUrls: ['./ocr-result.component.scss']
})
export class OcrResultComponent implements OnInit {
  @Input() stateDashboard: Dashboard;
  @Input() isShowAlert: string;
  @Output() isDataChanges = new EventEmitter<boolean>();
  
  view: MsxView;
  buttonList: Button[];
  serviceUrl = URLConstant.ListOcrResult;
  searchFilter: FormModel<string>; 
  swal = swalFunction;
  alertShow : boolean = false;

  @ViewChild(MsxPagingComponent) msxPagingComponent!: MsxPagingComponent;

  constructor(
    private modalService: NgbModal, 
    private router: Router, 
    private platformLocation: PlatformLocation,
    private dashboardService: DashboardService,
    private toastrService: ToastrService,
    private global: GlobalService,
    private cdr: ChangeDetectorRef,
    private configService: ConfigService,) { }
    
  async ngOnInit(): Promise<void> {
    this.initSearchFilter();
    this.initView();
    this.buttonList = [
      {name: 'Add New Bank Statement', class: 'btn btn-primary', hide: false, icon: 'ft-plus'}
    ]; 
 
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.isShowAlert && changes.isShowAlert.currentValue !== undefined) {
      this.alertShow = this.isShowAlert == '1' ? true : false;
      this.cdr.detectChanges(); // Ensure UI updates
    }
  }

  initView() {
    this.view = {
      title: ' Files',
      titleDynamic: this.stateDashboard.dashboardName,
      isTabTitle: true,
      components: [
        {
          type: WidgetType.SearchFilter,
          component: this.searchFilter
        },
        {
          type: WidgetType.Datatable,
          component: OcrResultTable
        }
      ]
    }
  }



  initSearchFilter() {
    this.searchFilter = {
      name: 'ocrResultSearchForm',
      direction: FormConstant.DIRECTION_HORIZONTAL,
      autoload: true,
      colSize: 6,
      components: [
        new QuestionDropdown({
          key: 'bankName',
          label: 'Bank',
          placeholder: 'Select bank',
          serviceUrl: URLConstant.ListBank,
          options: [
            {key: '', value: 'All'}
          ],
          value: '',
          args: {
            list: 'banks',
            key: 'bankName',
            value: 'bankName'
          },
          params: {
            dashboardName: this.stateDashboard.dashboardName,
            tenantCode: this.global.user.role.tenantCode
          }
        }),
        new QuestionDropdown({
          key: 'lovProcessResultCode',
          label: 'Status',
          placeholder: 'Select status',
            serviceUrl: URLConstant.GetLov,
            options: [
                {key: '', value: 'All'}
            ],
            value: '',
            args: {
                list: 'lovList',
                key: 'code',
                value: 'description'
            },
            params: {
                lovGroup: 'PROCESS_STATUS'
            }
        })
      ],
      params: [
        {
          key: 'page',
          controlType: FormConstant.TYPE_HIDDEN,
          value: 1
        },
        {
          key: 'tenantCode',
          controlType: 'hidden', 
          value: this.global.user.role.tenantCode 
        },
        {
          key: 'dashboardName',
          controlType: 'hidden', 
          value: this.stateDashboard.dashboardName 
        }
      ]
    }
  }

  onBtnClickListener($event) {
    const buttonName = $event['name'];
    if (buttonName == 'Add New Bank Statement') {
      this.openAddModal();
    }  
  }

  onItemClickListener(event) {
    const data = event['data'];
    switch (event['act']['type']) {
      case Act.Edit:
       return this.gotoDetail(data);
      case Act.Delete:
       return this.deleteData(data);
    }
  }

  openAddModal() {
    const modal = this.modalService.open(UploadComponent, {
      centered: true,
      backdrop: 'static',
      size: 'xl'
    });
    modal.componentInstance.dashboardName = this.stateDashboard.dashboardName; 
    modal.componentInstance.isBankStatement = true; 
    modal.componentInstance.result.subscribe(() => {
      this.msxPagingComponent.refreshSearch(); // Refresh data 
      this.isDataChanges.emit(true);
    });
  }
  
  gotoDetail(data:any){
    const states = {
      ...this.stateDashboard,
      fileSourcePath: data.fileSourcePath, 
    }
    this.router.navigate([PathConstant.DETAIL_OCR_RESULT], {state: states});
  }

  deleteData(data: any) {
    this.swal.Confirm('Are you sure? This file will be permanently lost.').then(
      async (result) => {
        if (result.isConfirmed) {

          const request = new DeleteBankStatementRequest();
          request.dashboardName = this.stateDashboard.dashboardName;
          request.tenantCode = this.global.user.role.tenantCode;
          request.fileSourcePath = data.fileSourcePath;

          this.dashboardService.deleteBankStatement(request).subscribe(response => {
            if (response.status.code !== 0) {
              return;
            }

            this.toastrService.success('Bank statement successfully deleted!', null, {
              positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
            });

            this.msxPagingComponent.refreshSearch(); 
            this.isDataChanges.emit(true);
            this.cdr.detectChanges();
          })
        }
      }
    );
  } 
 
}
