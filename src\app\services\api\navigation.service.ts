import { Injectable } from '@angular/core';
import { Router, NavigationStart } from '@angular/router';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class NavigationService {
  private previousUrl: string | undefined = undefined;  // Initialize as undefined
  private currentUrl: string | undefined = undefined;   // Initialize as undefined

  // Observable to provide current URL in any component
  currentUrlSubject = new BehaviorSubject<string | undefined>(this.currentUrl);

  constructor(private router: Router) {
    // Subscribe to router events to track the current URL
    this.router.events.subscribe(event => {
      if (event instanceof NavigationStart) {
        this.previousUrl = this.currentUrl; // Store the previous URL
        this.currentUrl = event.url;         // Update the current URL
        this.currentUrlSubject.next(this.currentUrl);  // Emit the new current URL
      }
    });
  }

  // Method to retrieve the previous URL
  getPreviousUrl(): string | undefined {
    return this.previousUrl;
  }

  // Method to retrieve the previous URL
  getCurrentUrl(): string | undefined {
    return this.currentUrl;
  }
}
