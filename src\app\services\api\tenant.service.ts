import { Injectable } from "@angular/core";
import { TenantSettingRequest } from "app/shared/dto/tenant-setting/tenant-setting.request";
import { AppService } from "./app.service";
import { URLConstant } from 'app/shared/constant/URLConstant';
import { UpdateTenantSettingRequest } from "app/shared/dto/tenant-setting/update-tenant-setting.request";
import { BaseResponse } from "app/model/api/base.response";

@Injectable({
    providedIn: 'root'
})
export class TenantService extends AppService {
    getTenantSettings(request: TenantSettingRequest) {
        return this.client.post<BaseResponse>(URLConstant.GetTenantSetting, request);
    }
    
    updateTenantSettings(request: UpdateTenantSettingRequest) {
        return this.client.post<BaseResponse>(URLConstant.UpdateTenantSetting, request);
    }
}
