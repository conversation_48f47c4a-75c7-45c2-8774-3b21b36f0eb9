import {Injectable} from '@angular/core';
import {URLConstant} from 'app/shared/constant/URLConstant';
import {AppService} from './app.service';
import {GetLovRequest} from '../../model/api/get.lov.request';
import {GetLovResponse} from '../../model/api/get.lov.response';
import { GeneralSettingRequest } from 'app/model/api/Data/general-setting.request';
import { GeneralSettingResponse } from 'app/model/api/Data/general-setting.response';

@Injectable({
    providedIn: 'root'
})
export class DataService extends AppService {

    getLov(request: GetLovRequest) {
      return this.client.post<GetLovResponse>(URLConstant.GetLov, request);
    }

    getGeneralSetting(gsCode: string) {
      const request = new GeneralSettingRequest();
      request.generalSettingCode = gsCode;
      return this.client.post<GeneralSettingResponse>(URLConstant.GetGeneralSetting, request);
    }
    
}
