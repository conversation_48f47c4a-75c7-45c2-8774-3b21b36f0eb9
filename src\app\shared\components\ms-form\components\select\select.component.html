<div class="form-group" *ngIf="isVerticalLayout" [formGroup]="form">
  <label [attr.for]="question.key" class="msx-form-label" translate>{{question.label}} <span *ngIf="question.required" class="text-danger" style="font-weight: bold">*</span></label>
  <ng-select class="select2-sm" [formControlName]="question.key" [id]="question.key" [items]="options"
             bindLabel="value" bindValue="key" [placeholder]="question.placeholder | translate" (change)="onSelect($event)">
            </ng-select>
  <ng-container *ngIf="validations">
    <div *ngFor="let validation of validations">
      <div *ngIf="form.get(question.key).touched && form.get(question.key).hasError(validation.type)"
           class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message | translate}}</div>
    </div>
  </ng-container>
</div>

<div class="form-group row" *ngIf="!isVerticalLayout" [formGroup]="form">
  <div class="col-5">
    <label [attr.for]="question.key" class="msx-form-label" translate>{{question.label}} <span *ngIf="question.required" class="text-danger" style="font-weight: bold">*</span></label>
  </div>
  <div class="col-7">
    <ng-select class="select2-sm" [formControlName]="question.key" [id]="question.key" [items]="options"
               bindLabel="value" bindValue="key" [placeholder]="question.placeholder | translate" (change)="onSelect($event)"></ng-select>
    <ng-container *ngIf="validations">
      <div *ngFor="let validation of validations">
        <div *ngIf="form.get(question.key).touched && form.get(question.key).hasError(validation.type)"
             class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message | translate}}</div>
      </div>
    </ng-container>
  </div>
</div>
