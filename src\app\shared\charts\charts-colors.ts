import { CommonConstant } from "../constant/common.constant";

export const oneLineChartColors: Array<any> = [
    {
      type: CommonConstant.LINE_CHART_TYPE,
      borderColor: CommonConstant.NAVY_COLOR,
      borderWidth: 2,
      pointStyle: CommonConstant.LINE,
      pointBackgroundColor: CommonConstant.NAVY_COLOR,
      pointRadius: 1,
      pointHoverRadius: 2,
      fill: false,   
      lineTension: 0.3,
    }, 
]

export const twoBarChartColors: Array<any> = [
  {
    backgroundColor: CommonConstant.BLUE_COLOR,
    borderColor: CommonConstant.BLUE_COLOR,
  },
  {
    backgroundColor: CommonConstant.RED_COLOR, //red ish
    borderColor:  CommonConstant.RED_COLOR, 
  },
]  

export const twoLineChartColors: Array<any> = [
    {
        type: CommonConstant.LINE_CHART_TYPE,
        borderColor: CommonConstant.YELLOW_COLOR,
        borderWidth: 2,
        pointStyle: CommonConstant.LINE,
        pointBackgroundColor: CommonConstant.YELLOW_COLOR,
        pointRadius: 1,
        pointHoverRadius: 2,
        fill: false,    
    }, 
    {
        type: CommonConstant.LINE_CHART_TYPE,
        borderColor: CommonConstant.RED_COLOR,
        borderWidth: 2,
        pointStyle: CommonConstant.LINE,
        pointBackgroundColor: CommonConstant.RED_COLOR,
        pointRadius: 1,
        pointHoverRadius: 2,
        fill: false,    
    }, 
]

export const oneBarOneLineChartColors: Array<any> = [
    {
      type: CommonConstant.LINE_CHART_TYPE,
      borderColor: CommonConstant.RED_COLOR,
      borderWidth: 2,
      pointStyle: CommonConstant.LINE,
      pointBackgroundColor: CommonConstant.RED_COLOR,
      pointRadius: 1,
      pointHoverRadius: 2,
      fill: false,   
      lineTension: 0.3,
      yAxisID:CommonConstant.RIGHT
    }, 
    {
      type: CommonConstant.BAR_CHART_TYPE,   
      backgroundColor: CommonConstant.NAVY_COLOR,
      borderColor: CommonConstant.NAVY_COLOR, 
      borderWidth: 1,
      yAxisID:CommonConstant.LEFT
    }, 
]

export const twoBarOneLineChartColors: Array<any> = [
    {
      type: CommonConstant.LINE_CHART_TYPE,
      borderColor: CommonConstant.YELLOW_COLOR,
      borderWidth: 2,
      pointStyle: CommonConstant.LINE,
      pointBackgroundColor: CommonConstant.YELLOW_COLOR,
      pointRadius: 1,
      pointHoverRadius: 2,
      lineTension: 0.3,
      fill: false,   
    }, 
    {
      type: CommonConstant.BAR_CHART_TYPE,   
      backgroundColor: CommonConstant.BLUE_COLOR,
      borderColor: CommonConstant.BLUE_COLOR, 
      borderWidth: 1,
    },
    {
      type: CommonConstant.BAR_CHART_TYPE,   
      backgroundColor: CommonConstant.RED_COLOR,  
      borderColor:  CommonConstant.RED_COLOR,  
      borderWidth: 1,
    },
]

export const twoBarTwoLineChartColors: Array<any> = [
    {
      type: CommonConstant.LINE_CHART_TYPE,
      borderColor: CommonConstant.YELLOW_COLOR,
      borderWidth: 2,
      pointBackgroundColor: CommonConstant.YELLOW_COLOR,
      pointStyle: CommonConstant.LINE,
      pointRadius: 1,
      pointHoverRadius: 2,
      lineTension: 0.3, 
      fill: false,   
      yAxisID: CommonConstant.RIGHT
    },
    {
      type: CommonConstant.LINE_CHART_TYPE,
      borderColor: CommonConstant.BLACK_COLOR,
      borderWidth: 2,
      pointStyle: CommonConstant.LINE,
      pointBackgroundColor: CommonConstant.BLACK_COLOR,
      pointRadius: 1,
      pointHoverRadius: 2,
      lineTension: 0.3,
      fill: false,   
      yAxisID: CommonConstant.RIGHT
    },
    {
      type: CommonConstant.BAR_CHART_TYPE,   
      backgroundColor: CommonConstant.BLUE_COLOR,
      borderColor: CommonConstant.BLUE_COLOR, 
      borderWidth: 1,
      pointStyle: CommonConstant.RECTANGLE,
      yAxisID: CommonConstant.LEFT
    },
    {
      type: CommonConstant.BAR_CHART_TYPE,   
      backgroundColor: CommonConstant.RED_COLOR,  
      borderColor:  CommonConstant.RED_COLOR,  
      borderWidth: 1,
      pointStyle: CommonConstant.RECTANGLE,
      yAxisID: CommonConstant.LEFT
    },
] 