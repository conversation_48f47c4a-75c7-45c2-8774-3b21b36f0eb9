import { Injectable } from "@angular/core";
import { AppService } from "./app.service";
import { BaseDashboardRequest } from "app/model/api/base.dashboard.request";
import { URLConstant } from "app/shared/constant/URLConstant";

@Injectable({
    providedIn: 'root'
})
export class InsightsService extends AppService {

    getSupplierBuyerGpmWcrLr(request: BaseDashboardRequest) {
        return this.client.post<any>(URLConstant.SupplierBuyerGpmWcrLr, request);
    }

    formatCurrency(amount: number): string {
      return amount.toLocaleString('en-US', { 
        minimumFractionDigits: 2, 
        maximumFractionDigits: 2 
      });
    } 

    formatNumber(amount: number): string {
      return amount.toLocaleString('de-DE', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      });
    }   

    getMaxAbsoluteRoundedValue(dataSets: any[]): number {
      const maxValue = Math.max(
          0, 
          ...dataSets.reduce((acc, dataset) => acc.concat(dataset.data ?? []), []).map(Math.abs)
      );
  
      const magnitude = Math.pow(10, Math.floor(Math.log10(maxValue))); // Get magnitude
      const roundedValue = Math.ceil(maxValue / magnitude) * magnitude; // Round up
      return roundedValue;
  }
  
}