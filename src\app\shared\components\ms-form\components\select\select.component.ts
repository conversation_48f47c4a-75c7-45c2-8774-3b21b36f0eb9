import {ChangeDetectorRef, Component, EventEmitter, Input, NgZone, OnInit, Output} from '@angular/core';
import {QuestionDropdown} from '../../questions';
import {InputComponent} from '../input/input.component'; 
import { HttpService } from 'app/services/http.service';

@Component({
  selector: 'app-select',
  templateUrl: './select.component.html',
  styleUrls: ['./select.component.scss']
})
export class SelectComponent extends InputComponent implements OnInit {

  @Input()
  question: QuestionDropdown;

  @Input()
  options = [];

  @Output()
    // tslint:disable-next-line:max-line-length
  selected: EventEmitter<{prop: string, data: {key: string, value: string}}> = new EventEmitter<{prop: string, data: {key: string, value: string}}>();

  constructor(private readonly http: HttpService, private cdk: ChangeDetectorRef, private ngZone: NgZone) {
    super();
  }

  ngOnInit(): void {
    this.options = this.options.length > 0 ? this.options : this.question.options;
    console.log('Criteria', this.question.criteria);
    if (this.question.serviceUrl) {
      this.http.post(this.question.serviceUrl, this.question.params).subscribe(response => {
        if (response['status']['code'] !== 0) {
          return;
        }

        const opts = [];
        const list: any[] = response[this.question.args.list];
        for (const item of list) {
          const option = {
            key: item[this.question.args.key],
            value: item[this.question.args.value]
          };

          opts.push(option);
        }

        this.options = [...this.options, ...opts];

        // Auto select options when length == 1
        if (this.options.length === 1) {
          this.form.get(this.question.key).setValue(this.options[0]['key']);
          this.form.get(this.question.key).updateValueAndValidity();
          console.log(this.form);
        }

        this.cdk.detectChanges();
      })
    }

    this.checkLayoutDirection();
  }

  onSelect(result) {
    console.log('onSelect', result);
    this.selected.emit({prop: this.question.key, data: result});
  }

}
