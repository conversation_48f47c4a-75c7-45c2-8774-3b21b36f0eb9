import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { InsightHeader } from 'app/model/insight-header';
import { DashboardService } from 'app/services/api/dashboard.service';
import { GlobalService } from 'app/shared/data/global.service';
import { Dashboard } from 'app/model/dashboard'; 
import { UtilsService } from 'app/services/api/utils.service'; 
import { twoBarChartColors, twoBarTwoLineChartColors } from 'app/shared/charts/charts-colors';
import { InsightsService } from 'app/services/api/insights.service';

@Component({
  selector: 'app-general-dashboard-insight',
  templateUrl: './general-dashboard-insight.component.html',
  styleUrls: ['./general-dashboard-insight.component.scss']
})
export class GeneralDashboardInsightComponent implements OnInit {
  @Input() stateDashboard: Dashboard; 

  insightCard: InsightHeader = {
    openingBalance: 0,
    endingBalance: 0,
    overallCredits: 0,
    overallDebits: 0,
    creditCount: 0,
    debitCount: 0,
  };

  //icons
  iconOpeningBalance:string = CommonConstant.ICON_GENERAL_OPENING_BALANCE;
  iconEndingBalance:string = CommonConstant.ICON_GENERAL_ENDING_BALANCE;
  iconOverallCredits:string = CommonConstant.ICON_GREEN_DOLLAR_SIGN_IN;
  iconOverallDebits:string = CommonConstant.ICON_YELLOW_DOLLAR_SIGN_OUT;
  iconTrxCredit:string = CommonConstant.ICON_GENERAL_NO_CREDIT;
  iconTrxDebit:string = CommonConstant.ICON_GENERAL_NO_DEBIT;

  MonthChartLabels:any[] = [];

  //Monthly Cash Flow Analysis
  cashFlowChartType = CommonConstant.BAR_CHART_TYPE; 
  cashFlowChartColors = twoBarTwoLineChartColors; 
  cashflowChartOptions:any = {}; 
  cashFlowChartData:any[];
  cashFlowDetails: any = {};   

  //Transaction Type Count Anaylsis
  transactionCountChartType = CommonConstant.BAR_CHART_TYPE; 
  transactionCountChartData:any[]; 
  transactionCountChartOptions:any = {}; 
  transactionCountChartColors = twoBarChartColors;   

  isLoaded : boolean = false;

  constructor(private global: GlobalService, private insightService: InsightsService,
    private dashboardService: DashboardService, private cdr: ChangeDetectorRef,) { }

  async ngOnInit(): Promise<void> {
    //hit api
    await this.generalInsightHeader();
    await this.generalCashFlow();

    //setting chart
    this.setupCashflowChartOptions();
    this.setupTransactionCountChartOptions();

    this.cdr.detectChanges();
    this.isLoaded = true;
  } 
  async generalInsightHeader(){
    const request = {
      tenantCode : this.global.user.role.tenantCode,
      dashboardName : this.stateDashboard.dashboardName,
    };
    await this.dashboardService.generalInsightHeader(request).toPromise().then(
      (response) => {
        if (response['status']['code'] == 0) {
           this.insightCard.openingBalance = response.openingBalance; 
           this.insightCard.endingBalance = response.endingbalance; 
           this.insightCard.overallCredits = response.overallCredit; 
           this.insightCard.overallDebits = response.overallDebit; 
           this.insightCard.creditCount = response.creditCount; 
           this.insightCard.debitCount = response.debitCount; 
        }
      }
    )
  }

  async generalCashFlow(){
    const request = {
      tenantCode : this.global.user.role.tenantCode,
      dashboardName : this.stateDashboard.dashboardName,
    };
    await this.dashboardService.generalCashFlow(request).toPromise().then(
      (response) => {
        if (response['status']['code'] == 0) {
           this.MonthChartLabels = response.labels;
           this.cashFlowChartData = response.cashFlow;
           this.transactionCountChartData = response.transactionCount;
           this.mapDataToAdditionalData(response.cashFlowDetails);
        }
      }
    )
  } 

  mapDataToAdditionalData(cashFlowDetails:any) {
    cashFlowDetails.forEach(item => {
      this.cashFlowDetails[item.period] = {
        netCash: this.insightService.formatCurrency(item.netCash),
        totalCreditAmount: this.insightService.formatCurrency(item.totalCreditAmount),
        averageCreditAmount: this.insightService.formatCurrency(item.averageCreditAmount),
        frequencyCreditDays: item.frequencyCreditDays,
        totalDebitAmount: this.insightService.formatCurrency(item.totalDebitAmount),
        averageDebitAmount: this.insightService.formatCurrency(item.averageDebitAmount),
        frequencyDebitDays: item.frequencyDebitDays,
        highestBalance: this.insightService.formatCurrency(item.highestBalance), 
        lowestBalance: this.insightService.formatCurrency(item.lowestBalance), 
        dailyAverageBalance: this.insightService.formatCurrency(item.dailyAverageBalance), 
        endingBalance: this.insightService.formatCurrency(item.endingBalance)
      };
    }); 
  }
 
  setupCashflowChartOptions(){  
    const cashFlowDetailsData =  this.cashFlowDetails; 
    
    // Separate data for left and right axes
    const rightAxisData = this.cashFlowChartData.filter(d => d.label === "Net Cash" || d.label === "Ending Balance");
    const leftAxisData = this.cashFlowChartData.filter(d => d.label === "Cash In" || d.label === "Cash Out");

    const rightMaxValue = this.insightService.getMaxAbsoluteRoundedValue(rightAxisData);
    const leftMaxValue = this.insightService.getMaxAbsoluteRoundedValue(leftAxisData);

    this.cashflowChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        datalabels: {
          display: false,
          color: '#000', // Label text color
          align: CommonConstant.TOP,  // Align labels above the data points
          anchor: 'end', // Anchor labels to the end of the data point
          formatter: (value: number) => value.toFixed(2), // Format values as needed
          font: {
            size: 10,
            weight: 'bold'
          }
        }
      },
      scales: {
        xAxes: [{ 
          stacked: true ,
          scaleLabel: {
            display: true,
            labelString: 'Year-Month'
          }
        }],
        yAxes: [
          {
          id: CommonConstant.LEFT,
          beginAtZero: false, 
          gridLines: {
            drawOnChartArea: true, 
          }, 
          ticks: {   
            min:-leftMaxValue,
            max: leftMaxValue,
            alignToPixels: true,
            callback: function (value: number) {
              return UtilsService.abbreviateNumber(value); // Display absolute values on Y-axis
            } 
          },
          scaleLabel: {
            display: true,
            labelString: 'Cash In/Out (IDR)',
            fontColor: 'black'
          }, 
        },
        { 
          id: CommonConstant.RIGHT,
          position: CommonConstant.RIGHT,
          gridLines: {
            drawOnChartArea: false // Prevent this axis from drawing grid lines
          },
          beginAtZero: false,
          scaleLabel: {
            display: true,
            labelString: 'Balance (IDR)',
            fontColor: 'black'
          }, 
          ticks: {   
            min: -rightMaxValue,
            max: rightMaxValue,
            alignToPixels: true,
            callback: function (value: number) {
              return UtilsService.abbreviateNumber(value); // Display absolute values on Y-axis
            } 
          },
        } ]
      },
      legend: {
        display: true,
        position: CommonConstant.TOP, 
        padding: 20,
        labels: {
          usePointStyle: true,
          borderRadius: 10,
        }
      },
      tooltips: {
        callbacks: {
          title: function (tooltipItems: any) {
            // Get the label of the hovered bar
            const label = tooltipItems[0].xLabel;
            return `${label}`;
          },
          beforeLabel: function (tooltipItem: any) {
            // Get additional data for the hovered period
            const label = tooltipItem.xLabel;
            const data = cashFlowDetailsData[label];
    
            // Format and return detailed information
            return [
              `Net Cash                                         : ${data.netCash}`,
              `Total Credit Amount                        : ${data.totalCreditAmount}`,
              `Average Credit Amount                   : ${data.averageCreditAmount}`,
              `Freq (days) of Credit Transactions : ${data.frequencyCreditDays}`,
              `Total Debit Amount                         : ${data.totalDebitAmount}`,
              `Average Debit Amount                   : ${data.averageDebitAmount}`,
              `Freq (days) of Debit Transactions  : ${data.frequencyDebitDays}`, 
              `Highest Balance                             : ${data.highestBalance}`,
              `Lowest Balance                              : ${data.lowestBalance}`,
              `Daily Average Balance                   : ${data.dailyAverageBalance}`,
              `Ending Balance                              : ${data.endingBalance}`
            ];
          },
          label: function (tooltipItem: any) {
            return ''; // Return an empty label to suppress the default value
          }
        }
      },
      title: {
        display: true,
        fontColor: 'black',   
        fontSize: 14,   
        fontStyle: 'bold',  
        text: 'Monthly Cash Flow Analysis'
      },
    };
  }

  setupTransactionCountChartOptions(){ 
    this.transactionCountChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      legend: {
        position: CommonConstant.TOP, 
      },
      plugins: { 
        datalabels: { 
          color: '#000', // Label color
          font: {
            weight: 'bold'
          },
          anchor: 'end',
          align: 'end',
          clamp: true,
        },
      },
      scales: {
        xAxes: [{
          display: true,
          scaleLabel: {
            display: true,
            labelString: 'Year-Month'
          }
        }],
        yAxes: [
          {
            id: 'netcash', // Left Y-axis
            type: 'linear',
            position: 'left',
            scaleLabel: {
              display: true,
              labelString: 'Transaction Count',
              fontColor: 'black'
            },
            ticks: {
              fontColor: 'black',
              callback: function (value: number) {
                return UtilsService.abbreviateNumber(value); // Display absolute values on Y-axis
              } 
            },
          }
        ]
      },
      title: {
        display: true,
        fontColor: 'black',   
        fontSize: 14,   
        fontStyle: 'bold',  
        text: 'Transaction Type Count Anaylsis'
      }, 
      
    };
  };

  chartClicked(e: any): void {
    //your code here
  }

  chartHovered(e: any): void {
    //your code here
  }

} 