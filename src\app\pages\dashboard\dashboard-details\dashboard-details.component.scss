.tab-header-content {
    background-color:#FFFFFF;
    padding: 0; 
}

::ng-deep .mat-tab-label{  
    height: 78px !important; 
    user-select: none;
} 

::ng-deep .mat-tab-label-content{
    color: #13111b; 
    font-size: 14px;
    white-space: normal !important;
    word-break: break-word;
    text-align: center; 
} 

::ng-deep .mat-tab-body-content{
    overflow: hidden !important;
} 

/* Prevent clipping when tab is active*/
::ng-deep .mat-tab-label-container{
    overflow: visible !important;
}

::ng-deep .mat-tab-header {  
    overflow: visible !important;  
} 
/* Prevent clipping when tab is active*/

/* Adjust tab active */
::ng-deep .mat-tab-label-active{ 
    background-color: #ffffff;
    font-weight: 500;
    border-radius: 10px 10px 0px 0px;
    position: relative;
    bottom: 8px; /* Move the active tab upwards */  
    box-shadow: rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;
} 

::ng-deep .mat-tab-label-active .mat-tab-label-content{
    color: #2B57BB;  
} 

  /* Adjust tab active line */
  ::ng-deep .mat-tab-group .mat-ink-bar {
    height: 8px !important;  
    background-color: #2B57BB;
  } 
  /* Adjust tab active line */
/* Adjust tab active */

.high-warning { 
  color: #fc0505; 
}

.medium-warning { 
    color: #fcaa05; 
}