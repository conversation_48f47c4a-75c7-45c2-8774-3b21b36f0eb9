import { BankStatement } from "app/model/bank-statement";
import { Act } from "app/shared/components/msx-datatable/enums/act";
import { Align } from "app/shared/components/msx-datatable/enums/align";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table"; 
import { CommonConstant } from "app/shared/constant/common.constant";

export const ConsolidatedBankStatementTable: Table<BankStatement> = {
    name: 'bankStatements',  
    list: [],
    columns: [
        {
            type: ColumnType.Number, 
            prop: 'rowNum',
            label: 'No',
            width: 50
        },
        {
            type: ColumnType.Text,
            prop: 'filename',
            label: 'File',
            width: 50
        },
        {
            type: ColumnType.Text,
            prop: 'bank',
            label: 'Bank',
            width: 50
        },  
        {
            type: ColumnType.Text,
            prop: 'accountNo',
            label: 'Account No',
            width: 50
        },  
        {
            type: ColumnType.Text,
            prop: 'accountName',
            label: 'Account Name',
            width: 50
        } 
    ]
}

