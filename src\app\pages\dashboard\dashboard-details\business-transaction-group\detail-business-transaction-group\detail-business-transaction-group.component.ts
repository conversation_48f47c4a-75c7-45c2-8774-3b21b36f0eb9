import { ChangeDetector<PERSON><PERSON>, Component, <PERSON>Zone, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { HttpService } from 'app/services/http.service';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { Page } from 'app/shared/components/msx-datatable/models/page';
import { GlobalService } from 'app/shared/data/global.service';
import * as swalFunction from 'app/shared/data/sweet-alerts'; 
 import { BehaviorSubject } from 'rxjs';
import { FormModel } from 'app/shared/components/ms-form/models';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { QuestionTextbox } from 'app/shared/components/ms-form/questions';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { PathConstant } from 'app/shared/constant/PathConstant'; 
import { ToastrService } from 'ngx-toastr';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DashboardService } from 'app/services/api/dashboard.service';
import { AddTransactionGroupComponent } from '../../add-transaction-group/add-transaction-group.component';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { DetailBusinessTransactionGroupTable } from './detail-business-transaction-group.list-view';

@Component({
  selector: 'app-detail-business-transaction-group',
  templateUrl: './detail-business-transaction-group.component.html',
  styleUrls: ['./detail-business-transaction-group.component.scss']
})
export class DetailBusinessTransactionGroupComponent implements OnInit {
  table = DetailBusinessTransactionGroupTable;
  datasource: BehaviorSubject<any> = new BehaviorSubject<any>([]);
  formObj: FormModel<any>;
  msxForm: FormGroup;
  state: any;
  dashboardName: string;
  swal = swalFunction;

  title: string = CommonConstant.BUSINESS_TRANSACTION_TITLE + ' - ' + CommonConstant.EDIT_GROUP_TRANSACTION_TITLE;

  constructor(
    private global: GlobalService,
    private router: Router,
    public http: HttpService,
    private ngZone: NgZone,
    private cdr: ChangeDetectorRef,
    private modalService: NgbModal, 
    //public activeModal: NgbActiveModal,
    private toastrService: ToastrService,
    private dashboardService: DashboardService,
    private readonly formBuilder: FormBuilder,) { 
      this.state = this.router.getCurrentNavigation().extras?.state; 
    }

    async ngOnInit() {  
    if(!this.state){
      this.router.navigate([PathConstant.DASHBOARD]);
    }
    this.dashboardName = this.state.dashboardName;

      this.msxForm = this.formBuilder.group({
        groupName: [this.state.groupName || '', [Validators.required, Validators.maxLength(64)]],
      });
      this.setupQuestion(); 

      //table
      await this.getListDetailBusinessTransactionGroup().then(() => {
        this.ngZone.run(() => {
          this.cdr.markForCheck();
        })
      });
    }

  async getPage(page: Page) {
    console.log('requestPage', page);
    await this.getListDetailBusinessTransactionGroup(page.pageNumber + 1).then(() => {
      this.ngZone.run(() => {
        this.cdr.markForCheck();
      })
    });
  }

  async getListDetailBusinessTransactionGroup(pageNumber: number = 1) {
    const request = { 
      groupName: this.state.groupName,
      dashboardName: this.state.dashboardName,
      tenantCode: this.global.user.role.tenantCode,
      page: pageNumber
    };
    await this.dashboardService.getListDetailBusinessTransactionGroup(request).toPromise().then(
      (response) => {
        if (response['status']['code'] == 0) {
          this.datasource.next(response);
        }
        this.cdr.detectChanges();
      }
    )
  }

  goToAddTransaction(){  
      const modal = this.modalService.open(AddTransactionGroupComponent, {
        centered: true,
        backdrop: 'static',
        size: 'xl'
      });  
      modal.componentInstance.formSource = CommonConstant.NON_BUSINESS;
      modal.componentInstance.dashboardName = this.dashboardName;
      modal.componentInstance.groupName = this.state.groupName;
      modal.componentInstance.urlAdd = URLConstant.AddDetailBusinessTransactionGroup;
  
      modal.componentInstance.result.subscribe(() => {
         this.getListDetailBusinessTransactionGroup(); // Refresh data  
      }); 
  }
  
  goBack() { 
    this.router.navigate([PathConstant.DASHBOARD_DETAIL], { 
      state: { 
        state: this.state, 
        indexTab: CommonConstant.BUSINESS_TRANSACTION_TAB_INDEX
      }
    });
  }
  
  setupQuestion() { 
    this.formObj = {
      name: 'groupNameForm',
      mode: CommonConstant.MODE_EDIT,
      colSize: 6,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        new QuestionTextbox({
          key: 'groupName',
            label: 'Group Name',
            placeholder: 'Type group name here',
            maxLength: 64,
            required: true,
            readonly: true,
            validations: [
              {type: 'required', message: 'Group Name cannot be empty'},
              {type: 'maxlength', message: 'The maximum number of characters for the Group Name Name is 64'}
            ]
        }),
      ],
      params: []
    }
  }

  getQuestionkey(key:string) {
    return this.formObj.components.find(x => x.key === key);
  }

   
  onItemClickListener(event) {
    const data = event['data'];
    switch (event['act']['type']) {
      case Act.Delete:
        return this.deleteData(data);
    }

  }

  deleteData(data: any) {
    this.swal.Confirm('Are you sure? This data will be permanently lost.').then(
      async (result) => {
        if (result.isConfirmed) {
          const request = { 
            groupName: this.state.groupName,
            detailId: data.detailId,
            tenantCode: this.global.user.role.tenantCode,
            dashboardName: this.state.dashboardName
          };
          await this.dashboardService.deleteDetailBusinessTransactionGroup(request).toPromise().then(
            async (response) => {
               if (response['status']['code'] == 0) {
                this.toastrService.success('Data successfully deleted!', null, {
                  positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
                });
                await this.getListDetailBusinessTransactionGroup(); // Refresh data 
                // Trigger change detection to update UI
                this.cdr.detectChanges();
               }
            }
          )
        }
      }
    );
  }

}

