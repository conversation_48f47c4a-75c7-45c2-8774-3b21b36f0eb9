import { ChangeDetectorRef, Component, EventEmitter, Input, NgZone, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Act } from 'app/shared/components/msx-datatable/enums/act'; 
import { PathConstant } from 'app/shared/constant/PathConstant';
import { NonBusinessTransactionGroupTable } from './non-business-transaction-group.list.view';
import { BehaviorSubject } from 'rxjs'; 
import { GetListNonBusinessTransactionGroupRequest } from 'app/model/api/NonBusinessTransactionGroup/get-list-non-business-transaction-group.request';
import { GlobalService } from 'app/shared/data/global.service';
import { Dashboard } from 'app/model/dashboard';
import { Page } from 'app/shared/components/msx-datatable/models/page';
import { HttpService } from 'app/services/http.service';
import { AddNonBusinessTransactionGroupComponent } from './add-non-business-transaction-group/add-non-business-transaction-group.component';
import * as swalFunction from 'app/shared/data/sweet-alerts';
import { ToastrService } from 'ngx-toastr';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { DashboardService } from 'app/services/api/dashboard.service';

@Component({
  selector: 'app-non-business-transaction-group',
  templateUrl: './non-business-transaction-group.component.html',
  styleUrls: ['./non-business-transaction-group.component.scss']
})
export class NonBusinessTransactionGroupComponent implements OnInit {  
  @Input() stateDashboard: Dashboard;
  @Output() isDataChanges = new EventEmitter<boolean>();

  table = NonBusinessTransactionGroupTable;
  datasource: BehaviorSubject<any> = new BehaviorSubject<any>([]);
  swal = swalFunction; 

  title: string = CommonConstant.NON_BUSINESS_TRANSACTION_TITLE;

  constructor(
    private global: GlobalService,
    private router: Router, 
    public http: HttpService,
    private ngZone: NgZone, 
    private cdr: ChangeDetectorRef,
    private modalService: NgbModal,
    private dashboardService: DashboardService,
    private toastrService: ToastrService,) { }

    async ngOnInit() { 
      await this.refreshPage();
    }

  async refreshPage(){
    await this.getListNonBusinessTransactionGroup().then(() => {
      this.ngZone.run(() => {
        this.cdr.markForCheck();
      })
    });
  }

  async getPage(page: Page) {
    console.log('requestPage', page);
    await this.getListNonBusinessTransactionGroup(page.pageNumber + 1).then(() => {
      this.ngZone.run(() => {
        this.cdr.markForCheck();
      })
    });
  }  
  
  async getListNonBusinessTransactionGroup(pageNumber: number = 1) {  
    const request = new GetListNonBusinessTransactionGroupRequest();
    request.page = pageNumber;
    request.tenantCode = this.global.user.role.tenantCode;
    request.dashboardName = this.stateDashboard.dashboardName; 

    await this.dashboardService.getListNonBusinessTransactionGroup(request).toPromise().then(
      (response) => {
        if (response['status']['code'] == 0) {
          this.datasource.next(response);
        }
      }
    )
  } 

  onBtnClick(){
    this.openAddModal();
  }

  onItemClickListener(event) {  
    const data = event['data'];
    const combinedData = Object.assign({}, this.stateDashboard, data);
    switch (event['act']['type']) {
      case Act.Edit:
        return this.gotoDetail(combinedData);
      case Act.Delete:
        return this.deleteData(data);
    }

  }

  gotoDetail(state: Dashboard) {
    this.router.navigate([PathConstant.DETAIL_NON_BUSINESS_TRANSACTION_GROUP], {state: state});
  }

  deleteData(data: any) {
    this.swal.Confirm('Are you sure? This group will be permanently lost.').then(
      async (result) => {
        if (result.isConfirmed) {
          const request = { 
            groupName: data.groupName,
            tenantCode: this.global.user.role.tenantCode,
            dashboardName: this.stateDashboard.dashboardName
          };
          await this.dashboardService.deleteNonBusinessTransactionGroup(request).toPromise().then(
            async (response) => {
               if (response['status']['code'] == 0) {
                this.toastrService.success('Group successfully deleted!', null, {
                  positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
                });
                await this.getListNonBusinessTransactionGroup(); // Refresh data 
                this.isDataChanges.emit(true);
                // Trigger change detection to update UI
                this.cdr.detectChanges();
               }
            }
          )
        }
      }
    );
  }

  openAddModal() {
    const modal = this.modalService.open(AddNonBusinessTransactionGroupComponent, {
      centered: true,
      backdrop: 'static',
      size: 'm'
    });
    modal.componentInstance.tenantCode = this.global.user.role.tenantCode;
    modal.componentInstance.dashboardName = this.stateDashboard.dashboardName;

    modal.componentInstance.transactionGroupAdded.subscribe(() => {
      this.getListNonBusinessTransactionGroup(); // Refresh data 
      this.isDataChanges.emit(true);
      this.cdr.detectChanges();
    });
  }
 

}
