  <div class="modal-header">
    <h4 class="modal-title">Add Transaction</h4>
    <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div> 
 <div class="m-content p-3"> 
  
  <ng-container>
    <app-search-filter-transaction  [formObj]="searchFormObj" [service]="serviceUrl" [pageNumber]="pageNumber" (result)="getResult($event)" (form)="onForm($event)" (selectX)="onSelect($event)"></app-search-filter-transaction>
    <div class="row pb-1">
      <div class="col-6">
       </div>
      <div class="col-6 text-right"> 
         <div *ngIf="selectedItem.length > 0">
             <a class="msx-action" class="btn btn-primary" (click)="addTransaction()">
               <span>({{selectedItem.length}}) </span>
               <i class="ft-plus"></i>   {{'Add'| translate}}</a>
         </div>
      </div>
    </div>
    <div class="modal-body px-0" (scroll)="onScroll($event)"> 
      <ng-container *ngIf="formSource != CommonConstant.ANOMALY">
        <app-msx-datatable [tableObj]="AddTransactionGroupTable" [datasource]="datasource" (getPage)="getPage($event)" (itemClickListener)="onItemClick($event)" (selected)="onChecked($event)"></app-msx-datatable>
      </ng-container>
      <ng-container *ngIf="formSource == CommonConstant.ANOMALY">
        <app-msx-datatable-v2 [tableObj]="AddTransactionGroupTable" [datasource]="datasource" (getPage)="getPage($event)" (itemClickListener)="onItemClick($event)" (selected)="onChecked($event)"></app-msx-datatable-v2>
      </ng-container>
      <button class="btn btn-primary scroll-top" type="button" *ngIf="isScrollTopVisible" (click)="scrollToTop()"><i class="ft-arrow-up"></i></button>
    </div> 
   </ng-container>     
</div> 
   