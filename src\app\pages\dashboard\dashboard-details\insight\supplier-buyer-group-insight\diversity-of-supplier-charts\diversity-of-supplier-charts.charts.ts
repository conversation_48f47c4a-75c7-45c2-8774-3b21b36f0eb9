import { CommonConstant } from "app/shared/constant/common.constant";

const basePieChartOptions: any = {
  animation: false,
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    datalabels: {
      formatter: (value, context) => {
        const total = context.dataset.data.reduce((sum: number, val: number) => sum + val, 0);
        const percentage = ((value / total) * 100).toFixed(1);
        return `${percentage}%`; // Display percentage only
      },
      color: '#fff', // Label color
      font: {
        size: 14,
        weight: 'bold'
      },
      anchor: 'center',
      align: 'center'
    },
  },
  legend: {
    display: true,
    position: 'right',
  }
};
  
export const pieChartOptionsByAmount: any = {
  ...basePieChartOptions,
  tooltips: {
    callbacks: {
      label: (tooltipItem, data) => {
        const dataset = data.datasets[tooltipItem.datasetIndex];
        const value = dataset.data[tooltipItem.index];
        const valueFormatted = value.toLocaleString('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        });
        return `${data.labels[tooltipItem.index]}: ${valueFormatted}`;
      }
    }
  }
};
 
export const pieChartOptionsByFreq: any = {
  ...basePieChartOptions,
  tooltips: {
    callbacks: {
      label: (tooltipItem, data) => {
        const dataset = data.datasets[tooltipItem.datasetIndex];
        const value = dataset.data[tooltipItem.index];
        const valueFormatted = value.toLocaleString();
        return `${data.labels[tooltipItem.index]}: ${valueFormatted}`;
      }
    }
  }
};

export const ChartColors: any[] = [{ backgroundColor: [
  CommonConstant.NAVY_COLOR,
  CommonConstant.LIGHT_GREEN_COLOR, 
  CommonConstant.YELLOW_COLOR, 
  CommonConstant.DARK_GREEN_COLOR,
  CommonConstant.RED_COLOR, 
  CommonConstant.BLUE_COLOR
] }];
 