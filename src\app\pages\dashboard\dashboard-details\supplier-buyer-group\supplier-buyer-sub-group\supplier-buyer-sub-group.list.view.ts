import { SupplierBuyerSubGroup } from "app/model/supplier-buyer-sub-group";
import { FormConstant } from "app/shared/components/ms-form/constants/form.constant";
import { FormModel } from "app/shared/components/ms-form/models";
import { Act } from "app/shared/components/msx-datatable/enums/act";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { MsxView } from "app/shared/components/msx-view/models/MsxView";
import { WidgetType } from "app/shared/components/msx-view/models/WidgetType";
import { CommonConstant } from "app/shared/constant/common.constant";

export const SupplierBuyerSubGroupSearchFilter: FormModel<string> = {
    name: 'SupplierBuyerSubGroupSearchForm',
    direction: FormConstant.DIRECTION_HORIZONTAL,
    autoload: true,
    colSize: 6,
    components: [
        {
            key: 'mainGroupName',
            label: 'Main Group',
            placeholder: 'Type main group here',
            controlType: FormConstant.TYPE_TEXT,
            value: ''
        },
        {
            key: 'subGroupName',
            label: 'Sub Group',
            placeholder: 'Type sub group here',
            controlType: FormConstant.TYPE_TEXT,
            value: ''
        },
    ],
    params: [
        {
            key: 'page',
            controlType: FormConstant.TYPE_HIDDEN,
            value: 1
        }
    ]
}

const SupplierBuyerSubGroupTable: Table<SupplierBuyerSubGroup> = {
    name: 'subGroups',
    list: [],
    columns: [
        {
            type: ColumnType.Number,
            prop: 'rowNum',
            label: 'No',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'subGroupName',
            label: 'Sub Group',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'mainGroupName',
            label: 'Main Group',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'createDate',
            label: 'Created Date',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'lastUpdated',
            label: 'Last Updated',
            width: 100
        }, 
        {
            type: ColumnType.Action,
            label: 'Action',
            width: 100,
            action: [
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: 'ft-edit',
                    descr: 'Edit',
                    type: Act.Edit,
                },
                {
                    class: CommonConstant.TEXT_DANGER,
                    icon: 'ft-trash-2',
                    descr: 'Delete',
                    type: Act.Delete,
                }
            ]
        }
    ]
}

export const SupplierBuyerSubGroupListView: MsxView = { 
    title: ' - ' + CommonConstant.SUPPLIER_BUYER_TITLE + ' - ' + CommonConstant.ADD_VIEW_SUB_GROUP_TITLE,
    components: [
        {
            type: WidgetType.SearchFilter,
            component: SupplierBuyerSubGroupSearchFilter
        },
        {
            type: WidgetType.Datatable,
            component: SupplierBuyerSubGroupTable
        }
    ]
}