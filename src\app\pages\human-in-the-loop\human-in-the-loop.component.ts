import { Component, Input, OnInit } from '@angular/core';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { HitlListView } from './human-in-the-loop.list.view';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { Router } from '@angular/router';
import { GlobalService } from 'app/shared/data/global.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-human-in-the-loop',
  templateUrl: './human-in-the-loop.component.html',
  styleUrls: ['./human-in-the-loop.component.scss']
})
export class HumanInTheLoopComponent implements OnInit {
  @Input() stateDashboard: Dashboard;

  view: MsxView = HitlListView;
  serviceUrl: string = URLConstant.HitlBankStatementList;
  buttonList: [];

  constructor(
    private modalService: NgbModal,
      private router: Router,
      private global: GlobalService,
  ) { }

  ngOnInit(): void {
  }

  onItemClickListener($event: any) {
    const buttonName = $event['act']['type'];
    if (Act.View === buttonName) {
      // to be added later
    } else if (Act.ViewLog === buttonName) {
      // to be added later
    } else if (Act.Edit === buttonName) {
      // to be added later
    }
  }

  editData(data: any) {
  const states = {
    ...this.stateDashboard,
    ocrResultId: data.id,
    isHitl: 0  // Flag untuk OCR Result normal
  } 
  this.router.navigate([PathConstant.DETAIL_OCR_RESULT], {state: states});
}

}
