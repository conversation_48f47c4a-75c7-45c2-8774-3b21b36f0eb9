import { Component, OnInit } from '@angular/core';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { HitlListView } from './human-in-the-loop.list.view';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { Act } from 'app/shared/components/msx-datatable/enums/act';

@Component({
  selector: 'app-human-in-the-loop',
  templateUrl: './human-in-the-loop.component.html',
  styleUrls: ['./human-in-the-loop.component.scss']
})
export class HumanInTheLoopComponent implements OnInit {

  view: MsxView = HitlListView;
  serviceUrl: string = URLConstant.HitlBankStatementList;
  buttonList: [];

  constructor() { }

  ngOnInit(): void {
  }

  onItemClickListener($event: any) {
    const buttonName = $event['act']['type'];
    if (Act.View === buttonName) {
      // to be added later
    } else if (Act.ViewLog === buttonName) {
      // to be added later
    } else if (Act.Edit === buttonName) {
      // to be added later
    }
  }


}
