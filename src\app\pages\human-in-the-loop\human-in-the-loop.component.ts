import { Component, Input, OnInit } from '@angular/core';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { HitlListView } from './human-in-the-loop.list.view';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { Router } from '@angular/router';
import { GlobalService } from 'app/shared/data/global.service';
import { Dashboard } from 'app/model/dashboard';

@Component({
  selector: 'app-human-in-the-loop',
  templateUrl: './human-in-the-loop.component.html',
  styleUrls: ['./human-in-the-loop.component.scss']
})
export class HumanInTheLoopComponent implements OnInit {
  @Input() stateDashboard: Dashboard;

  view: MsxView = HitlListView;
  serviceUrl: string = URLConstant.HitlBankStatementList;
  buttonList: [];

  constructor(
    private router: Router,
    private global: GlobalService,
  ) { }

  ngOnInit(): void {
  }

  onItemClickListener($event: any) {
    const buttonName = $event['act']['type'];
    const data = $event['data'];

    if (Act.View === buttonName) {
      // to be added later
    } else if (Act.ViewLog === buttonName) {
      // to be added later
    } else if (Act.Edit === buttonName) {
      this.editData(data);
    }
  }

  editData(data: any) {
    const states = {
      ...this.stateDashboard,
      fileSourcePath: data.fileSourcePath,
      dashboardName: data.dashboardGroupName,
      isHitl: 1  // Flag untuk HITL
    }
    this.router.navigate([PathConstant.DETAIL_OCR_RESULT], {state: states});
  }

}
