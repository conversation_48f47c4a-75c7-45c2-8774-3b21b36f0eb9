import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { HitlListView } from './human-in-the-loop.list.view';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { Router } from '@angular/router';
import { GlobalService } from 'app/shared/data/global.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DashboardService } from 'app/services/api/dashboard.service';
import { ToastrService } from 'ngx-toastr';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { HitlActionRequest } from 'app/model/api/hitl/hitl-action-request';
import { MsxPagingComponent } from 'app/shared/components/msx-view/msx-paging.component';
import * as swalFunction from 'app/shared/data/sweet-alerts';
import { Dashboard } from 'app/model/dashboard';

@Component({
  selector: 'app-human-in-the-loop',
  templateUrl: './human-in-the-loop.component.html',
  styleUrls: ['./human-in-the-loop.component.scss']
})
export class HumanInTheLoopComponent implements OnInit {
  @Input() stateDashboard: Dashboard;

  view: MsxView = HitlListView;
  serviceUrl: string = URLConstant.HitlBankStatementList;
  buttonList: [];

  // Modal properties
  @ViewChild('hitlActionModal') hitlActionModal: any;
  modalNotes: string = '';
  modalConfig: any = {};
  showValidationError: boolean = false;
  currentData: any = null;

  swal = swalFunction;
  @ViewChild(MsxPagingComponent) msxPagingComponent!: MsxPagingComponent;

  constructor(
    private modalService: NgbModal,
    private router: Router,
    private global: GlobalService,
    private dashboardService: DashboardService,
    private toastrService: ToastrService,
  ) { }

  ngOnInit(): void {
  }

  onItemClickListener($event: any) {
    const buttonName = $event['act']['type'];
    const data = $event['data'];

    if (Act.View === buttonName) {
      // to be added later
    } else if (Act.ViewLog === buttonName) {
      // to be added later
    } else if (Act.Edit === buttonName) {
      this.editData(data);
    }
  }

  editData(data: any) {
    const states = {
      ...this.stateDashboard,
      fileSourcePath: data.fileSourcePath,
      dashboardName: data.dashboardGroupName,
      isHitl: 1  // Flag untuk HITL
    }
    this.router.navigate([PathConstant.DETAIL_OCR_RESULT], {state: states});
  }

  // Open Reject Modal
  openRejectModal(data: any) {
    this.currentData = data;
    this.modalConfig = {
      action: 'reject',
      title: 'Reject OCR Result',
      message: 'Are you sure you want to reject this OCR result? Please provide a reason.',
      alertClass: 'alert-warning',
      icon: 'ft-x text-danger',
      notesLabel: 'Rejection Reason',
      notesPlaceholder: 'Please explain why you are rejecting this OCR result...',
      notesHint: 'This reason will be logged and sent to the system administrator.',
      submitButtonClass: 'btn btn-danger',
      submitIcon: 'ft-x',
      submitText: 'Reject'
    };

    this.modalNotes = '';
    this.showValidationError = false;
    this.modalService.open(this.hitlActionModal, {
      centered: true,
      backdrop: 'static',
      size: 'lg'
    });
  }

  // Open Submit Modal
  openSubmitModal(data: any) {
    this.currentData = data;
    this.modalConfig = {
      action: 'submit',
      title: 'Submit OCR Result',
      message: 'Please confirm that you want to submit this OCR result.',
      alertClass: 'alert-info',
      icon: 'ft-check text-success',
      notesLabel: 'Additional Notes (Optional)',
      notesPlaceholder: 'Add any additional notes or comments...',
      notesHint: 'These notes will be saved with the submission.',
      submitButtonClass: 'btn btn-success',
      submitIcon: 'ft-check',
      submitText: 'Submit'
    };

    this.modalNotes = '';
    this.showValidationError = false;
    this.modalService.open(this.hitlActionModal, {
      centered: true,
      backdrop: 'static',
      size: 'lg'
    });
  }

  // Confirm action from modal
  async confirmAction() {
    if (this.modalConfig.action === 'reject') {
      // Validation notes untuk reject
      if (!this.modalNotes?.trim()) {
        this.showValidationError = true;
        return;
      }
      await this.performReject();
    } else if (this.modalConfig.action === 'submit') {
      await this.performSubmit();
    }

    this.closeModal();
  }

  // Close modal
  closeModal() {
    this.modalService.dismissAll();
    this.modalNotes = '';
    this.showValidationError = false;
    this.currentData = null;
  }

  // Perform reject action
  async performReject() {
    const request = new HitlActionRequest();
    request.tenantCode = this.global.user.role.tenantCode;
    request.fileSourcePath = this.currentData.fileSourcePath;
    request.dashboardName = this.currentData.dashboardGroupName;
    request.lovRemark = 'REJECT';
    request.notes = this.modalNotes;
    request.actionBy = this.global.user.email;
    request.actionDate = new Date().toISOString();

    try {
      const response = await this.dashboardService.rejectHitlOcrResult(request).toPromise();
      if (response['status']['code'] == 0) {
        this.toastrService.success('OCR Result rejected successfully!', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        // Refresh data
        this.msxPagingComponent.refreshSearch();
      }
    } catch (error) {
      this.toastrService.error('Failed to reject OCR Result', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
    }
  }

  // Perform submit action
  async performSubmit() {
    const request = new HitlActionRequest();
    request.tenantCode = this.global.user.role.tenantCode;
    request.fileSourcePath = this.currentData.fileSourcePath;
    request.dashboardName = this.currentData.dashboardGroupName;
    request.lovRemark = 'SUCCESS';
    request.notes = this.modalNotes || '';
    request.actionBy = this.global.user.email;
    request.actionDate = new Date().toISOString();

    try {
      const response = await this.dashboardService.submitHitlOcrResult(request).toPromise();
      if (response['status']['code'] == 0) {
        this.toastrService.success('OCR Result submitted successfully!', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        // Refresh data
        this.msxPagingComponent.refreshSearch();
      }
    } catch (error) {
      this.toastrService.error('Failed to submit OCR Result', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
    }
  }

}
