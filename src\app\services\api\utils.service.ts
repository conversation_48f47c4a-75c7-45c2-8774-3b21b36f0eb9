import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class UtilsService {

  static abbreviateNumber(value: number): string {
    const SI_SYMBOL = ["", "K", "M", "B", "T"];
    const tier = Math.log10(Math.abs(value)) / 3 | 0;
  
    if (tier === 0) {
      return value.toString(); // If less than 1K, return raw value
    }
  
    const suffix = SI_SYMBOL[tier];
    const scale = Math.pow(10, tier * 3);
    const scaled = Math.floor(value / scale * 10) / 10; // Truncate to 1 decimal place
  
    return `${scaled}${suffix}`; // e.g., 2.1M
  }

  constructor() {
    // Intentionally empty
  }
}
