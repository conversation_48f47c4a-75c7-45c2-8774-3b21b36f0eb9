<div class="row" style="margin-top: 15px">
    <div class="col-8 px-4">
      <div class="content-header" style="margin-top: 0 !important;">
        {{dashboardName }} - {{title}} 
      </div>
    </div>
    <div class="col-4 px-4 text-right"> 
        <a class="msx-action" [ngClass]="'btn btn-warning mr-1'" (click)="goBack()"><i [ngClass]="'ft-arrow-left-circle'"></i> {{'Back' | translate}}</a>
        <a class="msx-action" [ngClass]="'btn btn-primary'" (click)="goToAddTransaction()"><i [ngClass]="'ft-plus'"></i> {{'Add Transaction' | translate}}</a>
    </div>
  </div> 
    <div class="px-1"> 
     <div class="card">
       <div class="card-body">
         <form [formGroup]="msxForm"> 
           <div class="row">
            <div class="col-{{formObj.colSize}}">
              <app-text [form]="msxForm" [question]="getQuestionkey('subGroupName')" [direction]="formObj.direction"></app-text>
            </div>
          </div> 
        </form>       
       </div> 
     </div> 
    </div>  

  <div class="body">
    <app-msx-datatable [tableObj]="table" [datasource]="datasource" (getPage)="getPage($event)" (itemClickListener)="onItemClickListener($event)"></app-msx-datatable>
  </div>
 