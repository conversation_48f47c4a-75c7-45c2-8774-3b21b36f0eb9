<div class="modal-header">
    <h4 class="modal-title">Add Circular</h4>
    <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  
  <div class="row">
    <div class="col-12"> 
                <div class="card-body">
                    <form [formGroup]="msxForm" (ngSubmit)="onSubmit()">

                      <div class="row">
                        <div class="col-{{formObj.colSize}}">
                          <app-text [form]="msxForm" [question]="getQuestionkey('groupName')" [direction]="formObj.direction"></app-text>
                        </div>
                      </div>

                        <div class="row">
                          <div class="col-12 text-right">
                            <button class="btn btn-primary" type="submit" >{{'Next' | translate}}</button>
                          </div>
                        </div>
                    </form>
                    
                </div> 
    </div>
  </div>