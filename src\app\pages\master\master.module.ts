import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MasterRoutingModule } from './master-routing.module';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {NgSelectModule} from '@ng-select/ng-select';
import {PipeModule} from '../../shared/pipes/pipe.module';
import {NgxDatatableModule} from '@swimlane/ngx-datatable';
import {NgbModule} from '@ng-bootstrap/ng-bootstrap';
import {SharedModule} from '../../shared/shared.module';
import {PdfJsViewerModule} from 'ngx-pdfjs-viewer';
import {DragDropModule} from '@angular/cdk/drag-drop';
import {MsFormModule} from '../../shared/components/ms-form/ms-form.module';


@NgModule({
  declarations: [
  ],
  imports: [
    CommonModule,
    MasterRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    NgSelectModule,
    NgbModule,
    PipeModule,
    NgxDatatableModule,
    SharedModule,
    PdfJsViewerModule,
    DragDropModule,
    MsFormModule
  ],
  entryComponents: [
  ]
})
export class MasterModule { }
