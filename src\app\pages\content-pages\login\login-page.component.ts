import {ChangeDetector<PERSON><PERSON>, Component, NgZone, OnInit} from '@angular/core';
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {ActivatedRoute, NavigationExtras, Router} from '@angular/router';
import {AuthService} from '../../../services/api/auth.service';
import {UserService} from '../../../services/api/user.service';
import {LoginRequest} from '../../../model/api/login.request';
import {GlobalService} from '../../../shared/data/global.service';
import {ToastrService} from 'ngx-toastr';
import * as swalFunctions from '../../../shared/data/sweet-alerts';
import {PathConstant} from 'app/shared/constant/PathConstant';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {MultiRoleComponent} from '../../../shared/components/multi-role/multi-role.component';
import {Menu} from '../../../model/menu';
import { DeviceDetectorService } from 'ngx-device-detector';
import { TranslateService } from '@ngx-translate/core';
import { CommonConstant } from 'app/shared/constant/common.constant';



@Component({
  selector: 'app-login-page',
  templateUrl: './login-page.component.html',
  styleUrls: ['./login-page.component.scss']
})

export class LoginPageComponent implements OnInit {

  loginFormSubmitted = false;
  isLoginFailed = false;
  isReturnToSign = false;
  redirect: string;
  documentId: string;
  errorMessage: string;
  params: any;
  msg: string;
  swal = swalFunctions;
  navs: Menu[];
  isMobile = false;
  isShowPassword = false;

  //CONFLICT 2

  // Ini hasil development Axel BSA-282

  loginForm = new FormGroup({
    username: new FormControl('', [Validators.required]),
    password: new FormControl('', [Validators.required]),
    rememberMe: new FormControl(true)
  });
  
  constructor(private router: Router, private authService: AuthService,
    private userService: UserService, private activateRoute: ActivatedRoute,
    private cdk: ChangeDetectorRef, private toastrService: ToastrService,
    private ngZone: NgZone, private global: GlobalService, private ngModal: NgbModal, 
    private deviceService: DeviceDetectorService, private translate: TranslateService) {
    this.navs = [];
    this.activateRoute.queryParams.subscribe(params => {
      this.params = params;
      this.msg    = this.params?.msg;
      this.isReturnToSign = this.params?.isReturnToSign;
      this.documentId = this.params?.id;
      this.redirect = this.params?.redirect;
    })
    if (deviceService.isMobile()) {
      this.isMobile = true;
    }
  }

  ngOnInit(): void {
    
    if (this.global.msg) {
      this.global.msg = null;
      localStorage.removeItem('msg');
    }

    if (this.global.user != null) {
      this.authService.logout();
    }

  }

  get lf() {
    return this.loginForm.controls;
  }

  // On submit button click
  onSubmit() {
    
    this.loginFormSubmitted = true;
    if (this.loginForm.invalid) {
      return;
    }

    const credential: LoginRequest = new LoginRequest('password', this.loginForm.value.username, this.loginForm.value.password, 'frontend', '');

    this.authService.login(credential).subscribe(loginResponse => {
      this.ngZone.run(() => {
        this.loginFormSubmitted = false;
        this.global.token = loginResponse.access_token;
        // Call API get user profile
        this.userService.profile(credential.username).subscribe(response => {
          if (response.status.code !== 0) {
            return;
          }

          // Set user data to global data
          this.global.user = response.user;

          if (this.global.user.roles.length === 1) {
            const user = response.user;
            user.role  = response.user.roles[0];
            this.global.user = user;
            this.onSuccessLogin();
            return;
          }

          const modal = this.ngModal.open(MultiRoleComponent, { size: 'md', backdrop: 'static', keyboard: false});
          modal.componentInstance.roles = response.user.roles;
          modal.dismissed.subscribe(result => {
            const user = this.global.user;
            user.role  = this.global.user.roles.find(x => x.tenantCode === result['tenantCode'] && x.roleCode === result['roleCode']);
            this.global.user = user;
            console.log('Update User', this.global.user);
            this.onSuccessLogin();
          });
        });
      });
    }, err => {
      this.loginFormSubmitted = false;
      this.toastrService.error(err['error']['error_description'] || 'Maaf terjadi timeout, silahkan coba kembali.', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
      return;
    });

    this.cdk.detectChanges();
  }

  onSuccessLogin() {
    // Get User Menu
    this.authService.getMenu().subscribe(response => {
      this.navs.splice(0, this.navs.length);
      this.navs = [...this.navs, ...response.menuList];

      if (this.navs.length === 0) {
        this.toastrService.error('Please provide access menu first!', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        return;
      }

      this.navs = this.navs.sort((x, y) => { return x.order - y.order });
      this.authService.routes = this.navs;
      console.log('Access Menu', this.navs);

      // Notifikasi perlu ganti password
      if (this.global.user.changePwdLogin === '1') {
        this.toastrService.info(this.translate.instant('You need to change access code'), null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        const extras: NavigationExtras = {
          state: {
            redirect: this.redirect || this.navs[0].path
          }
        };
        this.router.navigate([PathConstant.CHANGE_PASSWORD_PAGE], extras);
        return;
      }

      if (this.redirect) {
        this.router.navigateByUrl(this.redirect);
        return;
      }

      this.router.navigate([this.navs[0].path]);
    });
  }

  onInput($event) {
    this.errorMessage = $event.target.value;
  }

  toggleIsShowPassword() {
    this.isShowPassword = !this.isShowPassword;
  }
}
