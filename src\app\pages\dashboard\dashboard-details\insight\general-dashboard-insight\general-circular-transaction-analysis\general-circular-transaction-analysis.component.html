<div class="row text-center">  
    <div class="col-xl-12">
        <div class="card card-chart py-2 my-0">
            <h2 class="mb-0">Circular Transaction Analysis</h2>
        </div>
    </div>
</div>  
<section id="minimal-statistics">
	<div class="row"> 
          <div *ngIf="isShowWarning" class="col-lg-2 col-12 pr-0">
            <div class="card card-chart  text-center d-flex flex-column justify-content-center align-items-center pt-2" style="background-color: #fb6363;">
				<div class="card-body">
                    <i class="ft-alert-triangle font-large-4"></i>
                    <div class="font-weight-bold font-medium-1 text-center mt-2">Over 25% of transactions maybe circular. Please review the bank statement carefully.</div> 
				</div>
			</div>
          </div> 
		<div [ngClass]="isShowWarning ? 'col-lg-4 col-12' : 'col-lg-6 col-12'">
			<div class="card card-info-top " style="border-color: #fb6363;">
				<div class="card-body">
                    <div class="font-medium-5 text-center font-weight-bold">Transaction(s) Count</div>
                    <div class="row">
                        <div class="col-6 text-center d-flex flex-column justify-content-center align-items-center" style="border-width: 0px 1px 0px 0px; border-style: solid;">
                            <h1  style="color: #fb6363;">{{circularHeader.realCircularCount | number: '1.0-0':'id-ID'}}</h1>
                            <div class="font-medium-5">of</div>
                            <h2 class="card-num">{{circularHeader.circularCount | number: '1.0-0':'id-ID'}}</h2>
                        </div>
                        <div class="col-6 text-center d-flex flex-column justify-content-center align-items-center">
                            <div [ngClass]="isShowWarning ? 'font-large-3' : 'font-large-4'" class="card-num" style="color: #fb6363;"> {{circularHeader.circularCountRatio}} %</div>
                        </div>
                    </div>
                    <div class="card-num font-medium-4 text-center">detected as circular</div> 
				</div>
			</div>
		</div> 
        <div class="col-lg-6 col-12 pl-0">
			<div class="card card-info-top" style="border-color: #fb6363;">
				<div class="card-body">
                    <div class="font-medium-5 text-center font-weight-bold">Transaction(s) Amount</div>
                    <div class="row">
                        <div class="col-6 text-center d-flex flex-column justify-content-center align-items-center" style="border-width: 0px 1px 0px 0px; border-style: solid;">
                            <h1  style="color: #fb6363;">{{circularHeader.realCircularAmount | number: '1.2-2'}}</h1>
                            <div class="font-medium-5">of</div>
                            <h2 class="card-num">{{circularHeader.circularAmount | number: '1.2-2'}}</h2>
                        </div>
                        <div class="col-6 text-center d-flex flex-column justify-content-center align-items-center">
                            <h1 class="card-num font-large-4" style="color: #fb6363;"> {{circularHeader.circularAmountRatio}} %</h1>
                        </div>
                    </div>
                    <div class="card-num font-medium-4 text-center">detected as circular</div> 
				</div>
			</div>
		</div> 
	</div>
    
</section>

<div class="row text-center">  
    <div class="col-xl-12">
        <div class="card card-chart py-2 my-0">
            <div class="card-body">
                <div class="card-block">
                    <div class="row">
                        <div class="col-lg-6 col-12 pr-0">
                            <div class="height-400">
                                <canvas baseChart class="chart" [datasets]="comparisonTransactionChartData" [labels]="MonthChartLabels" [options]="comparisonTransactionChartOptions" [colors]="comparisonTransactionChartColors"
                                [legend]="true" [chartType]="comparisonTransactionChartType" (chartHover)="chartHovered($event)" (chartClick)="chartClicked($event)" ></canvas>
                            </div>
                        </div>
                        <div class="col-lg-6 col-12 pl-0">
                            <div class="height-400">
                                <canvas baseChart class="chart" [datasets]="monthlyPercentageCircularChartData" [labels]="MonthChartLabels" [options]="monthlyPercentageCircularChartOptions" [colors]="monthlyPercentageCircularChartColors"
                                [legend]="true" [chartType]="monthlyPercentageCircularChartType" (chartHover)="chartHovered($event)" (chartClick)="chartClicked($event)" ></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>  