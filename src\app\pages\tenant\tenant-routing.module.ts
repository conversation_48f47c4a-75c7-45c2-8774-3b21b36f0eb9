import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { PathConstant } from "app/shared/constant/PathConstant";
import { TenantComponent } from "./tenant.component";
import { AddTenantComponent } from "./add-tenant/add-tenant.component";
import { title } from "process";

const routes: Routes = [
    {
      path: '',
      children: [
        {
          path: '',
          component: TenantComponent,
          data: {
            title: 'Tenant'
          }
        },
        {
          path: 'add',
          component: AddTenantComponent,
          data: {
            title: 'Add Tenant'
          }
        }
      ]
    }
  ];
  
  @NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
  })
  export class TenantRoutingModule { }
  