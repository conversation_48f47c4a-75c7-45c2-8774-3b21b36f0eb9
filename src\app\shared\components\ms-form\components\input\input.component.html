<fieldset *ngIf="isVerticalLayout" class="form-group" [formGroup]="form">
  <label [for]="question.key" class="msx-form-label" translate>{{question.label}} <span *ngIf="question.required" class="text-danger" style="font-weight: bold">*</span></label>
  <input [formControlName]="question.key" [id]="question.key" [ngClass]="question.class" class="form-control"
         [type]="question.type" [placeholder]="question.placeholder | translate" [value]="question.value" [required]="question.required"
         (focus)="onFocus()" (blur)="onInput($event)">
</fieldset>

<div class="form-group row" *ngIf="!isVerticalLayout" [formGroup]="form">
  <div class="col-5">
    <label [for]="question.key" class="msx-form-label" translate>{{question.label}} <span *ngIf="question.required" class="text-danger" style="font-weight: bold">*</span></label>
  </div>
  <div class="col-7">
    <input [formControlName]="question.key" [id]="question.key" [ngClass]="question.class" class="form-control"
           [type]="question.type" [placeholder]="question.placeholder | translate" [value]="question.value" [required]="question.required"
           (focus)="onFocus()" (blur)="onInput($event)">
  </div>
</div>
