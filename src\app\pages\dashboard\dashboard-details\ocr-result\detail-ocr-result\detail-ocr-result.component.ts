import { ChangeDete<PERSON><PERSON><PERSON>, Component, <PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { FormGroup, FormBuilder } from '@angular/forms';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DashboardService } from 'app/services/api/dashboard.service';
import { HttpService } from 'app/services/http.service';
import { FormModel } from 'app/shared/components/ms-form/models';
import { GlobalService } from 'app/shared/data/global.service';
import { ToastrService } from 'ngx-toastr';
import * as swalFunction from 'app/shared/data/sweet-alerts'; 
import { PathConstant } from 'app/shared/constant/PathConstant';
import { BankStatementHeaderTransactionRequest } from 'app/model/api/OcrResult/bank-statement-header-transaction';
import { MsxFormControlService } from 'app/shared/components/ms-form/msx-form-control.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { SaveBankStatementHeaderRequest } from 'app/model/api/OcrResult/save-bank-statement-header-request';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { PersonalInfoOcrResultComponent } from './personal-info-ocr-result/personal-info-ocr-result.component';
import { TransactionSummaryOcrResultComponent } from './transaction-summary-ocr-result/transaction-summary-ocr-result.component';
import { SaveBankStatementSummaryRequest } from 'app/model/api/OcrResult/save-bank-statement-summary-request';
import { SummariesContainer } from 'app/model/custom/summaries-container';
import { HitlActionRequest } from 'app/model/api/hitl/hitl-action-request';

@Component({
  selector: 'app-detail-ocr-result',
  templateUrl: './detail-ocr-result.component.html',
  styleUrls: ['./detail-ocr-result.component.scss']
})
export class DetailOcrResultComponent implements OnInit {
  formObj: FormModel<any>;
  msxForm: FormGroup;
  state: any;
  dashboardName: string;
  pdfSrc:string;
  swal = swalFunction;

  private dpi = 200;
  pageVariable = 1;
  page = 1;
  lastRectangleId = '';
 
  @ViewChild(PersonalInfoOcrResultComponent) personalInfoOcrResultComponent!: PersonalInfoOcrResultComponent;
  @ViewChild(TransactionSummaryOcrResultComponent) transactionSummaryOcrResultComponent!: TransactionSummaryOcrResultComponent;

  // HITL Properties
  isHitl: number = 0;
  @ViewChild('hitlActionModal') hitlActionModal: any;
  modalNotes: string = '';
  modalConfig: any = {};
  showValidationError: boolean = false;

  constructor(
    private global: GlobalService,
    private router: Router,
    public http: HttpService,
    private fcs: MsxFormControlService,
    private ngZone: NgZone,
    private cdr: ChangeDetectorRef,
    private modalService: NgbModal,  
    private toastrService: ToastrService,
    private dashboardService: DashboardService,
    private spinner: NgxSpinnerService,
    private readonly formBuilder: FormBuilder,) {
      this.state = this.router.getCurrentNavigation().extras?.state;
      // Ambil isHitl dari router state
      this.isHitl = this.state?.isHitl || 0;
    }

  async ngOnInit() {  
    if(!this.state){
      this.router.navigate([PathConstant.DASHBOARD]);
    }
    this.dashboardName = this.state.dashboardName; 

    await this.getBankStatementHeaderTransactionFile();
  } 

  async getBankStatementHeaderTransactionFile(){
    const request = new BankStatementHeaderTransactionRequest();
    request.tenantCode = this.global.user.role.tenantCode;
    request.fileSourcePath = this.state.fileSourcePath;
    request.dashboardName = this.dashboardName;
    request.roleCode = this.global.user.role.roleCode;
   
    this.spinner.show();
    await this.dashboardService.getBankStatementHeaderTransactionFile(request).toPromise().then(
      async (response) => { 
         if (response['status']['code'] == 0) {   
          const res = response;
          this.pdfSrc = "data:application/pdf;base64,"+ res.pdfBase64;
          this.spinner.hide();
         }
      }
    )
  }  

  moveTo(info:any){
   const coordinates = JSON.parse(info.boxLocation); 
   const pageNumber = info.boxPage; 

    if(coordinates.x == 0 && coordinates.y == 0 && coordinates.h == 0 && coordinates.w == 0){
       return;
    }
    this.pageVariable = pageNumber;

    const area = {
      'rectangleId': 'rectangle-'+pageNumber,
      'pageNumber': pageNumber,
      'rect': {
        'height': Math.round((this.convertGoogleVisionToPDFTron(coordinates.h)) + this.dpi / 25),
        'width': Math.round((this.convertGoogleVisionToPDFTron(coordinates.w)) + this.dpi / 25),
        'x1': Math.round(this.convertGoogleVisionToPDFTron(coordinates.x)- this.dpi / 30),
        'y1': Math.round(this.convertGoogleVisionToPDFTron(coordinates.y)- this.dpi / 30)
      }
    }

    if(area.rect.x1 < 0){
      area.rect.x1 = 0;
    }
    if(area.rect.y1 < 0){
      area.rect.y1 = 0;
    }

    setTimeout(() => {
      if (this.lastRectangleId !== area.rectangleId && this.lastRectangleId !== '' ) {
        if (document.getElementById(this.lastRectangleId)) {
          document.getElementById(this.lastRectangleId).style.border = '';
        }
      }
      const rectElement = document.getElementById(area.rectangleId);
      if (rectElement) {
  
        rectElement.style.border = '3px solid #FF586B';
        rectElement.style.opacity = '1';
        rectElement.style.left = area.rect.x1 + 'px';
        rectElement.style.top = area.rect.y1 + 'px';
        rectElement.style.width = area.rect.width + 'px';
        rectElement.style.height = area.rect.height + 'px';
  
        this.lastRectangleId = area.rectangleId;
        
        setTimeout(() => {
          rectElement.scrollIntoView({ block: 'start', behavior: 'smooth' });
        }, 100);
      }
    }, 100);
  }

  convertGoogleVisionToPDFTron(googleX: number) {
    const scaleFactor = 67 / this.dpi;
    const coor = googleX * scaleFactor;
    return coor;
  }

  pageRendered(event) {
    const elem = document.createElement('div');
    elem.className = 'to-draw-rectangle';
    elem.style.position = 'absolute';
    elem.style.left = 0 + 'px';
    elem.style.top = 0 + 'px';
    elem.style.right = 0 + 'px';
    elem.style.bottom = 0 + 'px';
    elem.style.visibility = 'visible';
    const path = this.composedPath(event.source.div);

    path.find(p => p.className === 'page').appendChild(elem);

    const rect = document.createElement('div');
    rect.className = 'rectangle';
    rect.id = 'rectangle-' + event.pageNumber;
    rect.style.position = 'absolute';
    rect.style.border = '';
    rect.style.borderRadius = '3px';
    rect.style.left = 0 + 'px';
    rect.style.top = 0 + 'px';
    rect.style.width = 0 + 'px';
    rect.style.height = 0 + 'px';

    // get to-draw-rectangle div and add rectangle
    path.find(p => p.className === 'page').children[2].appendChild(rect);
    const pageElement = path.find(p => p.className === 'page');

    pageElement.addEventListener('mouseenter', () => {
      elem.style.visibility = 'hidden';
    });
  
    pageElement.addEventListener('mouseleave', () => {
      elem.style.visibility = 'visible';
    });
  }

  composedPath(el) {
    const path = [];
    while (el) {
      path.push(el);
      if (el.tagName === 'HTML') {
        path.push(document);
        path.push(window);
        return path;
      }
      el = el.parentElement;
    }
  }
 
  async saveBankStatementHeader(state:string){ 
    const formValue = this.personalInfoOcrResultComponent.msxForm.value;
    const request = new SaveBankStatementHeaderRequest();
    request.tenantCode = this.global.user.role.tenantCode;
    request.fileSourcePath = this.state.fileSourcePath;
    request.dashboardName = this.dashboardName;
    request.accountName = formValue.accountName;
    request.accountNo = formValue.accountNo;
    request.currency = formValue.currency;
    request.bankOffice = formValue.bankOffice;
    request.address = formValue.address;
    request.roleCode = this.global.user.role.roleCode;
    
    await this.dashboardService.saveBankStatementHeader(request).toPromise().then(
      async (response) => {  
         if (response["status"]["code"] == 0) {
          this.toastrService.success('Data Saved!', null, {
            positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
          }); 

          if(state === 'NEXT'){
            this.page = this.page === 1 ? 2 : 1;
          } else if(state === 'BACK'){
            this.goBack();
          }
        } 
      }
    );
  }  

  async saveBankStatementSummary(type:string, state:string){
    const summariesCombined = this.transactionSummaryOcrResultComponent.summariesCombined;  
      if(summariesCombined.length == 0){
        this.toastrService.error('Wait the data to be loaded!', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
         }); 
        return;
      }
    const request = new SaveBankStatementSummaryRequest();
    request.tenantCode = this.global.user.role.tenantCode;
    request.fileSourcePath = this.state.fileSourcePath;
    request.dashboardName = this.dashboardName;  
    request.roleCode = this.global.user.role.roleCode;
    request.saveType = type;  
    request.summaries = summariesCombined.map(item => 
      new SummariesContainer(item.period, item.beginningBalanceOcr, item.endingBalanceOcr)
    ); 
    
    await this.dashboardService.saveBankStatementSummary(request).toPromise().then(
      async (response) => {  
         if (response["status"]["code"] == 0) {
          this.toastrService.success('Data Saved!', null, {
            positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
          }); 

          if(state === 'SAVE'){
            this.goBack();
          } else if(state === 'SUBMIT'){
            delete this.state.fileSourcePath;
              this.router.navigate([PathConstant.DASHBOARD_DETAIL], { 
                state: { 
                  state: this.state, 
                  indexTab: 1
              }
            });
          }
        } 
      }
    );
  }  

  goBack(){
    if(this.page == 1){
      delete this.state.fileSourcePath;
      this.router.navigate([PathConstant.DASHBOARD_DETAIL], { 
        state: { 
          state: this.state, 
          indexTab: CommonConstant.OCR_RESULT_TAB_INDEX
        }
      });
    } else {
      this.page = this.page === 2 ? 1 : 2;
    }
  }

  async saveAndNext(){
    await this.saveBankStatementHeader('NEXT'); 
  }
 
  async saveAndBack(){
    if(this.page == 1){
      await this.saveBankStatementHeader('BACK'); 
    } else { 
      await this.saveBankStatementSummary('EDIT', 'SAVE'); 
    }
  }

  async saveAndExit(type){
    await this.saveBankStatementSummary(type,'SUBMIT');
  }

  // HITL
  openRejectModal() {
    this.modalConfig = {
      action: 'reject',
      title: 'Reject OCR Result',
      placeholder: 'Please provide a reason for rejecting this OCR result...',
      submitButtonClass: 'btn btn-danger',
      submitText: 'Reject'
    };

    this.modalNotes = '';
    this.showValidationError = false;
    this.modalService.open(this.hitlActionModal, {
      centered: true,
      backdrop: 'static',
      size: 'lg'
    });
  }

  openSubmitModal() {
    this.modalConfig = {
      action: 'submit',
      title: 'Submit OCR Result',
      placeholder: 'Add any additional notes or comments (optional)...',
      submitButtonClass: 'btn btn-success',
      submitText: 'Submit'
    };

    this.modalNotes = '';
    this.showValidationError = false;
    this.modalService.open(this.hitlActionModal, {
      centered: true,
      backdrop: 'static',
      size: 'lg'
    });
  }

  async confirmAction() {
    if (this.modalConfig.action === 'reject') {
      
      if (!this.modalNotes?.trim()) {
        this.showValidationError = true;
        return;
      }
      await this.performReject();
    } else if (this.modalConfig.action === 'submit') {
      await this.performSubmit();
    }

    this.closeModal();
  }

  // Close modal
  closeModal() {
    this.modalService.dismissAll();
    this.modalNotes = '';
    this.showValidationError = false;
  }

  // Perform reject action
  async performReject() {
    const request = new HitlActionRequest();
    request.tenantCode = this.global.user.role.tenantCode;
    request.fileSourcePath = this.state.fileSourcePath;
    request.dashboardName = this.state.dashboardName;
    request.lovRemark = 'REJECT';
    request.notes = this.modalNotes;

    try {
      const response = await this.dashboardService.submitHitlRemark(request).toPromise();
      if (response['status']['code'] == 0) {
        this.toastrService.success('OCR Result rejected successfully!', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        
        this.router.navigate([PathConstant.HUMAN_IN_THE_LOOP]);
      }
    } catch (error) {
      this.toastrService.error('Failed to reject OCR Result', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
    }
  }

  async performSubmit() {
    const request = new HitlActionRequest();
    request.tenantCode = this.global.user.role.tenantCode;
    request.fileSourcePath = this.state.fileSourcePath;
    request.dashboardName = this.state.dashboardName;
    request.lovRemark = 'SUCCESS';
    request.notes = this.modalNotes || '';

    try {
      const response = await this.dashboardService.submitHitlRemark(request).toPromise();
      if (response['status']['code'] == 0) {
        this.toastrService.success('OCR Result submitted successfully!', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        
        this.router.navigate([PathConstant.HUMAN_IN_THE_LOOP]);
      }
    } catch (error) {
      this.toastrService.error('Failed to submit OCR Result', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
    }
  }

}
