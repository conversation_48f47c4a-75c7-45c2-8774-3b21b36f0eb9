import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  NgZone,
  OnInit,
  Output,
  ViewChild,
  ViewEncapsulation
} from '@angular/core';
import {PDFDocumentProxy, PDFPageProxy, RenderTask} from 'pdfjs-dist';
import * as PDFJS from "pdfjs-dist";
import {BoxAnnotation} from '../../../model/box-annotation';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {ColumnMode} from '@swimlane/ngx-datatable';
import {SignerType} from '../../data/signer-type';
import {URLConstant} from '../../constant/URLConstant';
import {HttpClient} from '@angular/common/http';
import {Signer} from '../document-anotate/model/signer';
import {LabelModalComponent} from '../document-anotate/component/label-modal/label-modal.component';
import { PageViewport } from 'pdfjs-dist/types/src/display/display_utils';

const enum Status {
  OFF = 0,
  RESIZE = 1,
  MOVE = 2
}

@Component({
  selector: 'app-msx-annotation',
  templateUrl: './msx-annotation.component.html',
  styleUrls: ['./msx-annotation.component.scss']
})
export class MsxAnnotationComponent implements OnInit {

  @Input() document: string;
  @Input() signer: Signer[];
  @Input() readonly = false;

  @Output() annotations: EventEmitter<any> = new EventEmitter<any>();
  @Output() numOfPage: EventEmitter<number> = new EventEmitter<number>();

  @ViewChild('container', {static: false}) public pdfview: ElementRef;
  @ViewChild('ttdx', {static: false}) public ttdx: ElementRef;

  private pdfContainer: { left: number, top: number, right: number, bottom: number };

  imgSrc: string;
  imgWidth: number;
  imgHeight: number;
  errorMessage: string;

  pdf: PDFDocumentProxy;
  currentPage = 1;
  size: number;
  mId = 1;

  public mouse: {x: number, y: number};
  public status: Status = Status.OFF;
  public isLock = false;
  public class  = 'ttd';

  public ColumnMode = ColumnMode;
  public boxs: BoxAnnotation[] = [];
  public sampleSign: BoxAnnotation;

  private signerTypes: any[];

  private mouseClick: {x: number, y: number, left: number, top: number};
  private ttdxBox: { left: number, top: number, right: number, bottom: number };
  private transform: string;

  constructor(private http: HttpClient, private cdr: ChangeDetectorRef, private modalService: NgbModal, private ngZone: NgZone) { }

  async ngOnInit(): Promise<void> {
    this.isLock = !!this.readonly;
    // PDFJS.disableWorker = true;
    // this.initSigns();
    try {
      await this.renderPage().then((x) => {
        this.getSignerType();
      })
    } catch (error) {
      this.errorMessage = error;
      console.log(error);
    }
  }

  public addSigning() {
    console.log(`{width: ${this.imgWidth}, height: ${this.imgHeight} }`);
    // const container = this.container.nativeElement.getBoundingClientRect();
    // this.containerPos = container;
    console.log('Container', this.pdfContainer);

    const modal = this.modalService.open(LabelModalComponent, {centered: true, size: 'sm'});
    modal.dismissed.subscribe(result => {
      console.log('Result', result);
      if (!result) {
        return;
      }

      this.ngZone.run(() => {
        const annotation  = new BoxAnnotation();
        annotation.top    = 24;
        annotation.left   = 24;
        annotation.width  = 130;
        annotation.height = 65;
        annotation.page   = this.currentPage;
        annotation.type   = SignerType.TTD;
        annotation.label  = result?.value;
        annotation.signerType = result?.key;
        annotation.id     = this.mId++;
        annotation.viewport = this.pdfContainer;
        this.sampleSign = annotation;
        this.pushData(annotation);
        this.loadBox();
      })
    });
  }

  public addParaf() {
    console.log(`{width: ${this.imgWidth}, height: ${this.imgHeight} }`);
    // const container = this.container.nativeElement.getBoundingClientRect();
    // this.containerPos = container;
    // console.log('Container', container);

    const modal = this.modalService.open(LabelModalComponent, {centered: true, size: 'sm'});
    modal.dismissed.subscribe(result => {
      console.log('Result', result);
      if (!result) {
        return;
      }

      this.ngZone.run(() => {
        const annotation  = new BoxAnnotation();
        annotation.top    = 24;
        annotation.left   = 24;
        annotation.width  = 65;
        annotation.height = 65;
        annotation.page   = this.currentPage;
        annotation.type   = SignerType.PRF;
        annotation.label  = result?.value;
        annotation.signerType = result?.key;
        annotation.id     = this.mId++;
        annotation.viewport = this.pdfContainer;
        this.pushData(annotation);
      })
    });
  }

  public addMaterai() {
    console.log(`{width: ${this.imgWidth}, height: ${this.imgHeight} }`);
    // const container = this.container.nativeElement.getBoundingClientRect();
    // this.containerPos = container;
    // console.log('Container', container);

    const annotation  = new BoxAnnotation();
    annotation.top    = 24;
    annotation.left   = 24;
    annotation.width  = 130;
    annotation.height = 130;
    annotation.page   = this.currentPage;
    annotation.type   = SignerType.SDT;
    annotation.label  = 'Meterai';
    annotation.id     = this.mId++;
    annotation.viewport = this.pdfContainer;
    this.pushData(annotation);
  }

  lock() {
    this.isLock = !this.isLock;

    if (this.isLock) {
      this.loadBox();
    }
  }

  delete() {
    // this.remove.emit({id: this.annotation.id});
  }

  private loadBox() {
    // get class signer type
    switch (this.sampleSign.type) {
      case SignerType.TTD:
        this.class = 'ttd';
        break;
      case SignerType.PRF:
        this.class = 'prf';
        break;
      default:
        this.class = 'sdt';
        break;
    }

    this.pdfContainer = this.pdfview.nativeElement.getBoundingClientRect();
    this.transform = this.ttdx.nativeElement.style.transform;
    this.getTransformValue(this.transform);

    this.ttdxBox = this.ttdx.nativeElement.getBoundingClientRect();
    this.sampleSign.position = this.ttdxBox;
    console.log('box position:', this.ttdxBox);

    // this.boundingBox.emit({result: this.annotation, transform: this.transform});
    // return this.boxPosition;
    const location = {lx: 0, ly: 0, rx: 0, ry: 0};
    location.lx = this.ttdxBox.left - this.pdfContainer.left;
    location.ly = this.imgHeight + this.pdfContainer.top - this.ttdxBox.bottom;
    location.rx = this.ttdxBox.right - this.pdfContainer.left;
    location.ry = this.imgHeight + this.pdfContainer.top - this.ttdxBox.top;
    console.log('location', location);
  }

  private getTransformValue(transform: string) {
    // const values = transform.replace(/translate3d/gi, '').match(/\d+/g);
    // if (values.length === 3) {
    //   return;
    // }

    this.sampleSign.transform = transform;
    // this.annotation.left = Number(values[0]) + Number(values[3]);
    // this.annotation.top  = Number(values[1]) + Number(values[4]);
  }

  public pushData(data: BoxAnnotation) {
    this.boxs.push(data);
    // this.annotations.emit(this.boxs);
    // this.data.push({label: data.label, type: data.type, page: data.page});
    this.cdr.detectChanges();
  }

  public async nextPage() {
    this.currentPage = this.currentPage + 1;
    await this.renderPage();
  }

  public async previousPage() {
    this.currentPage = this.currentPage - 1;
    await this.renderPage();
  }

  public hasNext() {
    return this.size > this.currentPage;
  }

  public canBack() {
    return this.currentPage > 1;
  }

  setStatus(event, status: number) {
    if (status === 1) {
      event.stopPropagation();
    } else if (status === 2) {
      this.mouseClick = { x: event.clientX, y: event.clientY, left: this.ttdxBox.left, top: this.ttdxBox.top };
      console.log('mouse click', this.mouseClick);
    } else {
      this.loadBox();
    }

    // this.status = status;
  }

  public getBoundingClient(data: {result: BoxAnnotation, transform: string}) {
    const result = data.result;

    const location = {lx: 0, ly: 0, rx: 0, ry: 0};
    location.lx = result.position.left - this.pdfContainer.left;
    location.ly = this.imgHeight + this.pdfContainer.top - result.position.bottom;
    location.rx = result.position.right - this.pdfContainer.left;
    location.ry = this.imgHeight + this.pdfContainer.top - result.position.top;
    console.log('location', location);

    const temp = this.boxs.find(x => x.id === result.id);
    temp.lx = location.lx;
    temp.ly = location.ly;
    temp.rx = location.rx;
    temp.ry = location.ry;

    // console.log('SrcBox', this.boxs);
    this.annotations.emit(this.boxs);
  }

  public remove(result) {
    console.log('Remove', result);
    const temp = this.boxs.find(x => x.id === result.id);
    this.boxs.splice(this.boxs.indexOf(temp), 1);
    this.cdr.markForCheck();
  }

  private async renderPage() {
    try {
      await this.showPDF();
    } catch (error) {
      this.errorMessage = error;
      console.log(error);
    }
  }

  private async showPDF(): Promise<void> {
    const docStr: string[] = this.document.split(',');
    await PDFJS.getDocument({data: window.atob(docStr[1]), isEvalSupported: false}).promise.then(
      (pdf) => {
        this.size = pdf.numPages;
        pdf.getPage(this.currentPage).then(
          (page) => {
            const viewport: PageViewport = page.getViewport({scale: 1});

            const canvas: HTMLCanvasElement = document.createElement('canvas');
            canvas.height  = viewport.height;
            canvas.width   = viewport.width;
            this.imgHeight = viewport.height;
            this.imgWidth  = viewport.width;

            const context: CanvasRenderingContext2D = canvas.getContext('2d');
            page.render({
              canvasContext: context,
              viewport: viewport
            }).promise.then(
              (x) => {
                this.imgSrc = canvas.toDataURL();
                console.log(this.imgSrc);
                this.cdr.markForCheck();
              }
            );

            this.imgSrc = canvas.toDataURL();
            console.log(this.imgSrc);
            this.cdr.markForCheck();
          }
        )
      }
    );
  }

  private async getPage(pageNo: number): Promise<PDFPageProxy> {
    return await this.pdf.getPage(pageNo);
  }

  // private getCanvas(viewport: PDFPageViewport): HTMLCanvasElement {
  //   const canvas: HTMLCanvasElement = document.createElement('canvas');
  //   canvas.height  = viewport.height;
  //   canvas.width   = viewport.width;
  //   this.imgHeight = viewport.height;
  //   this.imgWidth  = viewport.width;
  //   return canvas;
  // }

  // private createRenderTask(
  //   page: PDFPageProxy,
  //   canvas: HTMLCanvasElement,
  //   viewport: PDFPageViewport
  // ): PDFRenderTask {
  //   const context: CanvasRenderingContext2D = canvas.getContext('2d');
  //   return page.render({
  //     canvasContext: context,
  //     viewport: viewport
  //   });
  // }

  private setDisplayValues(canvas: HTMLCanvasElement): void {
    // this.imgWidth  = canvas.width;
    // this.imgHeight = canvas.height;
    this.imgSrc = canvas.toDataURL();
    this.cdr.markForCheck();
  }

  private initSigns() {
    if (!this.signer) {
      return;
    }

    this.pdfContainer = this.pdfview.nativeElement.getBoundingClientRect();
    console.log('pdf-container', this.pdfContainer);

    // Convert signer data to annotation object
    for (const sign of this.signer) {
      switch (sign.signTypeCode) {
        case SignerType.TTD:
          this.createSignObj(sign);
          break;
        case SignerType.PRF:
          this.createPrfObj(sign);
          break;
        default:
          this.createMeteraiObj(sign);
          break;
      }
    }

    console.log('SignerBox', this.boxs);
  }

  private createSignObj(param: Signer) {
    const annotation  = new BoxAnnotation();
    annotation.top    = 24;
    annotation.left   = 24;
    annotation.width  = 130;
    annotation.height = 65;
    annotation.page   = param.signPage;
    annotation.type   = SignerType.TTD;
    annotation.label  = this.getSignerDesc(param.signerTypeCode);
    annotation.signerType = param.signerTypeCode;
    annotation.transform = param.transform;
    annotation.id     = this.mId++;
    this.pushData(annotation);
  }

  private createPrfObj(param: Signer) {
    const annotation  = new BoxAnnotation();
    annotation.top    = 24;
    annotation.left   = 24;
    annotation.width  = 65;
    annotation.height = 65;
    annotation.page   = param.signPage;
    annotation.type   = SignerType.PRF;
    annotation.label  = this.getSignerDesc(param.signerTypeCode);
    annotation.signerType = param.signerTypeCode;
    annotation.transform = param.transform;
    annotation.id     = this.mId++;
    this.pushData(annotation);
  }

  private createMeteraiObj(param: Signer) {
    const annotation  = new BoxAnnotation();
    annotation.top    = 24;
    annotation.left   = 24;
    annotation.width  = 130;
    annotation.height = 130;
    annotation.page   = param.signPage;
    annotation.type   = SignerType.SDT;
    annotation.label  = 'Meterai';
    annotation.transform = param.transform;
    annotation.id     = this.mId++;
    this.pushData(annotation);
  }

  private getSignerType() {
    this.http.post(URLConstant.GetLov, {lovGroup: 'SIGNER_TYPE'})
      .subscribe(response => {
        this.signerTypes = response['lovList'];

        this.ngZone.run(() => {
          this.initSigns();
        });
      });
  }

  private getSignerDesc(code: string) {
    const signerType = this.signerTypes.find(x => x['code'] === code);
    return signerType['description'];
  }

}
