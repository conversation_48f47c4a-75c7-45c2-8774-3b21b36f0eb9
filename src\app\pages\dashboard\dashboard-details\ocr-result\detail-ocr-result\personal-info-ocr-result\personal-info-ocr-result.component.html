<div class="card">
  <div class="row">
    <div class="col-12">
      <div class="content-sub-header px-3" style="margin-top: 15px !important;">
        Personal Information
      </div>
    </div> 
  </div>   
    <div class="card-body">
      <div class="row">
         <div class="col-12">
             <form [formGroup]="msxForm"> 
                 <div class="row">
                  <div class="col-{{formObj.colSize}}">
                    <app-text [form]="msxForm" [question]="getQuestionkey('accountName')" [direction]="formObj.direction" (focus)="onClick('accountNameInfo')"></app-text>
                  </div>
                  <div class="col-{{formObj.colSize}}">
                    <app-text [form]="msxForm" [question]="getQuestionkey('accountNo')" [direction]="formObj.direction" (focus)="onClick('accountNoInfo')"></app-text>
                  </div>
                  <div class="col-{{formObj.colSize}}">
                    <app-text [form]="msxForm" [question]="getQuestionkey('bankOffice')" [direction]="formObj.direction" (focus)="onClick('bankOfficeInfo')"></app-text>
                  </div>
                  <div class="col-{{formObj.colSize}}">
                    <app-text [form]="msxForm" [question]="getQuestionkey('currency')" [direction]="formObj.direction" (focus)="onClick('currencyInfo')"></app-text>
                  </div>
                  <div class="col-{{formObj.colSize}}">
                    <app-textarea [form]="msxForm" [question]="getQuestionkey('address')" [direction]="formObj.direction" (focus)="onClick('addressInfo')"></app-textarea>
                  </div>  
                </div> 
              </form>       
         </div>
      </div>
    </div> 
  </div>  