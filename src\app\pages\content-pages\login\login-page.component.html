<!--Login Page Starts-->
<div *ngIf="!isMobile">
  <section id="login">
    <div class="row vh-100">
      <div class="col left-content">
        <div class="d-flex flex-column justify-content-center align-items-center h-100">
          <img src="assets/img/logos/login-bsa-logo.png" alt="" style="width: 70%; max-height: 20vh; object-fit: contain;">
          <p class="title" translate>Simplify your bank statement data extraction with automation</p>
          <p class="desc" translate>Generate comprehensive bank statement reports and insights effortlessly with just a few clicks</p>
        </div>
      </div>
      <div class="col right-content">
        <div class="content-container">
          <img src="assets/img/logos/eendigo-logo-member-of-adins-small.png" alt="" width="55%">
          <h1 translate>Hi, Welcome to BSA</h1>
          <p style="color: #6E7483; font-size: 14px;" translate>Login now to analyze your bank statement.</p>
          <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
            <div class="form-group">
              <p style="margin-bottom: 8px; font-size: 14px;" translate>Email</p>
              <div class="input">
                <i class="ft-mail"></i>
                <input type="text" formControlName="username" class="form-control"
                  [placeholder]="'Enter your email' | translate" size="36" [disabled]="loginFormSubmitted"
                  [ngClass]="{ 'is-invalid': loginFormSubmitted && lf.username.invalid, 'is-valid': loginFormSubmitted && !lf.username.invalid }"
                  required>
              </div>
            </div>
            <div class="form-group">
              <p style="margin-bottom: 8px; margin-top: 25px; font-size: 14px;" translate>Password</p>
              <div class="input">
                <i class="ft-lock"></i>
                <input [type]="isShowPassword ? 'text' : 'password'" formControlName="password" class="form-control" [placeholder]="'Enter your password' | translate"
                  size="36" [disabled]="loginFormSubmitted"
                  [ngClass]="{ 'is-invalid': loginFormSubmitted && lf.password.invalid, 'is-valid': loginFormSubmitted && !lf.password.invalid }"
                  required>
                <i style="cursor: pointer;" [ngClass]="{'ft-eye-off': !isShowPassword, 'ft-eye': isShowPassword}" (click)="toggleIsShowPassword()"></i>
              </div>
            </div>
            <div class="d-sm-flex justify-content-between input" style="border: 0px; margin-top: 10px;">
              <!-- <div class="remember-me mb-2 mb-sm-0">
                <div class="checkbox auth-checkbox">
                  <input type="checkbox" formControlName="rememberMe" class="form-control" id="rememberMe">
                  <label for="rememberMe"><span class="font-small-2 mb-3 font-weight-normal">&nbsp;Remember Me</span></label>
                </div>
              </div> -->
              <a [routerLink]="['/pages/forgot-password']" style="color: #2B57BB;" translate>Forgot Password?</a>
            </div>
            <button class="login-button" type="submit" [disabled]="loginForm.invalid" translate>Login</button>
          </form>
          <!-- <p class="not-registered-msg">Not registered yet? 
            <span class="create-account-msg">
              <a [routerLink]="['/register']">Create an Account</a>
            </span>
          </p> -->
        </div>
      </div>
    </div>
  </section>
</div>