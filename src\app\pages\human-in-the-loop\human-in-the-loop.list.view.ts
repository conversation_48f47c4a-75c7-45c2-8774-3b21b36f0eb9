import { FormConstant } from "app/shared/components/ms-form/constants/form.constant";
import { FormModel } from "app/shared/components/ms-form/models";
import { QuestionDropdown } from "app/shared/components/ms-form/questions";
import { Act } from "app/shared/components/msx-datatable/enums/act";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { MsxView } from "app/shared/components/msx-view/models/MsxView";
import { WidgetType } from "app/shared/components/msx-view/models/WidgetType";
import { CommonConstant } from "app/shared/constant/common.constant";
import { URLConstant } from "app/shared/constant/URLConstant";

const HitlSearchFilter: FormModel<string> = {
    name: 'hitlSearchForm',
    direction: FormConstant.DIRECTION_HORIZONTAL,
    autoload: true,
    colSize: 6,
    components: [
        new QuestionDropdown({
            key: 'filterTenantCode',
            label: 'Tenant',
            placeholder: 'Select tenant',
            serviceUrl: URLConstant.GetActiveTenants,
            options: [
                {key: '', value: 'All'}
            ],
            value: '',
            args: {
                list: 'tenants',
                key: 'tenantCode',
                value: 'tenantName'
            }
        }),
        new QuestionDropdown({
          key: 'status',
          label: 'Status',
          placeholder: 'Select status',
            serviceUrl: URLConstant.GetLov,
            options: [
                {key: '', value: 'All'}
            ],
            value: '',
            args: {
                list: 'lovList',
                key: 'code',
                value: 'description'
            },
            params: {
                lovGroup: 'PROCESS_STATUS'
            }
        }),
        {
            key: 'dashboardName',
            label: 'Dashboard Name',
            placeholder: 'Type dashboard name here',
            controlType: FormConstant.TYPE_TEXT,
            value: ''
        },
        {
            key: 'filename',
            label: 'File Name',
            placeholder: 'Type file name here',
            controlType: FormConstant.TYPE_TEXT,
            value: ''
        },
        {
            key: 'uploadDateStart',
            label: 'Upload Date Start',
            placeholder: CommonConstant.FORMAT_DATE,
            controlType: FormConstant.TYPE_DATE
        },
        {
            key: 'uploadDateEnd',
            label: 'Upload Date End',
            placeholder: CommonConstant.FORMAT_DATE,
            controlType: FormConstant.TYPE_DATE
        }
    ],
    params: [
        {
            key: 'page',
            controlType: FormConstant.TYPE_HIDDEN,
            value: 1
        }
    ]
}

const HitlTable: Table<any> = {
    name: 'statements',
    list: [],
    columns: [
        {
            type: ColumnType.Number, 
            prop: 'rowNum',
            label: 'No',
            width: 20
        },
        {
            type: ColumnType.Text,
            prop: 'tenantName',
            label: 'Tenant',
            width: 80
        },
        {
            type: ColumnType.Text,
            prop: 'dashboardGroupName',
            label: 'Dashboard Name',
            width: 80
        },
        {
            type: ColumnType.Text,
            prop: 'filename',
            label: 'File Name',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'uploadDate',
            label: 'Upload Date',
            width: 80
        },
        {
            type: ColumnType.Text,
            prop: 'lastUpdated',
            label: 'Last Updated',
            width: 80
        },
        {
            type: ColumnType.Text,
            prop: 'redPercentage',
            label: 'Low Confidence',
            width: 50, 
            condition: true,
            conditionVariable: 'redPercentageWarning',
            conditionExpected: '1',
            conditionedClass: 'text-danger'
        },
        {
            type: ColumnType.Text,
            prop: 'status',
            label: 'Status',
            width: 80
        },
        {
            type: ColumnType.Action,
            label: 'Action',
            width: 60,
            action: [
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: 'ft-edit',
                    descr: 'Edit',
                    type: Act.Edit,
                    condition: true,
                    conditionVariable: 'editable',
                    conditionExpected: '0',
                    conditionedClass: 'd-none'
                },
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: 'ft-eye',
                    descr: 'View Notes',
                    type: Act.View
                },
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: 'ft-file-text',
                    descr: 'View Log',
                    type: Act.ViewLog
                }
            ]
        }
    ]
}

export const HitlListView: MsxView = {
    title: 'HITL',
    components: [
        {
            type: WidgetType.SearchFilter,
            component: HitlSearchFilter
        },
        {
            type: WidgetType.Datatable,
            component: HitlTable
        }
    ]
}