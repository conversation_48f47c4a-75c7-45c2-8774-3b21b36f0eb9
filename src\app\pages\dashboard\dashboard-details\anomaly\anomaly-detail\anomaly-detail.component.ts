import { ChangeDetectorRef, Component, EventEmitter, Input, NgZone, OnInit, Output, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DashboardService } from 'app/services/api/dashboard.service';
import { Button } from 'app/shared/components/msx-view/models/Button';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { MsxPagingComponent } from 'app/shared/components/msx-view/msx-paging.component';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { GlobalService } from 'app/shared/data/global.service';
import * as swalFunction from 'app/shared/data/sweet-alerts';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { ConfigService } from 'app/shared/services/config.service';
import { ToastrService } from 'ngx-toastr';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { FormModel } from 'app/shared/components/ms-form/models';
import { AnomalyTable } from './anomaly-detail.list.view';
import { BehaviorSubject } from 'rxjs';
import { MsxFormControlService } from 'app/shared/components/ms-form/msx-form-control.service';
import { HttpService } from 'app/services/http.service';
import { BankStatementHeaderTransactionRequest } from 'app/model/api/OcrResult/bank-statement-header-transaction';
import { NgxSpinnerService } from 'ngx-spinner';
import { AnomalyDetailTransactionComponent } from './anomaly-detail-transaction/anomaly-detail-transaction.component';

@Component({
  selector: 'app-anomaly-detail',
  templateUrl: './anomaly-detail.component.html',
  styleUrls: ['./anomaly-detail.component.scss']
})
export class AnomalyDetailComponent implements OnInit {
  
  selectedRowData: any = null;
  public WidgetType = WidgetType;
  pdfSrc:string;
  tabGroup: any;
  table = AnomalyTable;
  datasource: BehaviorSubject<any> = new BehaviorSubject<any>([]);
  buttonList: Button[];
  state: any;
  swal = swalFunction;
  title: any;
  msxForm: FormGroup;
  formObj: FormModel<any>;
  isVerticalLayout: boolean;
  selected: any;
  pageVariable = 1;
  container: MsxView; 
  currentFileSourcePath: string;
  private dpi = 200;
  lastRectangleId = '';
  anomalyState: any;

  @Input() reasonCode: string;
  @Input() pageNumber: BehaviorSubject<number> = new BehaviorSubject(1);
  @Output() isDataChanges = new EventEmitter<boolean>();
  @Output() result: EventEmitter<any> = new EventEmitter<any>();
  @Output() form: EventEmitter<FormGroup> = new EventEmitter<FormGroup>();
  @Output() data: EventEmitter<any> = new EventEmitter<any>();
  @Output() results: EventEmitter<any> = new EventEmitter<any>();
  @ViewChild(MsxPagingComponent) msxPagingComponent!: MsxPagingComponent;
  @Output() onClickRow = new EventEmitter<any>();
  @ViewChild('anomalyDetailTransaction') anomalyDetailTransaction: AnomalyDetailTransactionComponent;

  constructor(
    private readonly formControlService: MsxFormControlService, 
    private modalService: NgbModal, 
    private router: Router,  
    private dashboardService: DashboardService,
    private toastrService: ToastrService,
    private ngZone: NgZone,
    private global: GlobalService,
    private cdr: ChangeDetectorRef,
    private configService: ConfigService,
    private readonly formBuilder: FormBuilder,
    private readonly http: HttpService,
    private spinner: NgxSpinnerService) { 
      this.state = this.router.getCurrentNavigation().extras?.state; 
    }

  async ngOnInit(): Promise<void> {
    this.title = 'Anomaly - ' + this.state.reason;
    this.anomalyState = this.state.reasonCode;
    if(!this.state){
      this.router.navigate([PathConstant.DASHBOARD]);
    }
    await this.getBankStatementHeaderTransactionFile('');
  }

  async getBankStatementHeaderTransactionFile(fileSourcePath: string){
    if (!fileSourcePath) {
      if (this.anomalyState !== 'FRAUD_RULE_INCONSISTENT_METADATA') {
        this.showNoDataSelectedMessage();
      }
      return;
    }
    if (fileSourcePath === this.currentFileSourcePath) {
      return;
    }
    
    const request = new BankStatementHeaderTransactionRequest();
    request.tenantCode = this.global.user.role.tenantCode;
    request.fileSourcePath = fileSourcePath;
    request.dashboardName = this.state.dashboardName;
    
    this.spinner.show();
    await this.dashboardService.getBankStatementHeaderTransactionFile(request).toPromise().then(
      async (response) => { 
          if (response['status']['code'] == 0) {   
          const res = response;
          this.pdfSrc = "data:application/pdf;base64,"+ res.pdfBase64;
          this.spinner.hide();
          this.currentFileSourcePath = fileSourcePath;
          }
      }
    )
  }  

  goBack() { 
    delete this.state.reason;
    delete this.state.reasonCode;
    delete this.state.stateDashboard;
    this.router.navigate([PathConstant.DASHBOARD_DETAIL], { 
      state: { 
        state: this.state, 
        indexTab: CommonConstant.ANOMALY
      }
    });
  }

  pageRendered(event) {
    const elem = document.createElement('div');
    elem.className = 'to-draw-rectangle';
    elem.style.position = 'absolute';
    elem.style.left = 0 + 'px';
    elem.style.top = 0 + 'px';
    elem.style.right = 0 + 'px';
    elem.style.bottom = 0 + 'px';
    elem.style.visibility = 'visible';
    const path = this.composedPath(event.source.div);

    path.find(p => p.className === 'page').appendChild(elem);

    const rect = document.createElement('div');
    rect.className = 'rectangle';
    rect.id = 'rectangle-' + event.pageNumber;
    rect.style.position = 'absolute';
    rect.style.border = '';
    rect.style.borderRadius = '3px';
    rect.style.left = 0 + 'px';
    rect.style.top = 0 + 'px';
    rect.style.width = 0 + 'px';
    rect.style.height = 0 + 'px';

    // get to-draw-rectangle div and add rectangle
    path.find(p => p.className === 'page').children[2].appendChild(rect);
    const pageElement = path.find(p => p.className === 'page');

    pageElement.addEventListener('mouseenter', () => {
      elem.style.visibility = 'hidden';
    });
  
    pageElement.addEventListener('mouseleave', () => {
      elem.style.visibility = 'visible';
    });
  }

  composedPath(el) {
    const path = [];
    while (el) {
      path.push(el);
      if (el.tagName === 'HTML') {
        path.push(document);
        path.push(window);
        return path;
      }
      el = el.parentElement;
    }
  }
  
  async onClickedRow(event){
    const info = { 
      boxLocation : event.boxLocation,
      boxPage : event.boxPage,
      fileSourcePath: event.fileSourcePath
    }
    this.onClickRow.emit(info);
    await this.getBankStatementHeaderTransactionFile(event.fileSourcePath);

    const coordinates = JSON.parse(info.boxLocation); 
    const pageNumber = info.boxPage; 

    if(coordinates.x == 0 && coordinates.y == 0 && coordinates.h == 0 && coordinates.w == 0){
       return;
    }
    this.pageVariable = pageNumber;

    const area = {
      'rectangleId': 'rectangle-'+pageNumber,
      'pageNumber': pageNumber,
      'rect': {
        'height': Math.round((this.convertGoogleVisionToPDFTron(coordinates.h)) + this.dpi / 25),
        'width': Math.round((this.convertGoogleVisionToPDFTron(coordinates.w)) + this.dpi / 25),
        'x1': Math.round(this.convertGoogleVisionToPDFTron(coordinates.x)- this.dpi / 30),
        'y1': Math.round(this.convertGoogleVisionToPDFTron(coordinates.y)- this.dpi / 30)
      }
    }

    if(area.rect.x1 < 0){
      area.rect.x1 = 0;
    }
    if(area.rect.y1 < 0){
      area.rect.y1 = 0;
    }

    setTimeout(() => {
      if (this.lastRectangleId !== area.rectangleId && this.lastRectangleId !== '' ) {
        if (document.getElementById(this.lastRectangleId)) {
          document.getElementById(this.lastRectangleId).style.border = '';
        }
      }
      const rectElement = document.getElementById(area.rectangleId);
      if (rectElement) {
  
        rectElement.style.border = '3px solid #FF586B';
        rectElement.style.opacity = '1';
        rectElement.style.left = area.rect.x1 + 'px';
        rectElement.style.top = area.rect.y1 + 'px';
        rectElement.style.width = area.rect.width + 'px';
        rectElement.style.height = area.rect.height + 'px';
  
        this.lastRectangleId = area.rectangleId;
        
        setTimeout(() => {
          rectElement.scrollIntoView({ block: 'start', behavior: 'smooth' });
        }, 100);
      }
    }, 100);
  }

  convertGoogleVisionToPDFTron(googleX: number) {
    const scaleFactor = 67 / this.dpi;
    const coor = googleX * scaleFactor;
    return coor;
  }

  showNoDataSelectedMessage() {
    this.pdfSrc = '';
    this.toastrService.warning('No data selected. Please select a transaction to view.', null, {
      positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
    });
  }
  
}
