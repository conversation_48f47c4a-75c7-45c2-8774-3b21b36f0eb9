import { Dashboard } from "app/model/dashboard";
import { SupplierBuyerGroup } from "app/model/supplier-buyer-group";
import { FormConstant } from "app/shared/components/ms-form/constants/form.constant";
import { FormModel } from "app/shared/components/ms-form/models";
import { Act } from "app/shared/components/msx-datatable/enums/act";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { MsxView } from "app/shared/components/msx-view/models/MsxView";
import { WidgetType } from "app/shared/components/msx-view/models/WidgetType";
import { CommonConstant } from "app/shared/constant/common.constant";

export const SupplierBuyerGroupSearchFilter: FormModel<string> = {
    name: 'SupplierBuyerGroupSearchForm',
    direction: FormConstant.DIRECTION_HORIZONTAL,
    autoload: true,
    colSize: 6,
    components: [
        {
            key: 'type',
            label: 'Type',
            placeholder: 'Select type',
            controlType: FormConstant.TYPE_DROPDOWN,
            value: '',
            options: [
                {key: '', value: 'All'},
                {key: 'Supplier', value: 'Supplier'},
                {key: 'Buyer', value: 'Buyer'},
                {key: 'Related Parties', value: 'Related Parties'}
            ]
        },
    ],
    params: [
        {
            key: 'page',
            controlType: FormConstant.TYPE_HIDDEN,
            value: 1
        }
    ]
}

const SupplierBuyerGroupTable: Table<SupplierBuyerGroup> = {
    name: 'groups',
    list: [],
    columns: [
        {
            type: ColumnType.Number,
            prop: 'no',
            label: 'No',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'mainGroup',
            label: 'Main Group',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'createdDate',
            label: 'Created Date',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'type',
            label: 'Type',
            width: 100
        }, 
        {
            type: ColumnType.Action,
            label: 'Action',
            width: 100,
            action: [
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: 'ft-edit',
                    descr: 'Edit',
                    type: Act.Edit,
                },
                {
                    class: CommonConstant.TEXT_DANGER,
                    icon: 'ft-trash-2',
                    descr: 'Delete',
                    type: Act.Delete,
                }
            ]
        }
    ]
}

export const SupplierBuyerGroupListView: MsxView = { 
    title: ' - ' + CommonConstant.SUPPLIER_BUYER_TITLE,
    isTabTitle: true,
    components: [
        {
            type: WidgetType.SearchFilter,
            component: SupplierBuyerGroupSearchFilter
        },
        {
            type: WidgetType.Datatable,
            component: SupplierBuyerGroupTable
        }
    ]
}