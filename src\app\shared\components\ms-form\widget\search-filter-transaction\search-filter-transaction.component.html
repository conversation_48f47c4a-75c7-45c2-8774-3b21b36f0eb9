<div class="row match-height">
  <div class="col-12">
    <div class="card">
      <div class="card-content">
        <div class="card-body">
          <div class="card-title">
            <i class="ft-chevron-down mr-2" [ngClass]="isCollapsed ? 'ft-chevron-right' : 'ft-chevron-down'"
               [attr.aria-expanded]="!isCollapsed" aria-controls="searchForm" (click)="isCollapsed = !isCollapsed"
               style="font-size: 20px; font-weight: 600; cursor: pointer"></i>
            <span>Filter</span>
          </div>
          <hr style="margin-top: 4px !important; margin-bottom: 16px !important; border-top: 1.5px solid #3a3a3c;" />
          <form [formGroup]="searchForm" (ngSubmit)="doFilter()" id="searchForm" [ngbCollapse]="isCollapsed">
            <ng-container *ngIf="formObj.params">
              <input *ngFor="let param of formObj.params" type="hidden" [formControlName]="param.key"
                     [value]="param.value" [id]="param.key" />
            </ng-container>

            <div class="row">
              <div class="col-{{formObj.colSize}}" *ngFor="let question of formObj.components">
              <ng-container class="question" *ngIf="question.key != 'accountNo' && question.key != 'period' && question.key != 'category'">
                  <app-question [question]="question" [form]="searchForm" [direction]="formObj.direction" (selectX)="onSelect($event)"></app-question>
              </ng-container>
              <ng-container class="accountNo" *ngIf="question.key == 'accountNo' ">
                  <app-select-v2 [question]="question" [form]="searchForm" [direction]="formObj.direction" (selectX)="onSelect($event)" (selected)="onSelectDropdown($event)"></app-select-v2>
              </ng-container>
              <ng-container class="category" *ngIf="question.key == 'category' ">
                <app-select-v2 [question]="question" [form]="searchForm" [direction]="formObj.direction" (selectX)="onSelect($event)" (selected)="onSelectDropdownX($event)"></app-select-v2>
            </ng-container>
              <ng-container class="period" *ngIf="question.key == 'period' ">
                  <app-select-v2 [form]="searchForm" [question]="getQuestion(question.key)" [options]="getOptions(question.key)" (selected)="onSelectDropdownX($event)" [direction]="formObj.direction"></app-select-v2>  
              </ng-container>
              </div>
            </div> 

            <div class="row">
              <div class="col-12 text-right">
                <button class="btn btn-danger mr-2" type="button" *ngIf="formObj.exportExcel" (click)="doExport()"><i class="ft-download"></i> {{formObj.exportExcelLabel | translate}}</button>
                <button class="btn btn-secondary mr-2" type="button" (click)="initSearchForm()"><i class="ft-delete"></i> {{'Reset' | translate}}</button>
                <button class="btn btn-primary" type="submit" ><i class="ft-search"></i> {{'Search' | translate}}</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
