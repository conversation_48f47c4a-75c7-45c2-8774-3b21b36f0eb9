<div class="row" style="margin: 15px 0px 0px 0px">
    <div class="col-6">
      <div class="content-header" style="margin-top: 0 !important;">
        {{stateDashboard.dashboardName}} - {{title}}
      </div>
    </div>
    <div class="col-6 text-right"> 
        <a class="msx-action" [ngClass]="'btn btn-primary'" (click)="onBtnClick()"><i [ngClass]="'ft-plus'"></i> {{'Add Group' | translate}}</a>
    </div>
  </div>

  <div class="modal-body">
    <app-msx-datatable [tableObj]="table" [datasource]="datasource" (getPage)="getPage($event)" (itemClickListener)="onItemClickListener($event)"></app-msx-datatable>
  </div>