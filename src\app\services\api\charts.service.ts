import { Injectable } from '@angular/core';
import { oneBarOneLineChartColors, twoBarChartColors, twoBarOneLineChartColors, twoBarTwoLineChartColors, twoLineChartColors } from 'app/shared/charts/charts-colors';

@Injectable({
  providedIn: 'root'
})
export class ChartsService {
  constructor() {
    // Currently empty
  }

  /**
   * Returns chart colors with custom overrides.
   * @param customizations Object mapping index to custom properties.
   * @returns Modified chart colors array.
   */
  getTwoBarChartColors(customizations?: { [index: number]: Partial<any> }): any[] {
    return twoBarChartColors.map((color, index) => ({
      ...color,
      ...(customizations?.[index] || {}) // Override properties for specific indices
    }));
  }

  /**
   * Returns chart colors with custom overrides.
   * @param customizations Object mapping index to custom properties.
   * @returns Modified chart colors array.
   */
  getTwoLineChartColors(customizations?: { [index: number]: Partial<any> }): any[] {
    return twoLineChartColors.map((color, index) => ({
      ...color,
      ...(customizations?.[index] || {}) // Override properties for specific indices
    }));
  }

  /**
   * Returns chart colors with custom overrides.
   * @param customizations Object mapping index to custom properties.
   * @returns Modified chart colors array.
   */
  getOneBarOneLineChartColors(customizations?: { [index: number]: Partial<any> }): any[] {
    return oneBarOneLineChartColors.map((color, index) => ({
      ...color,
      ...(customizations?.[index] || {}) // Override properties for specific indices
    }));
  }

  /**
   * Returns chart colors with custom overrides.
   * @param customizations Object mapping index to custom properties.
   * @returns Modified chart colors array.
   */
  getTwoBarOneLineChartColors(customizations?: { [index: number]: Partial<any> }): any[] {
    return twoBarOneLineChartColors.map((color, index) => ({
      ...color,
      ...(customizations?.[index] || {}) // Override properties for specific indices
    }));
  }

  /**
   * Returns chart colors with custom overrides.
   * @param customizations Object mapping index to custom properties.
   * @returns Modified chart colors array.
   */
  getTwoBarTwoLineChartColors(customizations?: { [index: number]: Partial<any> }): any[] {
    return twoBarTwoLineChartColors.map((color, index) => ({
      ...color,
      ...(customizations?.[index] || {}) // Override properties for specific indices
    }));
  }
}
