import { Path } from "d3-path";

export class PathConstant {
  public static DASHBOARD = "dashboard";
  public static MASTER = "master";
  public static DOCUMENTS = "documents";
  public static SIGNATURE = "signature";
  public static LOGIN = "login";
  public static REGISTER = "register";
  public static ACTIVATION = "activation";
  public static STAMP_DUTY = "stampduty";
  public static USERS = "users";
  public static INQUIRY = "inquiry";
  public static CUSTOMER = "customer";
  public static EMPLOYEE = "employee";
  public static DETAIL = "detail";
  public static BALANCE = "balance";
  public static PAGES = "pages";
  public static DIGI = "digisign";
  public static CHANGE_USER_DATA = "change-user-data";
  public static ADD_BALANCE_TYPE = "add-balance-type";
  public static MONITORING_E_MATERAI = "monitoringematerai";
  public static UPDATE_USER = "update-user";
  public static ESIGN = "esign";
  public static PSRE_SETTING = "psre-setting";
  public static MESSAGE_DELIVERY_REPORT = "message-delivery-report";
  public static IMPORT_AUTOSIGN_BM = "inquiry-import-autosign";
  public static INQUIRY_SIGNING_AUDIT_TRAIL = "inquiry-audit-trail";
  public static BUSINESS_TRANSACTION_GROUP =  "business-transaction-group";
  public static NON_BUSINESS_TRANSACTION_GROUP =  "non-business-transaction-group";
  public static SUPPLIER_BUYER_GROUP =  "supplier-buyer-group";
  public static SUB_GROUP =  "sub-group";
  public static OCR_RESULT =  "ocr-result";
  public static CIRCULAR =  "circular";
  public static ANOMALY = "anomaly";
  public static HUMAN_IN_THE_LOOP = "human-in-the-loop";

  public static NEW = "new";
  public static ADD = "add";
  public static EDIT = "edit";
  public static SETTING = "setting";
  public static VIEW = "view";
  public static TOPUP = "topup";
  public static REVERSAL_TOPUP = "reversalTopup";
  public static BULK_SIGN = "bulkSign";
  public static FORGOT_PASSWORD = "forgot-password";
  public static RESET_PASWORD = "reset-accesscode";
  public static CHANGE_PASSWORD = "change-accesscode";
  public static CHANGE_DATA = "change-data";
  public static CHANGE_DATA_INPUT = "change-data-input";

  public static SETTING_DOCUMENT_TEMPLATE = `/${PathConstant.MASTER}/${PathConstant.DOCUMENTS}/${PathConstant.SETTING}`;
  public static VIEW_DOCUMENT_TEMPLATE = `/${PathConstant.MASTER}/${PathConstant.DOCUMENTS}/${PathConstant.VIEW}`;
  public static SETTING_SEQUENTIAL_SIGNING = `/${PathConstant.MASTER}/${PathConstant.DOCUMENTS}/${PathConstant.SETTING}/sequential`;
  public static VIEW_DOCUMENT_INQUIRY = `/${PathConstant.INQUIRY}/${PathConstant.VIEW}`;
  public static INQUIRY_BULK_SIGN = `/${PathConstant.INQUIRY}/${PathConstant.DETAIL}/${PathConstant.BULK_SIGN}`;
  public static INQUIRY_BULK_SIGN_DIGI = `/${PathConstant.INQUIRY_BULK_SIGN}/${PathConstant.DIGI}`;
  public static DASHBOARD_BALANCE = `/${PathConstant.BALANCE}`;
  public static TOPUP_BALANCE = `/${PathConstant.BALANCE}/${PathConstant.TOPUP}`;
  public static FORGOT_PASSWORD_PAGE = `/${PathConstant.PAGES}/${PathConstant.FORGOT_PASSWORD}`;
  public static RESET_PASSWORD_PAGE = `/${PathConstant.PAGES}/${PathConstant.RESET_PASWORD}`;
  public static CHANGE_PASSWORD_PAGE = `/${PathConstant.PAGES}/${PathConstant.CHANGE_PASSWORD}`;
  public static INQUIRY_USERS = `/${PathConstant.USERS}/${PathConstant.INQUIRY}`;
  public static DETAIL_USERS = `/${PathConstant.USERS}/${PathConstant.INQUIRY}/${PathConstant.DETAIL}`;
  public static EDIT_USER = `/${PathConstant.USERS}/${PathConstant.EDIT}`;
  public static ACTIVATION_STATUS = "/activationStatus";
  public static SETTING_USER_MANAGEMENT = `/${PathConstant.USERS}/setting-user-management`;
  public static EDIT_USER_DATA = `/edit-user-data`;
  public static INSERT_USER_MANAGEMENT = `/${PathConstant.USERS}/insert-user-management`;
  public static EDIT_ACTIVATION_STATUS = `/${PathConstant.USERS}/edit-activation-status`;

  // CHANGE USER DATA
  public static CHANGE_DATA_USER = `/${PathConstant.CHANGE_USER_DATA}/${PathConstant.CHANGE_DATA}`;
  public static CHANGE_DATA_USER_INPUT = `/${PathConstant.CHANGE_USER_DATA}/${PathConstant.CHANGE_DATA_INPUT}`;

  // PSRE SETTING
  public static EDIT_SETTING_PSRE = `/${PathConstant.PSRE_SETTING}/${PathConstant.EDIT}`;

  //MANUAL STAMP 
  public static MANUAL_STAMP_SETTING = `/${PathConstant.MASTER}/${PathConstant.DOCUMENTS}/manual-stamp/setting`;

  public static IMPORT_EXCEL_AUTOSIGN_BM = `/${PathConstant.INQUIRY}/insert-excel-bm-autosign`;

  //DASHBOARD
  public static DASHBOARD_DETAIL = `/${PathConstant.DASHBOARD}/${PathConstant.DETAIL}`;
  
  //ANOMALY
  public static ANOMALY_DETAIL = `/${PathConstant.DASHBOARD}/${PathConstant.ANOMALY}/${PathConstant.DETAIL}`;

  //OCR RESULT
  public static DETAIL_OCR_RESULT = `/${PathConstant.DASHBOARD}/${PathConstant.OCR_RESULT}/${PathConstant.DETAIL}`;
  
  //OCR RESULT
  public static VIEW_TRANSACTION_CIRCULAR = `/${PathConstant.DASHBOARD}/${PathConstant.CIRCULAR}/${PathConstant.VIEW}`;
  
  //BUSINESS TRANSACTION GROUP
  public static DETAIL_BUSINESS_TRANSACTION_GROUP = `/${PathConstant.DASHBOARD}/${PathConstant.BUSINESS_TRANSACTION_GROUP}/${PathConstant.DETAIL}`;
   
  //NON BUSINESS TRANSACTION GROUP
  public static DETAIL_NON_BUSINESS_TRANSACTION_GROUP = `/${PathConstant.DASHBOARD}/${PathConstant.NON_BUSINESS_TRANSACTION_GROUP}/${PathConstant.DETAIL}`;
  
  //SUPPLIER BUYER GROUP
  public static SUPPLIER_BUYER_SUB_GROUP = `/${PathConstant.DASHBOARD}/${PathConstant.SUPPLIER_BUYER_GROUP}/${PathConstant.SUB_GROUP}`;
  public static EDIT_SUPPLIER_BUYER_SUB_GROUP_TRANSACTION = `/${PathConstant.DASHBOARD}/${PathConstant.SUPPLIER_BUYER_GROUP}/${PathConstant.SUB_GROUP}/${PathConstant.DETAIL}`;

  //TENANT
  public static TENANT = "tenant";
  public static ADD_TENANT = `/${PathConstant.TENANT}/${PathConstant.ADD}`;
  public static EDIT_TENANT = `/${PathConstant.TENANT}/${PathConstant.EDIT}`;
  
}
