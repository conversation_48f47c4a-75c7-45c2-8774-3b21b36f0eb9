import { ChangeDetectorRef, Component, EventEmitter, Input, NgZone, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import swal from 'sweetalert2';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { OssService } from 'app/services/api/oss.service';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { MsxFormControlService } from 'app/shared/components/ms-form/msx-form-control.service';
import { FileItem, FileUploader } from 'ng2-file-upload';
import { ToastrService } from 'ngx-toastr';
import { v4 as uuidv4 } from 'uuid';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { GlobalService } from 'app/shared/data/global.service';
import { OssDirectService } from 'app/services/api/oss-direct.service';
import { OssSignatureResponse } from 'app/shared/dto/oss/oss-signature.response';
import { DashboardService } from 'app/services/api/dashboard.service';
import { AddDashboardRequest } from 'app/model/api/dashboard/add-dashboard.request';
import { DashboardFileContainer } from 'app/model/custom/dashboard-file-container';
import { DataService } from 'app/services/api/data.service';
import * as PDFJS from "pdfjs-dist";
import { PasswordDocComponent } from './password-doc/password-doc.component';
import { Router } from '@angular/router';
import { PathConstant } from 'app/shared/constant/PathConstant';

@Component({
  selector: 'app-upload',
  templateUrl: './upload.component.html',
  styleUrls: ['./upload.component.scss']
})
export class UploadComponent implements OnInit {

  @Input() dashboardName: string;
  @Input() isBankStatement: boolean;
  @Output() result = new EventEmitter<void>();

  supportedFileExtension = ['pdf', 'jpg', 'jpeg', 'png'];

  uploader: FileUploader = new FileUploader({
    url: '',
    isHTML5: true
  });

  filenameContainer: DashboardFileContainer[] = [];
  hasBaseDropZoneOver: boolean;
  formObj: FormModel<any>;
  msxForm: FormGroup;
  isAutoConsolidate: boolean = false;
  currentlyUploading: boolean = false; // used to prevent double process when double click
  maxFileSizeInBytes: number = ********; // 1024*1024*10 -> 10MB
  minimumImageDimension: number = 0;
  maximumTotalImage: number = 0;

  constructor(
    private router: Router,
    private readonly formControlService: MsxFormControlService,
    private readonly toastService: ToastrService,
    private activeModal: NgbActiveModal,
    private modalService: NgbModal, 
    private dataService: DataService,
    private ossService: OssService,
    private dashboardService: DashboardService,
    private ossDirectService: OssDirectService,
    private toastrService: ToastrService,
    private ngZone: NgZone,
    private global: GlobalService
  ) { 
    this.hasBaseDropZoneOver = false; 
    this.validateImage();
  }

  ngOnInit(): void { 
    this.getMinimumImageDimension()
    this.getMaxTotalImage()
    this.getMaxFileSize();
    this.prepareFormModel();
    const controls = this.formObj.components;
    this.msxForm = this.formControlService.toFormGroup(controls); 
  }

  /**
   * Ketika upload file (baik dari drop atau pick),
   * Set isError berdasarkan apakah pdf pakai password atau tidak,
   * Set isReady = true untuk pdf tidak berpassword, selain itu false
   *
   * Hanya set isReady = true jika password yang diisi sudah benar
   */  
  onFileSelected(event: any) { 

    this.validateMaxFileSize();
    this.validateUnsupportedFileExtension();  
    this.checkSelectedFiles();

  } 

  checkSelectedFiles(){  // Convert FileList to an array
    this.uploader.queue.forEach(async item => {
      const duplicate = this.filenameContainer.some(file => file.actualFilename === item.file.name);

      if(!duplicate){
        this.validatePasswordPdf(item);  
        this.generateOssFileName(item);
      }
       
    }); 
    console.log('uploader queue', this.uploader.queue);
    console.log('Filename container', this.filenameContainer);
  }

  getMaxFileSize() {
    this.dataService.getGeneralSetting('OSS_DIRECT_UPLOAD_MAX_SIZE').subscribe(response => {
      if (response.status.code !== 0) {
        return;
      }

      const maxFileSize = Number(response.value);
      this.maxFileSizeInBytes = maxFileSize;
    });
  }

  getMinimumImageDimension() {
    this.dataService.getGeneralSetting('IMG_MIN_DIMENSION').subscribe(response => {
      if (response.status.code !== 0) {
        return;
      }

      const minImgDimension = Number(response.value);
      this.minimumImageDimension = minImgDimension;
    });
  }

  getMaxTotalImage() {
    this.dataService.getGeneralSetting('IMG_MAX_TOTAL').subscribe(response => {
      if (response.status.code !== 0) {
        return;
      }

      const maxTotalImg = Number(response.value);
      this.maximumTotalImage = maxTotalImg;
    });
  }

  fileOverBase(e: any): void {
    this.hasBaseDropZoneOver = e;
  }

  validateMaxFileSize() {
    console.log('Validating max file size');
    const unsupportedFiles = this.uploader.queue.filter(item => {
      return item.file.size > this.maxFileSizeInBytes;
    });

    const numberOfFileDeleted = unsupportedFiles.length;
    if (numberOfFileDeleted == 0) {
      return;
    }

    unsupportedFiles.forEach(unsupportedFile => {
      this.deleteFile(unsupportedFile);
    });

    const maxFileSizeInMB = this.maxFileSizeInBytes / 1024 / 1024;
    const maxFileSizeString = maxFileSizeInMB + ' MB';

    this.toastService.error(`${numberOfFileDeleted} file(s) removed because of file size limit of ${maxFileSizeString}.`, null, {
      positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
    });
  }

  validateUnsupportedFileExtension() {
    const unsupportedFiles = this.uploader.queue.filter(item => {
      const fileExtension = item.file.name.split('.').pop().toLowerCase();
      return !this.supportedFileExtension.includes(fileExtension);
    });

    const numberOfFileDeleted = unsupportedFiles.length;
    if (numberOfFileDeleted == 0) {
      return;
    }

    unsupportedFiles.forEach(unsupportedFile => {
      this.deleteFile(unsupportedFile);
    });

    this.toastService.error(`${numberOfFileDeleted} file(s) removed because of unsupported extension.`, null, {
      positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
    });
  }

  calculateProgressBar() { 
    const successfulUploads = this.uploader.queue.filter(item => item.isSuccess).length;
    if (successfulUploads === 0) {
      return 0;
    } 
    const progress = (successfulUploads * 100) / this.filenameContainer.length;
    return parseFloat(progress.toFixed(2));
  }
  

  onInput(event: any) { 
  }

  prepareFormModel() {
    this.formObj = {
      name: 'dashboardFiles',
      colSize: 12,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        {
          key: 'dashboardName',
          label: 'Dashboard Name',
          placeholder: 'Type your dashboard name here',
          controlType: FormConstant.TYPE_TEXT,
          value: this.dashboardName,
          maxLength: 64,
          required: true,
          readonly: this.dashboardName && this.dashboardName !== '',
          validations: [
            {type: 'required', message: 'Dashboard Name cannot be empty'}, 
          ]
        }
      ],
      params: [
      ]
    }
  }

  closeUploadModal() {

    if (this.uploader.queue.length == 0) {
      this.activeModal.dismiss("cancel");
      return;
    }

    swal.fire({
      title: 'Are you sure?',
      text: 'Your uploaded files cannot be recovered.',
      icon: 'warning',
      reverseButtons: true,
      showCancelButton: true,
      showConfirmButton: true,
      cancelButtonText: 'No',
      confirmButtonText: 'Yes'
    }).then(result => {
      if (!result.isConfirmed) {
        return;
      }

      this.deleteAllFile();
      this.activeModal.dismiss("cancel");

    });
  }

  uploadFile(item: FileItem) {

    if (this.currentlyUploading) {
      return;
    }

    this.currentlyUploading = true;
    this.ossService.generateSignature().subscribe(response => {
      if (response.status.code !== 0) {
        this.toastService.error('Save failed. Please try once again.', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        this.currentlyUploading = false;
        return;
      }
      const fileContainer = this.filenameContainer.find(file => file.actualFilename === item.file.name);
      this.uploadFileToOss(item, response, fileContainer.ossFilename);
      this.currentlyUploading = false;
    });
  }

  uploadAllFile() {

    if (this.currentlyUploading) {
      return;
    }

    this.currentlyUploading = true;

    this.ossService.generateSignature().subscribe(response => {
      if (response.status.code !== 0) {
        this.currentlyUploading = false;
        return;
      }

      this.uploader.queue.forEach(item => {
        const fileContainer = this.filenameContainer.find(file => file.actualFilename === item.file.name);
        if (!item.isSuccess) {
          this.uploadFileToOss(item, response, fileContainer.ossFilename);  
        }
      });

      this.currentlyUploading = false;
    });
  }

  uploadFileToOss(item: FileItem, ossSignature: OssSignatureResponse, ossFilename:string) {
    if (item.isSuccess) {
      return;
    }
    
   const url = ossSignature.host;
    
    const formData = new FormData();
    formData.append('name', ossFilename);
    formData.append('policy', ossSignature.policy);
    formData.append('OSSAccessKeyId', ossSignature.ossAccessKeyId);
    formData.append('success_action_status', '200');
    formData.append('signature', ossSignature.signature);
    formData.append('key', ossSignature.dir + ossFilename);
    formData.append('file', item._file);
    
    this.ossDirectService.uploadFile(formData, ossSignature.host).subscribe(
      () => {
        console.log('Filename container', this.filenameContainer);
        item.isSuccess = true;
      },
      (error) => {
        console.log('Upload to OSS error', error);
      }
    );
  } 
  
  generateOssFileName(item){
    const ossFilename = uuidv4() + "_" + this.global.user.loginId + "_" + item.file.name;
    this.filenameContainer.push(new DashboardFileContainer(item.file.name, ossFilename));
  }

  deleteFile(item: FileItem) {
    this.filenameContainer = this.filenameContainer.filter(x => x.actualFilename !== item.file.name);
    this.uploader.removeFromQueue(item);
  }

  deleteAllFile() {
    this.uploader.clearQueue();
    this.filenameContainer = [];
  }

  openDeleteAllFileModal() {
    
    swal.fire({
      title: 'Are you sure?',
      text: 'Your uploaded files cannot be recovered.',
      icon: 'warning',
      reverseButtons: true,
      showCancelButton: true,
      showConfirmButton: true,
      cancelButtonText: 'No',
      confirmButtonText: 'Yes'
    }).then(result => {
      if (!result.isConfirmed) {
        return;
      }

      this.deleteAllFile();
    });

  }

  async validatePasswordPdf(item){
    const pdfFormat = item.file.name.split('.').pop().toLowerCase();

    if (pdfFormat === 'pdf') {
      const isDocumentNeededPassword = await this.isPdfEncrypted(item); 
      console.log('isDocumentNeededPassword', isDocumentNeededPassword);
      item.isError = isDocumentNeededPassword;
      item.isReady = !isDocumentNeededPassword; 
      console.log('item', item);
    }else{
      item.isReady = true; 
    }
  }

  async getImageSize(item: any): Promise<{ width: number; height: number }> {
    const file = item.file.rawFile;
  
    return new Promise((resolve, reject) => {
      if (!file || !file.type.startsWith('image/')) {
        reject('Not a valid image file');
        return;
      }
  
      const reader = new FileReader();
  
      reader.onload = (event: any) => {
        const img = new Image();
        img.onload = () => {
          resolve({ width: img.width, height: img.height });
        };
        img.onerror = () => reject('Failed to load image');
        img.src = event.target.result;
      };
  
      reader.onerror = () => reject('Failed to read file');
      reader.readAsDataURL(file);
    });
  }

  //check is document needed password
  async isPdfEncrypted(item: FileItem) {
    const file = item._file;
    const arrayBuffer = await file.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);
 
    try {
      await PDFJS.getDocument({data: uint8Array, isEvalSupported: false}).promise;
      return false;
    } catch (error: any) {
      return true;
    }
  }  
   

  allFilesUploaded(): boolean {
    return this.uploader.queue.every(item => item.isSuccess);
  }

  allFilesUnlockPassword(): boolean { 
    return this.uploader.queue.every(item => item.isReady);
  }

  changeAutoConsolidate(event:boolean){
    this.isAutoConsolidate = event;
  }

  saveDashboard() {
    console.log('Save dashboard', this.filenameContainer); 

    if (this.filenameContainer.length === 0) {
      this.toastService.error('Files have not been uploaded', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
      return;
    }

    if (!this.allFilesUploaded()) {
      this.toastService.error('Not all files have been uploaded', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
      return;
    }

    if (!this.allFilesUnlockPassword()) {
      this.toastService.error('Not all files have been Unlock! Please input the password for all files', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
      return;
    }

    const autoConsolidate = (<HTMLInputElement> document.getElementById('autoConsolidate')).checked;
    const autoConsolidateLabel = autoConsolidate ? '1' : '0';
    const isHitl = (<HTMLInputElement> document.getElementById('hitl')).checked;
    const isHitlLabel = isHitl ? '1' : '0';

    const request = new AddDashboardRequest();
    request.files = this.filenameContainer;

    if (!this.isBankStatement) {
      request.dashboardName = this.msxForm.value.dashboardName;
      request.isAutoConsolidate = autoConsolidateLabel;
      request.isHitl = isHitlLabel;

      this.dashboardService.addDashboard(request).subscribe(response => {
        if (response["status"]["code"] == 0) {
          this.toastrService.success('Dashboard successfully added!', null, {
            positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
          });
          this.result.emit();
          this.activeModal.close('SUCCESS');
        }  
      });
    } else {
      request.dashboardName = this.dashboardName;
      request.isAutoConsolidate = autoConsolidateLabel;
      request.isHitl = isHitlLabel;

      this.dashboardService.addNewBankStatement(request).subscribe(response => {
        if (response["status"]["code"] == 0) {
          this.toastrService.success('Data successfully added!', null, {
            positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
          });
          this.result.emit();
          this.activeModal.close('SUCCESS');
          this.nativateToDashboardPage(autoConsolidate);
        }  
      });
    }
  }

  nativateToDashboardPage(autoConsolidate: boolean) {
    if (!autoConsolidate) {
      return;
    }

    this.router.navigate([PathConstant.DASHBOARD]);
  }
 
  openModal(item: FileItem){
    const modal = this.modalService.open(PasswordDocComponent, {
      centered: true,
      backdrop: 'static',
      size: 's'
    });
    modal.componentInstance.file = item; 
    modal.componentInstance.passwordUnlock.subscribe((result: any) => {
      item.isReady = result.success; // Store the emitted value

      const currentFile = this.filenameContainer.find(file => file.actualFilename === item.file.name);
      currentFile.password = result.password; // Set the password
       
      console.log('filenameContainer', this.filenameContainer); 
      console.log('Password Unlock Status:', item.isReady); 
    });
  }

  validateImage(){
    this.uploader.onAfterAddingAll = async (fileItems: FileItem[]) => {
      const filesRejectedByDimension: FileItem[] = [];
      const newItems: FileItem[] = [];

      // 1. check duplicate file
      fileItems.forEach(fileItem => {
        const isDuplicate = this.uploader.queue.some(
          item => item !== fileItem && item.file.name === fileItem.file.name
        );
        if (isDuplicate) {
          this.uploader.removeFromQueue(fileItem);
        } else {
          newItems.push(fileItem);
        }
      });
   
      if (newItems.length < fileItems.length) {
        this.toastService.error('Duplicate file(s) will not be processed', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
      }
    
      // 2. Count incoming image files
      const allImageCount = this.uploader.queue.filter(item =>
        item.file.type.startsWith('image/')
      ).length;

      const newImageFiles = newItems.filter(item => {
        return item.file.type.startsWith('image/');
      });
      const currentImageCount = allImageCount - newImageFiles.length;
      const totalAfterAdding = currentImageCount + newImageFiles.length;
      console.log("all image to be validate: " + allImageCount);
      console.log("new image count: " + newImageFiles.length);
      console.log("currentImageCount: " + currentImageCount);
      console.log("totalAfterAdding: " + totalAfterAdding);
    
      if (totalAfterAdding > this.maximumTotalImage) {
        this.toastService.error('Cannot add images. Upload limit of '+this.maximumTotalImage+' exceeded.', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        newImageFiles.forEach(item => this.uploader.removeFromQueue(item));
      } 
      else { 
        // 3. Check image dimensions
        const dimensionCheckPromises = newItems.filter(item => item.file.type.startsWith('image/')).map(fileItem =>
          this.getImageSize(fileItem)
            .then(size => {
              if (size.width < this.minimumImageDimension && size.height < this.minimumImageDimension) {
                filesRejectedByDimension.push(fileItem);
              }
            })
            .catch(error => {
              filesRejectedByDimension.push(fileItem);
            })
        );
    
        await Promise.all(dimensionCheckPromises);
    
        if (filesRejectedByDimension.length > 0) {
          newImageFiles.forEach(file => this.deleteFile(file));
          this.toastService.error('Image(s) do not meet minimum size ('+this.minimumImageDimension+'px)', null, {
            positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
          });
        }
      }
    };
  }

}
