<div class="row" style="margin-top: 15px">
    <div class="col-12 col-md-8 px-4">
      <div class="content-header" style="margin-top: 0 !important;">
        {{dashboardName }} - REKENING KORAN 1
      </div>
    </div>
    <div class="col-12 col-md-4 px-4 text-right"> 
        <a *ngIf="page==1" class="msx-action" [ngClass]="'btn btn-warning mr-1'" (click)="goBack()"><i [ngClass]="'ft-arrow-left-circle'"></i> {{'Back' | translate}}</a>
        <a class="msx-action btn btn-primary mr-1" (click)="saveAndBack()">{{ page === 1 ? 'Save & Exit' : 'Save & Back' | translate}}</a>
        <ng-container *ngIf="page==1">
          <a class="msx-action btn btn-primary mr-1" (click)="saveAndNext()">{{'Save & Next' | translate}}</a>
          <a *ngIf="isHitl === 1" class="msx-action btn btn-danger mr-1" (click)="openRejectModal()">
              <i class="ft-x"></i> {{'Reject' | translate}}
          </a>
    </ng-container>
        <ng-container *ngIf="page==2">
            <a class="msx-action btn btn-primary mr-1" (click)="saveAndExit('EDIT')">
                {{'Save & Exit' | translate}}
            </a>

            <ng-container *ngIf="isHitl === 1">
                <a class="msx-action btn btn-success" (click)="openSubmitModal()">
                    <i class="ft-check"></i> {{'Submit' | translate}}
                </a>
            </ng-container>

            <ng-container *ngIf="isHitl !== 1">
                <a class="msx-action btn btn-primary" (click)="saveAndExit('COMPLETE')">
                    {{'Submit' | translate}}
                </a>
            </ng-container>
        </ng-container>
    </div>
</div>   
<div class="row">
    <div class="col-12 col-md-6"> 
         <app-personal-info-ocr-result *ngIf="page==1" [state]="state" (onClickInput)="moveTo($event)"></app-personal-info-ocr-result>
      <div class="scrollable-content"> 
        <div class="card">
           <app-transaction-summary-ocr-result *ngIf="page==2" [state]="state" ></app-transaction-summary-ocr-result>
           <app-transaction-history-ocr-result *ngIf="page==2" [state]="state" (onClickRow)="moveTo($event)"></app-transaction-history-ocr-result>
        </div>
      </div>
    </div>
    <div class="col-12 col-md-6">
        <ngx-spinner></ngx-spinner>
        <div class="pdf-frame py-3">
            <pdf-viewer [src]="pdfSrc" 
            [render-text]="true"   
            (page-rendered)="pageRendered($event)" 
            [(page)]="pageVariable"
            style="display: block; height: 95vh"></pdf-viewer>
        </div>
    </div>
</div>

<!-- Modal untuk HITL Action (Submit/Reject) - Simple Version -->
<ng-template #hitlActionModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title">
            {{ modalConfig.title | translate }}
        </h4>
    </div>

    <div class="modal-body">
        <div class="form-group">
            <textarea
                class="form-control"
                [(ngModel)]="modalNotes"
                [placeholder]="modalConfig.placeholder | translate"
                rows="10"
                style="min-height: 200px; resize: vertical;">
            </textarea>
        </div>

        <!-- Validation Error untuk Reject -->
        <div class="alert alert-danger" *ngIf="showValidationError">
            <i class="ft-alert-triangle"></i>
            {{ 'Please provide a reason for rejection' | translate }}
        </div>
    </div>

    <div class="modal-footer justify-content-end">
        <button type="button" class="btn btn-secondary mr-2" (click)="closeModal()">
            {{'Back' | translate}}
        </button>

        <button type="button" [class]="modalConfig.submitButtonClass" (click)="confirmAction()"
                [disabled]="modalConfig.action === 'reject' && !modalNotes?.trim()">
            {{ modalConfig.submitText | translate }}
        </button>
    </div>
</ng-template>
