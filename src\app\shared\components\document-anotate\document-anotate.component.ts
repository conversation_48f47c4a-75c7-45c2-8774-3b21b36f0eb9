import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  NgZone,
  OnInit,
  Output,
  ViewChild,
  ViewEncapsulation
} from '@angular/core';
import {PDFDocumentProxy, PDFPageProxy, RenderTask} from 'pdfjs-dist';
import * as PDFJS from "pdfjs-dist";
import {BoxAnnotation} from '../../../model/box-annotation';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {LabelModalComponent} from './component/label-modal/label-modal.component';
import {ColumnMode} from '@swimlane/ngx-datatable';
import {SignerType} from '../../data/signer-type';
import {Signer} from './model/signer';
import {URLConstant} from '../../constant/URLConstant';
import {HttpClient} from '@angular/common/http';
import { DeviceDetectorService } from 'ngx-device-detector';
import { PageViewport } from 'pdfjs-dist/types/src/display/display_utils';

@Component({
  selector: 'app-document-anotate',
  templateUrl: './document-anotate.component.html',
  styleUrls: ['./document-anotate.component.scss', '/assets/sass/libs/datatables.scss'],
  encapsulation: ViewEncapsulation.None
})
export class DocumentAnotateComponent implements OnInit, AfterViewInit {

  @Input() document: string;
  @Input() users: Signer[];
  @Input() signer: Signer[];
  @Input() readonly = false;
  @Input() sdtOnly  = false;

  @Output() annotations: EventEmitter<any> = new EventEmitter<any>();
  @Output() numOfPage: EventEmitter<number> = new EventEmitter<number>();

  @ViewChild('container', {static: false}) public container: ElementRef;

  private containerPos: { left: number, top: number, right: number, bottom: number };

  imgSrc: string;
  imgWidth: number;
  imgHeight: number;
  errorMessage: string;

  pdf: PDFDocumentProxy;
  currentPage = 1;
  size: number;

  mId = 1;

  isMobile = false;

  public ColumnMode = ColumnMode;
  public boxs: BoxAnnotation[] = [];

  private signerTypes: any[];

  constructor(private http: HttpClient, private cdr: ChangeDetectorRef, private modalService: NgbModal, private ngZone: NgZone,
    private deviceService: DeviceDetectorService) {
      this.isMobile = deviceService.isMobile();
  }

  ngAfterViewInit(): void {
    // Intentionally empty
  }

  async ngOnInit(): Promise<void> {
    // PDFJS.disableWorker = true;
    // this.initSigns();
    try {
      await this.renderPage().then((x) => {
        this.getSignerType();
        this.validateStamping();
      })
    } catch (error) {
      this.errorMessage = error;
      console.log(error);
    }
  }

  validateStamping() {
    if (!this.sdtOnly) {
      return;
    }

    if (this.signer) {
      console.log('start:', this.signer.length);
      for (const sign of this.signer) {
        if (sign.signPage > this.size) {
          const idx = this.signer.indexOf(sign);
          this.signer.splice(idx);
        }
      }

      console.log('end:', this.signer.length);
    }

    console.log('Total Page: ', this.size);
    console.log('Setting Mode: ', 'sdt');
  }

  public addSigning() {
    console.log(`{width: ${this.imgWidth}, height: ${this.imgHeight} }`);
    const container = this.container.nativeElement.getBoundingClientRect();
    this.containerPos = container;
    console.log('Container', container);

    const modal = this.modalService.open(LabelModalComponent, {centered: true, size: 'sm'});

    if (this.users) {
      modal.componentInstance.users = this.users;
    }

    modal.dismissed.subscribe(result => {
      console.log('Result', result);
      if (!result) {
        return;
      }

      this.ngZone.run(() => {
        const annotation  = new BoxAnnotation();
        annotation.top    = 24;
        annotation.left   = 24;
        annotation.width  = 130;
        annotation.height = 65;
        annotation.page   = this.currentPage;
        annotation.type   = SignerType.TTD;
        annotation.id     = this.mId++;
        annotation.viewport = container;

        if (this.users) {
          const user = this.users.find(usr => usr.email === result?.email);
          annotation.label = user.name;
          annotation.name  = user.name;
          annotation.email = user.email;
          annotation.phone = user.phone;
        } else {
          annotation.label  = result?.value;
          annotation.signerType = result?.key;
        }

        this.pushData(annotation);
      })
    });
  }

  public addParaf() {
    console.log(`{width: ${this.imgWidth}, height: ${this.imgHeight} }`);
    const container = this.container.nativeElement.getBoundingClientRect();
    this.containerPos = container;
    console.log('Container', container);

    const modal = this.modalService.open(LabelModalComponent, {centered: true, size: 'sm'});
    modal.dismissed.subscribe(result => {
      console.log('Result', result);
      if (!result) {
        return;
      }

      this.ngZone.run(() => {
        const annotation  = new BoxAnnotation();
        annotation.top    = 24;
        annotation.left   = 24;
        annotation.width  = 65;
        annotation.height = 65;
        annotation.page   = this.currentPage;
        annotation.type   = SignerType.PRF;
        annotation.label  = result?.value;
        annotation.signerType = result?.key;
        annotation.id     = this.mId++;
        annotation.viewport = container;
        this.pushData(annotation);
      })
    });
  }

  public addMaterai() {
    console.log(`{width: ${this.imgWidth}, height: ${this.imgHeight} }`);
    const container = this.container.nativeElement.getBoundingClientRect();
    this.containerPos = container;
    console.log('Container', container);

    const annotation  = new BoxAnnotation();
    annotation.top    = 24;
    annotation.left   = 24;
    annotation.width  = 130;
    annotation.height = 130;
    annotation.page   = this.currentPage;
    annotation.type   = SignerType.SDT;
    annotation.label  = 'Meterai';
    annotation.id     = this.mId++;
    annotation.viewport = container;
    this.pushData(annotation);
  }

  public pushData(data: BoxAnnotation) {
    this.boxs.push(data);
    // this.annotations.emit(this.boxs);
    // this.data.push({label: data.label, type: data.type, page: data.page});
    this.cdr.detectChanges();
  }

  public async nextPage() {
    this.currentPage = this.currentPage + 1;
    await this.renderPage();
  }

  public async previousPage() {
    this.currentPage = this.currentPage - 1;
    await this.renderPage();
  }

  public hasNext() {
    return this.size > this.currentPage;
  }

  public canBack() {
    return this.currentPage > 1;
  }

  public getBoundingClient(data: {result: BoxAnnotation, transform: string}) {
    const result = data.result;
    this.containerPos = this.container.nativeElement.getBoundingClientRect();

    const location = {lx: 0, ly: 0, rx: 0, ry: 0};
    location.lx = result.position.left - this.containerPos.left;
    location.ly = this.imgHeight + this.containerPos.top - result.position.bottom;
    location.rx = result.position.right - this.containerPos.left;
    location.ry = this.imgHeight + this.containerPos.top - result.position.top;
    console.log(`ttdx-${data.result.id}`, location);


    //vida Location
    const Vidalocation = {xPoint: 0, yPoint: 0, height: 0, width: 0};
    Vidalocation.xPoint = Math.round(location.lx);
    Vidalocation.yPoint = Math.round(location.ly);
    Vidalocation.width = Math.round(location.rx - location.lx);
    Vidalocation.height = Math.round(location.ry - location.ly);
    console.log('VIDA POSITION', Vidalocation);

    // Get location coordinate for tekenAja
    const dpi = this.getDPI();
    const apercentWidth = (((result.position.left - this.containerPos.left) * 25.4) / dpi / ((this.imgWidth * 25.4) / dpi)) * 100;
    const x = Number(((apercentWidth / 100) * (this.imgWidth * 0.352778)).toFixed(2));

    const apercentHeight = (((result.position.top - this.containerPos.top) * 25.4) / dpi / ((this.imgHeight * 25.4) / dpi)) * 100;
    const y = Number(((apercentHeight / 100) * (this.imgHeight * 0.352778)).toFixed(2));

    const widthPercent = ((result.width * 25.4) / dpi / ((this.imgWidth * 25.4) / dpi)) * 100;
    const w = Number(((widthPercent / 100) * (this.imgWidth * 0.352778)).toFixed(2));

    const heightPercent = ((result.height * 25.4) / dpi / ((this.imgHeight * 25.4) / dpi)) * 100;
    const h = Number(((heightPercent / 100) * (this.imgHeight * 0.352778)).toFixed(2));

    // View output coordinate
    console.log('TekenAja x', x);
    console.log('TekenAja y', y);
    console.log('TekenAja w', w);
    console.log('TekenAja h', h);

    // Get location coordinate for Privy
    const apercentWidthP = (((result.position.left - this.containerPos.left) * 25.4) / dpi / ((this.imgWidth * 25.4) / dpi)) * 100;
    const xPrivy = Number(((apercentWidthP / 100) * (this.imgWidth * 0.352778)).toFixed(2));
    const xP = Number(((xPrivy * dpi)/25.4).toFixed());

    const apercentHeightP = (((result.position.top - this.containerPos.top) * 25.4) / dpi / ((this.imgHeight * 25.4) / dpi)) * 100;
    const yPrivy = Number(((apercentHeightP / 100) * (this.imgHeight * 0.352778)).toFixed(2));
    const yP = Number(((yPrivy * dpi)/25.4).toFixed());

    const widthPercentP = ((result.width * 25.4) / dpi / ((this.imgWidth * 25.4) / dpi)) * 100;
    const wPrivy = Number(((widthPercentP / 100) * (this.imgWidth * 0.402778)).toFixed(2));
    const wP = Number(((wPrivy * dpi)/25.4).toFixed());

    const heightPercentP = ((result.height * 25.4) / dpi / ((this.imgHeight * 25.4) / dpi)) * 100;
    const hPrivy = Number(((heightPercentP / 100) * (this.imgHeight * 0.431076)).toFixed(2));
    const hP = Number(((hPrivy * dpi)/25.4).toFixed());

    console.log('Privy x', xP);
    console.log('Privy y', yP);
    console.log('Privy w', wP);
    console.log('Privy h', hP);


    const temp = this.boxs.find(box => box.id === result.id);
    temp.coordinate = JSON.stringify({x: x, y: y, w: w, h: h});
    temp.coordinateVida = JSON.stringify({
      x: Vidalocation.xPoint, 
      y: Vidalocation.yPoint, 
      w: Vidalocation.width, 
      h: Vidalocation.height});
    temp.coordinatePrivy = JSON.stringify({x: xP, y: yP, w: wP, h: hP});  
    temp.lx = location.lx;
    temp.ly = location.ly;
    temp.rx = location.rx;
    temp.ry = location.ry;
    temp.x  = x;
    temp.y  = y;
    temp.w  = w;
    temp.h  = h;
    
    // console.log('SrcBox', this.boxs);
    this.annotations.emit(this.boxs);
  }

  public remove(result) {
    console.log('Remove', result);
    const temp = this.boxs.find(x => x.id === result.id);
    this.boxs.splice(this.boxs.indexOf(temp), 1);
    this.cdr.markForCheck();
    this.annotations.emit(this.boxs);
  }

  private async renderPage() {
    try {
      await this.showPDF();
    } catch (error) {
      this.errorMessage = error;
      console.log(error);
    }
  }

  private async showPDF(): Promise<void> {
    const docStr: string[] = this.document.split(',');
    await PDFJS.getDocument({data: window.atob(docStr[1]), isEvalSupported: false}).promise.then(
      (pdf) => {
        this.size = pdf.numPages;
        this.numOfPage.emit(this.size);
        pdf.getPage(this.currentPage).then(
          (page) => {
            const viewport: PageViewport = page.getViewport({scale: 1});

            const canvas: HTMLCanvasElement = document.createElement('canvas');
            canvas.height  = viewport.height;
            canvas.width   = viewport.width;
            this.imgHeight = viewport.height;
            this.imgWidth  = viewport.width;

            const context: CanvasRenderingContext2D = canvas.getContext('2d');
            page.render({
              canvasContext: context,
              viewport: viewport
            }).promise.then(
              (x) => {
                this.imgSrc = canvas.toDataURL();
                console.log(this.imgSrc);
                this.cdr.markForCheck();
              }
            );

            this.imgSrc = canvas.toDataURL();
            console.log(this.imgSrc);
            this.cdr.markForCheck();
          }
        )
      }
    );
  }

  private async getPage(pageNo: number): Promise<PDFPageProxy> {
    return await this.pdf.getPage(pageNo);
  }

  // private getCanvas(viewport: PDFPageViewport): HTMLCanvasElement {
  //   const canvas: HTMLCanvasElement = document.createElement('canvas');
  //   canvas.height  = viewport.height;
  //   canvas.width   = viewport.width;
  //   this.imgHeight = viewport.height;
  //   this.imgWidth  = viewport.width;
  //   return canvas;
  // }

  // private createRenderTask(
  //   page: PDFPageProxy,
  //   canvas: HTMLCanvasElement,
  //   viewport: PDFPageViewport
  // ): PDFRenderTask {
  //   const context: CanvasRenderingContext2D = canvas.getContext('2d');
  //   return page.render({
  //     canvasContext: context,
  //     viewport: viewport
  //   });
  // }

  private setDisplayValues(canvas: HTMLCanvasElement): void {
    this.imgSrc = canvas.toDataURL();
    this.cdr.markForCheck();
  }

  private initSigns() {
    if (!this.signer) {
      return;
    }

    this.containerPos = this.container.nativeElement.getBoundingClientRect();

    // Convert signer data to annotation object
    for (const sign of this.signer) {
      switch (sign.signTypeCode) {
        case SignerType.TTD:
          this.createSignObj(sign);
          break;
        case SignerType.PRF:
          this.createPrfObj(sign);
          break;
        default:
          this.createMeteraiObj(sign);
          break;
      }
    }

    console.log('SignerBox', this.boxs);
  }

  private createSignObj(param: Signer) {
    const annotation  = new BoxAnnotation();
    annotation.top    = 24;
    annotation.left   = 24;
    annotation.width  = 130;
    annotation.height = 65;
    annotation.page   = param.signPage;
    annotation.type   = SignerType.TTD;
    annotation.label  = this.getSignerDesc(param.signerTypeCode);
    annotation.signerType = param.signerTypeCode;
    annotation.transform = param.transform;
    annotation.id     = this.mId++;
    this.pushData(annotation);
  }

  private createPrfObj(param: Signer) {
    const annotation  = new BoxAnnotation();
    annotation.top    = 24;
    annotation.left   = 24;
    annotation.width  = 65;
    annotation.height = 65;
    annotation.page   = param.signPage;
    annotation.type   = SignerType.PRF;
    annotation.label  = this.getSignerDesc(param.signerTypeCode);
    annotation.signerType = param.signerTypeCode;
    annotation.transform = param.transform;
    annotation.id     = this.mId++;
    this.pushData(annotation);
  }

  private createMeteraiObj(param: Signer) {
    const annotation  = new BoxAnnotation();
    annotation.top    = 24;
    annotation.left   = 24;
    annotation.width  = 130;
    annotation.height = 130;
    annotation.page   = param.signPage;
    annotation.type   = SignerType.SDT;
    annotation.label  = 'Meterai';
    annotation.transform = param.transform;
    annotation.id     = this.mId++;
    this.pushData(annotation);
  }

  private getSignerType() {
    this.http.post(URLConstant.GetLov, {lovGroup: 'SIGNER_TYPE'})
      .subscribe(response => {
        this.signerTypes = response['lovList'];

        this.ngZone.run(() => {
          this.initSigns();
        });
      });
  }

  private getSignerDesc(code: string) {
    const signerType = this.signerTypes.find(x => x['code'] === code);
    return signerType['description'];
  }

  private getDPI() {
    const div = document.createElement('div');
    div.style.height = '1in';
    div.style.width = '1in';
    div.style.top = '-100%';
    div.style.left = '-100%';
    div.style.position = 'absolute';

    document.body.appendChild(div);

    const result = div.offsetHeight;
    document.body.removeChild(div);

    return result;
  }

  isParafAvailable() {
    if (this.sdtOnly) {
      return false;
    }

    if (this.users) {
      return false;
    }

    return true;
  }

  checkUsers() {
    return !!this.users;
  }

}
