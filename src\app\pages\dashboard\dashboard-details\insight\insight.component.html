
<div *ngIf="basicInfo">
  <div  class="header">
    <ngb-alert *ngIf="alertShow"  type="danger" class="mb-2" [dismissible]="false">
      <div class="row">
        <div class="col-lg-12 col-sm-6 d-flex align-items-center"> 
            <div>There is data changes that has not been consolidated yet. <i class="ft-info"></i></div>
        </div> 
      </div>
    </ngb-alert>
    <div class="row" style="margin: 15px 0px 5px 0px">
      <div class="col-8">
        <div *ngIf="isEdited == 0" class="content-header" style="margin-top: 0 !important;">
          {{dashboardName}}  
          <span *ngIf="basicInfo.isEditable == 1" >
            <a class="text-info ml-2" (click)="onClickEdit()" title="Edit Dashboard Name"> 
              <i style="font-size: 19px;" class="ft-edit-2 primary"></i></a>
          </span>
        </div> 
        <div *ngIf="isEdited == 1" class="content-header-input">
          <div class="row">
            <div class="col-8">
              <input class="form-control form-control-lg" placeholder="Enter Dashboard Name" [(ngModel)]="dashboardName">  
            </div> 
            <div class="col-4 p-0">
              <span >
                <a class="msx-action" [ngClass]="'btn btn-primary'" (click)="onSaveClick()"> {{'Save' | translate}}</a>
              </span>
            </div>
          </div> 
        </div>
      </div>
      <div class="col-4 text-right"> 
          <a class="msx-action mr-2" (click)="onClickGetFile('EXCEL')">
            <img [src]="excelSrc" class="icon-excel" alt="Excel Icon Image" title="Export To Excel">
          </a>
          <a class="msx-action" (click)="onClickGetFile('PDF')">
            <img [src]="pdfSrc" class="icon-pdf" alt="Pdf Icon Image" title="Export To Pdf">
          </a>
      </div>
    </div>
    <div class="row py-2 px-3">
      <div class="col-4">
        <div class="label-info">
          <label class="font-weight-bold pr-1">Created By : </label>
          <label class="value-info">{{basicInfo.createdBy}}</label>
        </div> 
      </div>
      <div class="col-3">
        <div class="label-info">
          <label class="font-weight-bold pr-1">Last Updated Date : </label>
          <label class="value-info">{{basicInfo.lastUpdate}}</label>
        </div> 
      </div>
      <div class="col-3">
        <div class="label-info">
          <label class="font-weight-bold pr-1">Last Consolidate Date : </label>
          <label class="value-info">{{basicInfo.lastConsolidate}}</label>
        </div> 
      </div>
      <div class="col-2 text-right"> 
        <a class="text-info" (click)="clickInfo()" title="Dashboard Information">
          <i style="font-size: 22px;" class="ft-info primary"></i></a>
      </div>
    </div>
  </div>
  
  <ng-container *ngIf="basicInfo.lastConsolidate != '-'">
    <div class="col tab-header-content">
      <div class="content-container">
        <mat-tab-group mat-align-tabs="start" [disablePagination]="false"  [disableRipple]="true" [selectedIndex]="selectedTabIndex" (selectedTabChange)="onTabChange($event)">
          <mat-tab class="tab-nav" label="{{'General Dashboard' | translate}}" translate>
             <app-general-dashboard-insight [stateDashboard]="stateDashboard"></app-general-dashboard-insight>  
          </mat-tab> 
          <mat-tab class="tab-nav" label="{{'Supplier & Buyer Group' | translate}}" translate>        
             <app-supplier-buyer-group-insight [stateDashboard]="stateDashboard"></app-supplier-buyer-group-insight>
          </mat-tab> 
        </mat-tab-group>
      </div>
  </div> 
  </ng-container>
</div>
