<app-msx-paging [container]="view" [serviceUrl]="serviceUrl" [buttonList]="buttonList"
    (onItemClickListener)="onItemClickListener($event)"></app-msx-paging>

<!-- Modal untuk HITL Action (Submit/Reject) -->
<ng-template #hitlActionModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title">
            <i [class]="modalConfig.icon"></i>
            {{ modalConfig.title | translate }}
        </h4>
    </div>

    <div class="modal-body">
        <div class="alert" [ngClass]="modalConfig.alertClass">
            <strong>{{ modalConfig.message | translate }}</strong>
        </div>

        <!-- File Information -->
        <div class="card mb-3" *ngIf="currentData">
            <div class="card-header">
                <h6 class="mb-0">{{ 'File Information' | translate }}</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <small class="text-muted">{{ 'Tenant' | translate }}:</small>
                        <div class="font-weight-bold">{{ currentData?.tenantName }}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">{{ 'Dashboard' | translate }}:</small>
                        <div class="font-weight-bold">{{ currentData?.dashboardGroupName }}</div>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-6">
                        <small class="text-muted">{{ 'File Name' | translate }}:</small>
                        <div class="font-weight-bold">{{ currentData?.filename }}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">{{ 'Upload Date' | translate }}:</small>
                        <div class="font-weight-bold">{{ currentData?.uploadDate }}</div>
                    </div>
                </div>
                <div class="row mt-2" *ngIf="currentData?.redPercentage">
                    <div class="col-6">
                        <small class="text-muted">{{ 'Low Confidence' | translate }}:</small>
                        <div class="font-weight-bold" [ngClass]="{'text-danger': currentData?.redPercentageWarning === '1'}">
                            {{ currentData?.redPercentage }}
                        </div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">{{ 'Current Status' | translate }}:</small>
                        <div class="font-weight-bold">{{ currentData?.status }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notes Section -->
        <div class="form-group">
            <label for="modalNotes" class="form-label">
                {{ modalConfig.notesLabel | translate }}
                <span *ngIf="modalConfig.action === 'reject'" class="text-danger">*</span>
            </label>
            <textarea
                id="modalNotes"
                class="form-control"
                [(ngModel)]="modalNotes"
                [placeholder]="modalConfig.notesPlaceholder | translate"
                rows="4"
                [required]="modalConfig.action === 'reject'">
            </textarea>
            <small class="form-text text-muted">
                {{ modalConfig.notesHint | translate }}
            </small>
        </div>

        <!-- Validation Error -->
        <div class="alert alert-danger" *ngIf="showValidationError">
            <i class="ft-alert-triangle"></i>
            {{ 'Please provide a reason for rejection' | translate }}
        </div>
    </div>

    <div class="modal-footer">
        <!-- Back Button -->
        <button type="button"
                class="btn btn-secondary mr-2"
                (click)="closeModal()">
            <i class="ft-arrow-left"></i>
            {{ 'Back' | translate }}
        </button>

        <!-- Submit Button -->
        <button type="button"
                [class]="modalConfig.submitButtonClass"
                (click)="confirmAction()"
                [disabled]="modalConfig.action === 'reject' && !modalNotes?.trim()">
            <i [class]="modalConfig.submitIcon"></i>
            {{ modalConfig.submitText | translate }}
        </button>
    </div>
</ng-template>