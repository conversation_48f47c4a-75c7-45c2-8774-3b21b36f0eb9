import { Component, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { DashboardService } from 'app/services/api/dashboard.service';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { AddTenantRequest } from 'app/shared/dto/tenant/add-tenant.request';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-add-tenant',
  templateUrl: './add-tenant.component.html',
  styleUrls: ['./add-tenant.component.scss']
})
export class AddTenantComponent implements OnInit {

  addForm: FormModel<any>;
  xForm: FormGroup;

  constructor(private dashboardService: DashboardService, private router: Router, private toastrService: ToastrService,) { }

  ngOnInit(): void {
    this.addForm = {
      name: 'addTenant',
      colSize: 12,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        {
          key: 'tenantCode',
          label: 'Tenant Code',
          controlType: FormConstant.TYPE_TEXT,
          placeholder: 'Type tenant code here',
          required: true
        },
        {
          key: 'tenantName',
          label: 'Tenant Name',
          controlType: FormConstant.TYPE_TEXT,
          placeholder: 'Type tenant name here',
          required: true
        },
        {
          key: 'apiKey',
          label: 'API Key',
          controlType: FormConstant.TYPE_TEXT,
          placeholder: 'Type API key here',
          required: true
        },
        {
          key: 'name',
          label: 'Full Name',
          controlType: FormConstant.TYPE_TEXT,
          placeholder: 'Type full name here',
          required: true
        },
        {
          key: 'email',
          label: 'Email',
          controlType: FormConstant.TYPE_TEXT,
          placeholder: 'Type email here',
          required: true
        },
        {
          key: 'password',
          label: 'Password',
          controlType: FormConstant.TYPE_PASSWORD,
          placeholder: 'Type password here',
          required: true
        }
      ],
      params: [
      ]
    }
  }

  onSubmit(data: any) {
    if (!data['tenantCode']) {
      return;
    }
    const request = new AddTenantRequest();
    request.tenantCode = data['tenantCode'];
    request.tenantName = data['tenantName'];
    request.name = data['name'];
    request.apiKey = data['apiKey'];
    request.email = data['email'];
    request.password = data['password'];
    this.dashboardService.addTenant(request).subscribe(
      (response) => {
        if (response.status.code === 0) {
          this.toastrService.success('Add tenant success', null, {
            positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
         }); 
         this.router.navigate([PathConstant.TENANT]);
        }
      }
     )
  }
  
 
  onInput(event: any) {
    console.log('On input', event);
  }

  onForm(form: FormGroup) {
    this.xForm = form;
  }

}
