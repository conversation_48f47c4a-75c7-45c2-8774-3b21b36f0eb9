import { ChangeDetectorRef, Component, ElementRef, HostListener, Input, OnInit, ViewChild } from '@angular/core';
import * as chartsData from './margin-ratio-charts.charts';
import { InsightsService } from 'app/services/api/insights.service';
import { Dashboard } from 'app/model/dashboard';
import { BaseDashboardRequest } from 'app/model/api/base.dashboard.request';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { ChartsService } from 'app/services/api/charts.service';
import { oneLineChartColors } from 'app/shared/charts/charts-colors';

@Component({
  selector: 'app-margin-ratio-charts',
  templateUrl: './margin-ratio-charts.component.html',
  styleUrls: ['./margin-ratio-charts.component.scss']
})
export class MarginRatioChartsComponent implements OnInit {
  @Input() stateDashboard: Dashboard;
  
  barChartType = 'bar';
  barChartLegend = true;
  lineChartLegend = true;
  lineChartType = 'line';

  // Charts label
  labels: string[];
  oneBarOneLineChartColors : any[];

  // Gross profit margin data
  gpmData: any[];
  gpmOptions = chartsData.gpmOptions; 
  gpmGlossaryVisible = false;

  // Working capital ratio data
  wcrData: any[];
  wcrOptions = chartsData.wcrOptions; 
  wcrGlossaryVisible = false;

  // Liquidity ration data
  lrData: any[];
  lrOptions = chartsData.lrOptions;
  lrColors = oneLineChartColors;
  lrGlossaryVisible = false;
 
  @ViewChild('glossaryCardGPM') glossaryCardGPM!: ElementRef;
  @ViewChild('glossaryCardWCR') glossaryCardWCR!: ElementRef;
  @ViewChild('glossaryCardLR') glossaryCardLR!: ElementRef;
  
  constructor(
    private insightsService: InsightsService,
    private chartsService: ChartsService,
    private cdr: ChangeDetectorRef,
    private eRef: ElementRef
  ) { 
    this.oneBarOneLineChartColors = this.chartsService.getOneBarOneLineChartColors({
      0: {borderDash: [5, 5] }  
    }); 
  }

  ngOnInit(): void {
    const request = new BaseDashboardRequest();
    request.dashboardName = this.stateDashboard.dashboardName;
    
    this.insightsService.getSupplierBuyerGpmWcrLr(request).subscribe(response => {
      if (response.status.code !== 0) {
        return;
      }

      this.labels = response.labels;
      this.gpmData = response.grossProfitMargin;
      this.wcrData = response.workingCapitalRatio;
      this.lrData = response.liquidityRatio;
      this.cdr.detectChanges();
    });
  }

  chartClicked(e: any): void {
    //your code here
  }

  chartHovered(e: any): void {
    //your code here
  }

  // Open glossary
  openGlossary(term: string, event: MouseEvent) {
    event.stopPropagation();
    if(CommonConstant.GROSS_PROFIT_MARGIN == term){
      this.gpmGlossaryVisible = !this.gpmGlossaryVisible;
    } else if(CommonConstant.WORKING_CAPITAL_RATIO == term){
      this.wcrGlossaryVisible = !this.wcrGlossaryVisible;
    } else if(CommonConstant.LIQUIDITY_RATIO == term){
      this.lrGlossaryVisible = !this.lrGlossaryVisible;
    } 
  } 

  @HostListener('document:click', ['$event'])
  onClickOutside(event: MouseEvent) {
    const clickedTarget = event.target as HTMLElement;

    if (this.gpmGlossaryVisible && 
        !this.glossaryCardGPM?.nativeElement.contains(clickedTarget)) {
      this.gpmGlossaryVisible = false;
    }
    if (this.wcrGlossaryVisible && 
        !this.glossaryCardWCR?.nativeElement.contains(clickedTarget)) {
      this.wcrGlossaryVisible = false;
    }
    if (this.lrGlossaryVisible && 
        !this.glossaryCardLR?.nativeElement.contains(clickedTarget)) {
      this.lrGlossaryVisible = false;
    }
  }
}
