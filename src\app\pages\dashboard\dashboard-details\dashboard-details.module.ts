import { NgModule } from '@angular/core';
import { CommonModule, registerLocaleData } from '@angular/common'; 
import { DashboardDetailsRoutingModule } from './dashboard-details-routing.module';
import { DashboardDetailsComponent } from './dashboard-details.component';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbDatepickerModule, NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { MsFormModule } from 'app/shared/components/ms-form/ms-form.module';
import { SharedModule } from 'app/shared/shared.module';
import { NgxPhotoEditorModule } from 'ngx-photo-editor';
import { NgxSpinnerModule } from 'ngx-spinner';
import { ToastrModule } from 'ngx-toastr';
import { HttpClientModule } from '@angular/common/http';
import {MatTabsModule} from '@angular/material/tabs';
import { SupplierBuyerGroupComponent } from './supplier-buyer-group/supplier-buyer-group.component';
import { NonBusinessTransactionGroupComponent } from './non-business-transaction-group/non-business-transaction-group.component';
import { AddNonBusinessTransactionGroupComponent } from './non-business-transaction-group/add-non-business-transaction-group/add-non-business-transaction-group.component';
import { DetailNonBusinessTransactionGroupComponent } from './non-business-transaction-group/detail-non-business-transaction-group/detail-non-business-transaction-group.component';
import { AddTransactionGroupComponent } from './add-transaction-group/add-transaction-group.component';
import { OcrResultComponent } from './ocr-result/ocr-result.component';
import { AddSupplierBuyerGroupComponent } from './supplier-buyer-group/add-supplier-buyer-group/add-supplier-buyer-group.component';
import { SupplierBuyerSubGroupComponent } from './supplier-buyer-group/supplier-buyer-sub-group/supplier-buyer-sub-group.component';
import { AddSupplierBuyerSubGroupComponent } from './supplier-buyer-group/supplier-buyer-sub-group/add-supplier-buyer-sub-group/add-supplier-buyer-sub-group.component';
import { ConfigureSubGroupComponent } from './supplier-buyer-group/configure-sub-group/configure-sub-group.component';
import { EditSubGroupTransactionComponent } from './supplier-buyer-group/supplier-buyer-sub-group/edit-sub-group-transaction/edit-sub-group-transaction.component';
import { AnomalyComponent } from './anomaly/anomaly.component';
import { DetailOcrResultComponent } from './ocr-result/detail-ocr-result/detail-ocr-result.component';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { PersonalInfoOcrResultComponent } from './ocr-result/detail-ocr-result/personal-info-ocr-result/personal-info-ocr-result.component';
import { TransactionSummaryOcrResultComponent } from './ocr-result/detail-ocr-result/transaction-summary-ocr-result/transaction-summary-ocr-result.component';
import { NgxMaskModule } from 'ngx-mask';
import { TransactionHistoryOcrResultComponent } from './ocr-result/detail-ocr-result/transaction-history-ocr-result/transaction-history-ocr-result.component';
import { InsightComponent } from './insight/insight.component';
import { GeneralDashboardInsightComponent } from './insight/general-dashboard-insight/general-dashboard-insight.component';
import { SupplierBuyerGroupInsightComponent } from './insight/supplier-buyer-group-insight/supplier-buyer-group-insight.component';
import { ChartsModule } from 'ng2-charts';
import { ConsolidatedBankStatementComponent } from './insight/consolidated-bank-statement/consolidated-bank-statement.component';
import { DiversityOfSupplierChartsComponent } from './insight/supplier-buyer-group-insight/diversity-of-supplier-charts/diversity-of-supplier-charts.component';
import { MarginRatioChartsComponent } from './insight/supplier-buyer-group-insight/margin-ratio-charts/margin-ratio-charts.component';
import { CircularComponent } from './circular/circular.component';
import { AddCircularComponent } from './circular/add-circular/add-circular.component';
import { ViewTransactionCircularComponent } from './circular/view-transaction-circular/view-transaction-circular.component';
import { GeneralEndingBalanceDailyCashFlowAnalysisComponent } from './insight/general-dashboard-insight/general-ending-balance-daily-cash-flow-analysis/general-ending-balance-daily-cash-flow-analysis.component';
import { BusinessTransactionGroupComponent } from './business-transaction-group/business-transaction-group.component';
import { AddBusinessTransactionGroupComponent } from './business-transaction-group/add-business-transaction-group/add-business-transaction-group.component';
import { DetailBusinessTransactionGroupComponent } from './business-transaction-group/detail-business-transaction-group/detail-business-transaction-group.component';
import { UiSwitchModule } from "ngx-ui-switch";
import { GeneralCircularTransactionAnalysisComponent } from './insight/general-dashboard-insight/general-circular-transaction-analysis/general-circular-transaction-analysis.component';
import localeId from '@angular/common/locales/id';
import { AnomalyDetailComponent } from './anomaly/anomaly-detail/anomaly-detail.component';
import { AnomalyDetailTransactionComponent } from './anomaly/anomaly-detail/anomaly-detail-transaction/anomaly-detail-transaction.component';
import { TransactionComponent } from './transaction/transaction.component';
import { SettingsComponent } from './settings/settings.component';
import { AuditLogComponent } from './audit-log/audit-log.component';


registerLocaleData(localeId); // Register Indonesian format (if needed)

@NgModule({
  declarations: [
    DashboardDetailsComponent, 
    SupplierBuyerGroupComponent, 
    NonBusinessTransactionGroupComponent, 
    AddNonBusinessTransactionGroupComponent, 
    DetailNonBusinessTransactionGroupComponent, 
    AddTransactionGroupComponent, 
    OcrResultComponent, 
    AddSupplierBuyerGroupComponent, 
    SupplierBuyerSubGroupComponent, 
    AddSupplierBuyerSubGroupComponent, 
    ConfigureSubGroupComponent, 
    EditSubGroupTransactionComponent, 
    AnomalyComponent,
    DetailOcrResultComponent,
    PersonalInfoOcrResultComponent,
    TransactionSummaryOcrResultComponent,
    TransactionHistoryOcrResultComponent,
    InsightComponent,
    GeneralDashboardInsightComponent,
    SupplierBuyerGroupInsightComponent,
    ConsolidatedBankStatementComponent,
    DiversityOfSupplierChartsComponent,
    MarginRatioChartsComponent,
    CircularComponent,
    AddCircularComponent,
    ViewTransactionCircularComponent,
    GeneralEndingBalanceDailyCashFlowAnalysisComponent,
    BusinessTransactionGroupComponent,
    AddBusinessTransactionGroupComponent,
    DetailBusinessTransactionGroupComponent,
    GeneralCircularTransactionAnalysisComponent,
    AnomalyDetailComponent,
    AnomalyDetailTransactionComponent,
    TransactionComponent,
    SettingsComponent,
    AuditLogComponent
  ],
  imports: [
    SharedModule,
    HttpClientModule,
    CommonModule,
    DashboardDetailsRoutingModule,
    MatTabsModule,
    ToastrModule.forRoot(),
    NgbModule,
    NgxSpinnerModule,
    FormsModule,
    ReactiveFormsModule,
    NgSelectModule,
    NgbModule,
    NgxDatatableModule,
    NgxPhotoEditorModule,
    DragDropModule,
    SharedModule,
    MsFormModule,
    NgxMaskModule.forRoot(),
    PdfViewerModule, 
    NgbDatepickerModule,
    ChartsModule,
    UiSwitchModule
  ]
})
export class DashboardDetailsModule { }
