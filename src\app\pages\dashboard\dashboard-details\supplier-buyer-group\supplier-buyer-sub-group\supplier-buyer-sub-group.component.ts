import { ChangeDetector<PERSON><PERSON>, Component, <PERSON>Zone, OnInit, ViewChild } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DashboardService } from 'app/services/api/dashboard.service';
import { HttpService } from 'app/services/http.service';
import { GlobalService } from 'app/shared/data/global.service';
import { ToastrService } from 'ngx-toastr';
import * as swalFunction from 'app/shared/data/sweet-alerts';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { Button } from 'app/shared/components/msx-view/models/Button';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { SupplierBuyerSubGroupListView, SupplierBuyerSubGroupSearchFilter } from './supplier-buyer-sub-group.list.view';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { MsxPagingComponent } from 'app/shared/components/msx-view/msx-paging.component';
import { AddSupplierBuyerSubGroupComponent } from './add-supplier-buyer-sub-group/add-supplier-buyer-sub-group.component';
import { CommonConstant } from 'app/shared/constant/common.constant';

@Component({
  selector: 'app-supplier-buyer-sub-group',
  templateUrl: './supplier-buyer-sub-group.component.html',
  styleUrls: ['./supplier-buyer-sub-group.component.scss']
})
export class SupplierBuyerSubGroupComponent implements OnInit {
  state: any;
  dashboardName: string;
  swal = swalFunction;

  view: MsxView;
  buttonList: Button[];
  serviceUrl = URLConstant.GetListMasterSubGroup; 
  
  @ViewChild(MsxPagingComponent) msxPagingComponent!: MsxPagingComponent;
  
  constructor(
    private global: GlobalService,
    private router: Router,
    public http: HttpService,
    private ngZone: NgZone,
    private cdr: ChangeDetectorRef,
    private modalService: NgbModal, 
    private toastrService: ToastrService,
    private dashboardService: DashboardService,
    private readonly formBuilder: FormBuilder,) { 
      this.state = this.router.getCurrentNavigation().extras?.state; 
    }

    ngOnInit(): void {
    if(!this.state){
        this.router.navigate([PathConstant.DASHBOARD]);
      }
    this.dashboardName = this.state.dashboardName;
      SupplierBuyerSubGroupSearchFilter.params.push(
        {
          key: 'tenantCode',
          controlType: 'hidden', 
          value: this.global.user.role.tenantCode 
        },
        {
          key: 'dashboardName',
          controlType: 'hidden', 
          value: this.dashboardName 
        }
      );
      SupplierBuyerSubGroupListView.titleDynamic = this.dashboardName;
      this.view = SupplierBuyerSubGroupListView;
      
      this.buttonList = [
        {name: 'Back', class: 'btn btn-warning', hide: false, icon: 'ft-arrow-left-circle'},
        {name: 'Add New Sub Group', class: 'btn btn-primary', hide: false, icon: 'ft-plus'}
      ];
    }

    onBtnClickListener($event) {
      const buttonName = $event['name'];
      if (buttonName == 'Add New Sub Group') {
        this.openAddModal();
      } else{
        this.goBack();
      }
    }

    goBack() { 
      this.router.navigate([PathConstant.DASHBOARD_DETAIL], { 
        state: { 
          state: this.state, 
          indexTab: CommonConstant.SUPPLIER_BUYER_TAB_INDEX
        }
      });
    }

    openAddModal() {
      const modal = this.modalService.open(AddSupplierBuyerSubGroupComponent, {
        centered: true,
        backdrop: 'static',
        size: 'm'
      });
      modal.componentInstance.tenantCode = this.global.user.role.tenantCode;
      modal.componentInstance.dashboardName = this.dashboardName;
  
      modal.componentInstance.result.subscribe(() => {
        this.msxPagingComponent.refreshSearch(); // Refresh data 
      });
    }
  
    onItemClickListener(event) {
      const data = event['data'];
      switch (event['act']['type']) {
        case Act.Edit:
         return this.openEdit(data);
        case Act.Delete:
         return this.deleteData(data);
      }
    } 

    openEdit(data:any){
      const subGroupName = {
        subGroupName:data.subGroupName
      }
      this.router.navigate([PathConstant.EDIT_SUPPLIER_BUYER_SUB_GROUP_TRANSACTION], {state: {...subGroupName,...this.state}});
    }

    deleteData(data: any) {
      this.swal.Confirm('Are you sure? This sub group will be permanently lost.').then(
        async (result) => {
          if (result.isConfirmed) {
            const request = { 
              subGroupName: data.subGroupName,
              tenantCode: this.global.user.role.tenantCode,
              dashboardName: this.dashboardName
            };
            await this.dashboardService.deleteSupplierBuyerSubGroup(request).toPromise().then(
              async (response) => {
                 if (response['status']['code'] == 0) {
                  this.toastrService.success('Data successfully deleted!', null, {
                    positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
                  });
                   this.msxPagingComponent.refreshSearch(); // Refresh data 
                  // Trigger change detection to update UI
                  this.cdr.detectChanges();
                 }
              }
            )
          }
        }
      );
    }
}
