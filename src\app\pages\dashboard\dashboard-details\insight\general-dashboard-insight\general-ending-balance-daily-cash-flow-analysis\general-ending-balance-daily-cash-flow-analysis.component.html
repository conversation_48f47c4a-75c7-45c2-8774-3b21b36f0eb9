<div class="row">
  <div class="col-12 col-md-5 pr-0" *ngIf="endingBalanceChartData">
    <div class="card card-chart mb-0"> 
      <div class="card-body">
          <div class="card-block">
              <div class="height-400">
                      <canvas baseChart class="chart" [datasets]="endingBalanceChartData" [labels]="DailyChartLabels" [options]="endingBalanceChartOptions" [colors]="endingBalanceChartColors"
                      [legend]="true" [chartType]="endingBalanceChartType" (chartHover)="chartHovered($event)" (chartClick)="chartClicked($event)" ></canvas>
              </div>
          </div>
      </div>
  </div>
  </div>
  
  <!-- Insight General Program -->
  <div class="col-12 col-md-2">
    <div class="card card-chart mb-2">
        <div class="card-chart-body">
            <div class="text-center">
              <p class="mb-2">Highest Balance</p>
              <h6 class="card-general-body-value">Rp. {{insightCard.highestBalance | number: '1.2-2'}}</h6>
              <h6 class="card-general-body-value mb-0">({{insightCard.highestBalanceDate}})</h6>
            </div>
        </div>
    </div>  

    <div class="card card-chart my-2">
        <div class="card-chart-body">
            <div class="text-center">
              <p class="mb-2">Lowest Balance</p>
              <h6 class="card-general-body-value">Rp. {{insightCard.lowestBalance | number: '1.2-2'}}</h6>
              <h6 class="card-general-body-value mb-0">({{insightCard.lowestBalanceDate}})</h6>
            </div>
        </div>
    </div> 

    <div class="card card-chart my-2">
        <div class="card-chart-body">
            <div class="text-center">
              <p class="mb-2">Monthly Average Balance</p>
              <h6 class="card-general-body-value mb-0">Rp. {{insightCard.monthlyAverageBalance | number: '1.2-2'}}</h6>
            </div>
        </div>
    </div> 

    <div class="card card-chart my-2">
        <div class="card-chart-body">
            <div class="text-center">
              <p class="mb-2">Daily Average Balance</p>
              <h6 class="card-general-body-value mb-0">Rp. {{insightCard.dailyAverageBalance | number: '1.2-2'}}</h6>
            </div>
        </div>
    </div> 

    <div class="card card-chart mt-2">
        <div class="card-chart-body">
            <div class="text-center">
              <p class="mb-2">Growth Rate</p>
              <h6 class="card-general-body-value mb-0">{{insightCard.growthRate | number: '1.2-2'}} %</h6>
            </div>
        </div>
    </div> 

  </div>
  <!-- Insight General Program -->

  <div class="col-12 col-md-5 pl-0" *ngIf="cashFlowChartData">
    <div class="card card-chart mb-0">
      <div class="card-body">
          <div class="card-block">
              <div id="bar-bar1" class="height-400">
                  <canvas  baseChart class="chart" [datasets]="cashFlowChartData" [labels]="DailyChartLabels" [options]="cashflowChartOptions" 
                  [colors]="cashFlowChartColors" [legend]="true" [chartType]="cashFlowChartType" (chartHover)="chartHovered($event)" (chartClick)="chartClicked($event)"></canvas>
              </div>
          </div>
      </div>
    </div>
  </div>
</div>