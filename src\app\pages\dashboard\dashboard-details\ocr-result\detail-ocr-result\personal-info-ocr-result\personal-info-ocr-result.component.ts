import { ChangeDetector<PERSON>ef, Component, EventEmitter, Input, NgZone, OnInit, Output } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DashboardService } from 'app/services/api/dashboard.service';
import { HttpService } from 'app/services/http.service';
import { FormModel } from 'app/shared/components/ms-form/models';
import { GlobalService } from 'app/shared/data/global.service';
import * as swalFunction from 'app/shared/data/sweet-alerts'; 
import { QuestionTextbox } from 'app/shared/components/ms-form/questions';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { BankStatementHeaderTransactionRequest } from 'app/model/api/OcrResult/bank-statement-header-transaction';
import { QuestionTextarea } from 'app/shared/components/ms-form/questions/question-textarea';
import { MsxFormControlService } from 'app/shared/components/ms-form/msx-form-control.service';
import {  FormGroup } from '@angular/forms';


@Component({
  selector: 'app-personal-info-ocr-result',
  templateUrl: './personal-info-ocr-result.component.html',
  styleUrls: ['./personal-info-ocr-result.component.scss']
})
export class PersonalInfoOcrResultComponent implements OnInit {
  @Input() state: any;
  @Output() onClickInput = new EventEmitter<any>();

  formObj: FormModel<any>;
  msxForm: FormGroup; 
  dashboardName: string;
  bankStatement: any;
  swal = swalFunction;

  constructor(
    private global: GlobalService,
    public http: HttpService,
    private fcs: MsxFormControlService,
    private dashboardService: DashboardService,) {  
    }

  async ngOnInit() {   
    this.setupQuestion(); 
    const controls = [...this.formObj.params, ...this.formObj.components];
    this.msxForm = this.fcs.toFormGroup(controls); 

    this.getBankStatementHeaderTransaction();
  }

  setupQuestion() { 
    this.formObj = {
      name: 'groupNameForm',
      mode: CommonConstant.MODE_EDIT,
      colSize: 6,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        new QuestionTextbox({
            key: 'accountName',
            label: 'Name',
            placeholder: 'Type name here',
            maxLength: 64,
            required: true, 
            validations: [
              {type: 'required', message: 'Name cannot be empty'},
              {type: 'maxlength', message: 'The maximum number of characters for Name is 64'}
            ]
        }),
        new QuestionTextbox({
          key: 'accountNo',
            label: 'Account No',
            placeholder: 'Type group name here',
            maxLength: 64,
            required: true, 
            validations: [
              {type: 'required', message: 'Account No cannot be empty'},
              {type: 'maxlength', message: 'The maximum number of characters for Account No is 64'}
            ]
        }),
        new QuestionTextbox({
          key: 'bankOffice',
            label: 'Bank Office',
            placeholder: 'Type bank office here',
            maxLength: 64,
            validations: [
              {type: 'maxlength', message: 'The maximum number of characters for Bank Office is 64'}
            ]
        }),
        new QuestionTextbox({
          key: 'bankOffice',
            label: 'Bank Office',
            placeholder: 'Type bank office here',
            maxLength: 64,
            required: true, 
            validations: [
              {type: 'required', message: 'Bank Office cannot be empty'},
              {type: 'maxlength', message: 'The maximum number of characters for Bank Office is 64'}
            ]
        }),
        new QuestionTextbox({
          key: 'currency',
            label: 'Currency',
            placeholder: 'Type currency here',
            maxLength: 64,
            required: true, 
            validations: [
              {type: 'required', message: 'Currency cannot be empty'},
              {type: 'maxlength', message: 'The maximum number of characters for Currency is 64'}
            ]
        }),
        new QuestionTextarea({
          key: 'address',
            label: 'Address',
            placeholder: 'Type address here',
            maxLength: 128,
            required: true, 
            validations: [
              {type: 'required', message: 'Address cannot be empty'},
              {type: 'maxlength', message: 'The maximum number of characters for Address is 128'}
            ]
        }),  
      ],
      params: []
    }
  }
  
  getQuestionkey(key:string) {
    return this.formObj.components.find(x => x.key === key);
  }

  async getBankStatementHeaderTransaction(){
    const request = new BankStatementHeaderTransactionRequest();
    request.tenantCode = this.global.user.role.tenantCode;
    request.fileSourcePath = this.state.fileSourcePath;
    request.dashboardName = this.state.dashboardName;
   
    await this.dashboardService.getBankStatementHeaderTransaction(request).toPromise().then(
      async (response) => {
         if (response['status']['code'] == 0) { 
          this.bankStatement = response;
          this.pathValueForm(this.bankStatement);
         }
      }
    )
  } 

  pathValueForm(res){
    this.msxForm.patchValue({
      accountName: res.accountName,
      accountNo: res.accountNo,
      bankOffice: res.bankOffice,
      currency: res.currency,
      address: res.address
    });
  }

  onClick(label:string){
    const info = { 
      boxLocation : this.bankStatement[label][0].boxLocation,
      boxPage : this.bankStatement[label][0].boxPage,
    }
    this.onClickInput.emit(info); 
  }

}
