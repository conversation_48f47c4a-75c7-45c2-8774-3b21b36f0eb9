import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap'; 
import { DashboardService } from 'app/services/api/dashboard.service';
import { HttpService } from 'app/services/http.service';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { MsxFormControlService } from 'app/shared/components/ms-form/msx-form-control.service';
import { QuestionTextbox } from 'app/shared/components/ms-form/questions'; 
import { CommonConstant } from 'app/shared/constant/common.constant'; 
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-add-business-transaction-group',
  templateUrl: './add-business-transaction-group.component.html',
  styleUrls: ['./add-business-transaction-group.component.scss']
})
export class AddBusinessTransactionGroupComponent implements OnInit {
  formObj: FormModel<any>;
  msxForm: FormGroup;

  @Input() dashboardName: string;
  @Input() tenantCode: string;
  @Output() transactionGroupAdded = new EventEmitter<void>();

  constructor(
    private fcs: MsxFormControlService,
    public activeModal: NgbActiveModal, 
    public http: HttpService,
    private dashboardService: DashboardService,
    private toastrService: ToastrService,) { }

  ngOnInit(): void {
    this.setupQuestion();
    const controls = [...this.formObj.params, ...this.formObj.components];
    this.msxForm = this.fcs.toFormGroup(controls); 
  }
  setupQuestion() { 
    this.formObj = {
      name: 'groupNameForm',
      mode: CommonConstant.MODE_ADD,
      colSize: 12,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        new QuestionTextbox({
          key: 'groupName',
            label: 'New Group Name',
            placeholder: 'Type new group name here',
            maxLength: 64,
            required: true,
            validations: [
              {type: 'required', message: 'Group Name cannot be empty'},
              {type: 'maxlength', message: 'The maximum number of characters for the Group Name Name is 64'}
            ]
        }), 
      ],
      params: []
    }
  }

  getQuestionkey(key:string) {
    return this.formObj.components.find(x => x.key === key);
  }

  closeModal() { 
    this.activeModal.dismiss("cancel");
  }

  async onSubmit(){ 
    // Check if the form is valid
    this.msxForm.markAllAsTouched();
    if (this.msxForm.invalid) {
      return;  
    }

    //save data
    const data = this.msxForm.value;
    const request = {
      ...data,
      tenantCode: this.tenantCode,
      dashboardName: this.dashboardName
    };
    await this.dashboardService.addBusinessTransactionGroup(request).toPromise().then(response => {
      if (response["status"]["code"] == 0) {
        this.toastrService.success('Group successfully added!', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        this.transactionGroupAdded.emit();
        this.closeModal();
      } 
    });
  }

}
