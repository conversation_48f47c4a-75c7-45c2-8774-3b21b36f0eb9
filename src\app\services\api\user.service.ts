import { Injectable } from '@angular/core';
import {UserProfileResponse} from '../../model/api/user.profile.response';
import {UserProfileRequest} from '../../model/api/user.profile.request';
import {AppService} from './app.service';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { CheckUserForgotPasswordRequest } from 'app/model/api/User/check-user-forgot-password.request';
import { BaseResponse } from 'app/model/api/base.response';
import { VerifyOtpForgotPasswordRequest } from 'app/model/api/User/verify-otp-forgot-password.request';
import { ResetPasswordRequest } from 'app/model/api/User/reset-password.request';


@Injectable({
  providedIn: 'root'
})
export class UserService extends AppService {

  profile(username: string) {
    const request = new UserProfileRequest(username);
    request.audit = {callerId: username};
    return this.client.post<UserProfileResponse>(URLConstant.GetProfile, request);
  }

  checkUserForgotPassword(loginId: string) {
    const request = new CheckUserForgotPasswordRequest();
    request.loginId = loginId;
    request.audit = {callerId: loginId, locale: this.defaultLocale};

    return this.client.post<BaseResponse>(URLConstant.CheckUserForgotPassword, request);
  }

  sendOtpForgotPassword(loginId: string) {
    const request = new CheckUserForgotPasswordRequest();
    request.loginId = loginId;
    request.audit = {callerId: loginId, locale: this.defaultLocale};

    return this.client.post<BaseResponse>(URLConstant.SendOtpForgotPassword, request);
  }

  verifyOtpForgotPassword(loginId: string, otp: string) {
    const request = new VerifyOtpForgotPasswordRequest();
    request.loginId = loginId;
    request.otp = otp;
    request.audit = {callerId: loginId, locale: this.defaultLocale};

    return this.client.post<BaseResponse>(URLConstant.VerifyOtpForgotPassword, request);
  }

  resetPassword(request: ResetPasswordRequest) {
    return this.client.post<BaseResponse>(URLConstant.ResetPassword, request);
  }

}
