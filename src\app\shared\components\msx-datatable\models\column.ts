import {Action} from './action';
import {Align} from '../enums/align';
import {ColumnType} from '../enums/column-type';

export class Column {
  type?: ColumnType;
  prop?: string;
  align?: Align;
  label: string;
  width: number;
  action?: Action[];
  format?: string;
  class?: string;
  condition?: boolean;
  conditionedClass?: string;
  conditionExpected?: any;
  conditionVariable?: string;
  conditionExpected2?: any;
  conditionVariable2?: string;
  
  // For data table in msx-datatable-transaction-history
  allowNegativeNumber?: boolean = false;

  constructor(options: {
    type?: ColumnType;
    prop?: string;
    align?: Align;
    label?: string;
    action?: Action[];
  } = {}) {
    this.type  = options.type || ColumnType.Text;
    this.prop  = options.prop;
    this.align = options?.align || Align.LEFT;
  }
}
