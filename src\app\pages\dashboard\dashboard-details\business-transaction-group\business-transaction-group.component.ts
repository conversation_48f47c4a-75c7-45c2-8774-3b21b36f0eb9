import { ChangeDetectorRef, Component, EventEmitter, Input, NgZone, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Act } from 'app/shared/components/msx-datatable/enums/act'; 
import { PathConstant } from 'app/shared/constant/PathConstant';
 import { BehaviorSubject } from 'rxjs'; 
import { GetListNonBusinessTransactionGroupRequest } from 'app/model/api/NonBusinessTransactionGroup/get-list-non-business-transaction-group.request';
import { GlobalService } from 'app/shared/data/global.service';
import { Dashboard } from 'app/model/dashboard';
import { Page } from 'app/shared/components/msx-datatable/models/page'; 
import * as swalFunction from 'app/shared/data/sweet-alerts';
import { ToastrService } from 'ngx-toastr';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { DashboardService } from 'app/services/api/dashboard.service';
import { BusinessTransactionGroupTable } from './business-transaction-group.list.view';
import { AddBusinessTransactionGroupComponent } from './add-business-transaction-group/add-business-transaction-group.component';
import { HttpService } from 'app/services/http.service';

@Component({
  selector: 'app-business-transaction-group',
  templateUrl: './business-transaction-group.component.html',
  styleUrls: ['./business-transaction-group.component.scss']
})
export class BusinessTransactionGroupComponent implements OnInit {  
  @Input() stateDashboard: Dashboard;
  @Output() isDataChanges = new EventEmitter<boolean>();

  table = BusinessTransactionGroupTable;
  datasource: BehaviorSubject<any> = new BehaviorSubject<any>([]);
  swal = swalFunction; 

  title: string = CommonConstant.BUSINESS_TRANSACTION_TITLE;

  constructor(
    private global: GlobalService,
    private router: Router, 
    public http: HttpService,
    private ngZone: NgZone, 
    private cdr: ChangeDetectorRef,
    private modalService: NgbModal,
    private dashboardService: DashboardService,
    private toastrService: ToastrService,) { }

    async ngOnInit() { 
      await this.refreshPage();
    }

  async refreshPage(){
    await this.getListBusinessTransactionGroup().then(() => {
      this.ngZone.run(() => {
        this.cdr.markForCheck();
      })
    });
  }

  async getPage(page: Page) {
    console.log('requestPage', page);
    await this.getListBusinessTransactionGroup(page.pageNumber + 1).then(() => {
      this.ngZone.run(() => {
        this.cdr.markForCheck();
      })
    });
  }  
  
  async getListBusinessTransactionGroup(pageNumber: number = 1) {  
    const request = new GetListNonBusinessTransactionGroupRequest();
    request.page = pageNumber;
    request.tenantCode = this.global.user.role.tenantCode;
    request.dashboardName = this.stateDashboard.dashboardName; 

    await this.dashboardService.getListBusinessTransactionGroup(request).toPromise().then(
      (response) => {
        if (response['status']['code'] == 0) {
          this.datasource.next(response);
        }
      }
    )
  } 

  onBtnClick(){
    this.openAddModal();
  }

  onItemClickListener(event) {  
    const data = event['data'];
    const combinedData = Object.assign({}, this.stateDashboard, data);
    switch (event['act']['type']) {
      case Act.Edit:
        return this.gotoDetail(combinedData);
      case Act.Delete:
        return this.deleteData(data);
    }

  }

  gotoDetail(state: Dashboard) {
    this.router.navigate([PathConstant.DETAIL_BUSINESS_TRANSACTION_GROUP], {state: state});
  }

  deleteData(data: any) {
    this.swal.Confirm('Are you sure? This group will be permanently lost.').then(
      async (result) => {
        if (result.isConfirmed) {
          const request = { 
            groupName: data.groupName,
            tenantCode: this.global.user.role.tenantCode,
            dashboardName: this.stateDashboard.dashboardName
          };
          await this.dashboardService.deleteBusinessTransactionGroup(request).toPromise().then(
            async (response) => {
               if (response['status']['code'] == 0) {
                this.toastrService.success('Group successfully deleted!', null, {
                  positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
                });
                await this.getListBusinessTransactionGroup(); // Refresh data 
                this.isDataChanges.emit(true);
                // Trigger change detection to update UI
                this.cdr.detectChanges();
               }
            }
          )
        }
      }
    );
  }

  openAddModal() {
    const modal = this.modalService.open(AddBusinessTransactionGroupComponent, {
      centered: true,
      backdrop: 'static',
      size: 'm'
    });
    modal.componentInstance.tenantCode = this.global.user.role.tenantCode;
    modal.componentInstance.dashboardName = this.stateDashboard.dashboardName;

    modal.componentInstance.transactionGroupAdded.subscribe(() => {
      this.getListBusinessTransactionGroup(); // Refresh data 
      this.isDataChanges.emit(true);
      this.cdr.detectChanges();
    });
  }
 

}
