<div class="card-body pt-0">
  <div class="row">
    <div class="col-12"> 
      <ng-container *ngFor="let widget of container.components" [ngSwitch]="widget.type">
        <app-search-filter *ngSwitchCase="WidgetType.SearchFilter" [formObj]="widget.component" [service]="serviceUrl" [pageNumber]="pageNumber" (result)="getResult($event)" (selectX)="onSelect($event)" ></app-search-filter>
        <div *ngIf="widget.type === WidgetType.SearchFilter">
            <div class="row mb-2">
                <div class="col-12 px-4 text-right">
                    <button *ngIf="!isMetadataAnomaly" class="btn btn-primary mr-2" type="button" (click)="addNewAnomaly()"><i class="ft-plus"></i> {{'Add New Anomaly' | translate}}</button>
                </div>
            </div>
        </div>
        <app-msx-datatable-transaction-history *ngSwitchCase="WidgetType.Datatable" [hideAddButton]="true" [tableObj]="widget.component" [datasource]="datasource" (getPage)="getPage($event)" (itemClickListener)="onItemClick($event)" (selectedRow)="onClickedRow($event)"></app-msx-datatable-transaction-history>
      </ng-container> 
    </div>
  </div>
</div>