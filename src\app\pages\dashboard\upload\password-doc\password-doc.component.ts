import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { HttpService } from 'app/services/http.service'; 
import { FormModel } from 'app/shared/components/ms-form/models';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { ToastrService } from 'ngx-toastr';
import * as PDFJS from "pdfjs-dist";
import { FileItem } from 'ng2-file-upload';

@Component({
  selector: 'app-password-doc',
  templateUrl: './password-doc.component.html',
  styleUrls: ['./password-doc.component.scss']
})
export class PasswordDocComponent implements OnInit {
  @Input() file: FileItem; 
  @Output() passwordUnlock = new EventEmitter<{success: boolean; password: string }>(); 

  formObj: FormModel<any>; 
  isShowPassword:boolean = false;
  passwordDocFormSubmitted:boolean = false;
  result:boolean = false;

  passwordDocForm = new FormGroup({ 
      password: new FormControl('', [Validators.required]), 
  }); 

  constructor(
    public activeModal: NgbActiveModal, 
    public http: HttpService,
    private toastrService: ToastrService,) { }

  ngOnInit(): void {
    // Intentionally empty. Will be filled when necessary.
  } 

  get pdf() {
    return this.passwordDocForm.controls;
  }

  closeModal() { 
    this.activeModal.dismiss("cancel");
  }

  toggleIsShowPassword() {
    this.isShowPassword = !this.isShowPassword;
  }

  async checkPasswordInput(){ 
    // Check if the form is valid 
    this.passwordDocFormSubmitted = true;
    this.passwordDocForm.markAllAsTouched();
    if (this.passwordDocForm.invalid) {
      return;
    }

    const password = this.passwordDocForm.get('password').value;
    this.result = await this.isPasswordCorrect(this.file, password);
    if(this.result){
      this.passwordUnlock.emit({success:this.result, password: password});
      this.closeModal();
    }
    else { 
      this.toastrService.error('Password is Incorrect', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
    } 
  }

  //check if password that is inputed is correct
    async isPasswordCorrect(item: FileItem, password: string) {
      const file = item._file;
      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
   
      try {
        const loadingTask = PDFJS.getDocument({ data: uint8Array, isEvalSupported: false, password: password });
        const pdf = await loadingTask.promise; // Mungkin bisa hapus baris ini
        const pdfData = await pdf.getData(); // Mungkin bisa hapus baris ini
        return true;
      } catch (error) {
        console.log('Failed to unlock PDF: ', error.message);
        return false;
      }
    }

}
  