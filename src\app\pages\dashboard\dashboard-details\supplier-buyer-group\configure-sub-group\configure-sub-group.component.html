<div class="modal-header">
    <h4 class="modal-title">Sub Group</h4> 
  </div>
  <div class="card-body px-3 pb-0"> 
    <div class="row">
      <div class="col-12">
        <form [formGroup]="msxForm"> 
          <div class="row">
           <div class="col-{{formObj.colSize}}">
             <app-text [form]="msxForm" [question]="getQuestionkey('mainGroupName')" [direction]="formObj.direction"></app-text>
           </div>
           <div class="col-{{formObj.colSize}}">
            <app-select-v2  [question]="getQuestionkey('mainGroupType')" [form]="msxForm" [direction]="formObj.direction"></app-select-v2>
          </div>
         </div> 
       </form>       
      </div> 
    </div> 
   </div>
  <div class="m-content px-3 pb-3 pt-1"> 
    <div class="body-table">
        <div class="text-right">
            <button (click)="addRowform()" class="btn btn-primary">Add Row</button> 
        </div>
        <table class="table" id="custom-container">
          <thead>
            <tr>
              <th>No</th>
              <th>Sub Group</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let subGroup of listSubGroupForTable; let i = index"> 
              <tr>
                <td [width]="'50'">{{i + 1}}</td>
                <td [width]="'400'"> 
                  <div class="row">
                    <div class="col-12">  
                      <select #subGroupSelect 
                          class="form-select"  
                         (change)="onSelectChange(subGroupSelect.options[subGroupSelect.selectedIndex].dataset.key, i)">
                          <option 
                              *ngIf="isMissingOption(selectedOptions[i].value)" 
                              [ngValue]="selectedOptions[i].value" 
                              [attr.data-key]="selectedOptions[i].value">
                              {{ selectedOptions[i].value }} 
                           </option>  
                           <option *ngFor="let option of subGroupFilteredOptions" [ngValue]="option.value" [attr.data-key]="option.value">
                           {{ option.value }}
                           </option>
                      </select>
                      <div *ngIf="subGroup.isInvalid" class="help-block mt-1 text-danger"> 
                        <i class="ft-alert-circle align-middle"></i>
                        Please select one data
                      </div>
                    </div> 
                  </div> 
                </td>
                <td [width]="'50'">
                  <a
                    class="mr-2 text-danger"
                    (click)="deleteRow(subGroup, i)"
                    [title]="'delete' | translate"
                  >
                    <em class="ft-trash-2 cursor-pointer font-medium-3 align-middle"></em>
                  </a>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
    </div>
    <div class="row">
      <div class="col-6"> 
      </div>
      <div class="col-6 text-right"> 
        <a class="msx-action btn btn-warning mr-1" (click)="activeModal.dismiss('Cross click')"> 
          {{'Cancel' | translate}}
        </a>
        <a class="msx-action btn btn-primary" (click)="saveData()"> 
          {{'Save' | translate}}
        </a>
      </div>
    </div>
  </div>
  