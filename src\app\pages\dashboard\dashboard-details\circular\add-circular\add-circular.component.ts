import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { DashboardService } from 'app/services/api/dashboard.service';
import { HttpService } from 'app/services/http.service';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { MsxFormControlService } from 'app/shared/components/ms-form/msx-form-control.service';
import { QuestionTextbox } from 'app/shared/components/ms-form/questions';
import { CommonConstant } from 'app/shared/constant/common.constant';

@Component({
  selector: 'app-add-circular',
  templateUrl: './add-circular.component.html',
  styleUrls: ['./add-circular.component.scss']
})
export class AddCircularComponent implements OnInit {
  formObj: FormModel<any>;
  msxForm: FormGroup;
  groupName: string;

  @Input() dashboardName: string;
  @Input() tenantCode: string;
  @Output() result = new EventEmitter<string>();

  constructor(
    private fcs: MsxFormControlService,
    public activeModal: NgbActiveModal, 
    public http: HttpService,
    private dashboardService: DashboardService,
  ) { }

  ngOnInit(): void {
    this.setupQuestion();
    const controls = [...this.formObj.params, ...this.formObj.components];
    this.msxForm = this.fcs.toFormGroup(controls); 
  }
  setupQuestion() { 
    this.formObj = {
      name: 'groupNameForm',
      mode: CommonConstant.MODE_ADD,
      colSize: 12,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        new QuestionTextbox({
          key: 'groupName',
            label: 'New Group Name',
            placeholder: 'Type new group name here',
            maxLength: 64,
            required: true,
            validations: [
              {type: 'required', message: 'Group Name cannot be empty'},
              {type: 'maxlength', message: 'The maximum number of characters for the Group Name Name is 64'}
            ]
        }), 
      ],
      params: []
    }
  }

  getQuestionkey(key:string) {
    return this.formObj.components.find(x => x.key === key);
  }

  closeModal() { 
    this.activeModal.dismiss("cancel");
  }

  checkGroupName(){
    this.msxForm.markAllAsTouched();
    if (this.msxForm.invalid) {
      return;  
    }
  }

  async onSubmit(){ 
    // Check if the form is valid
    this.msxForm.markAllAsTouched();
    if (this.msxForm.invalid) {
      return;  
    }
 
    this.groupName = this.msxForm.get('groupName')?.value;

    const request = {
      groupName: this.groupName,
      tenantCode: this.tenantCode,
      dashboardName: this.dashboardName
    };
    await this.dashboardService.checkGroupNameCircular(request).toPromise().then(response => {
      if (response["status"]["code"] == 0) {
        this.result.emit(this.groupName);
        this.closeModal();
      } 
    });
  }

}
