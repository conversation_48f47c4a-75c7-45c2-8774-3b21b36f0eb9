<div class="row" style="margin-top: 15px">
  <div class="col-6">
     <button *ngIf="!isView" class="msx-action btn btn-primary" (click)="startConsolidate()" [disabled]="consolidateDisabled">{{consolidateBtn}}</button>
  </div>
  <div class="col-6 text-right"> 
    <ng-container>
      <a class="msx-action" [ngClass]="'btn btn-warning'" (click)="onBtnClick()"><i [ngClass]="'ft-arrow-left-circle'"></i> {{'Back To Dashboard' | translate}}</a>
    </ng-container>
  </div>
</div>
<div class="col tab-header-content">
  <div class="content-container mt-4">
    <mat-tab-group class="mat-stretch-tabs" mat-align-tabs="start" [disablePagination]="true"  [disableRipple]="true" [selectedIndex]="selectedTabIndex" (selectedTabChange)="onTabChange($event)">
      
      <!-- Anomaly -->
      <mat-tab *ngIf="!isView" class="tab-nav" translate>        
        <ng-template mat-tab-label>
          <div [ngClass]="{'high-warning': warningStatus.anomalyWarningStatus == '1' && warningStatus.anomalyWarningSeverity == 'HIGH',
                           'medium-warning': warningStatus.anomalyWarningStatus == '1' && warningStatus.anomalyWarningSeverity == 'MEDIUM'}"> 
            {{ anomalyTitle | translate }}  
            <mat-icon><i [ngClass]="warningStatus.anomalyWarningStatus == '1' ? 'ft-alert-circle' : null"></i></mat-icon> 
          </div>
        </ng-template>  
        <app-anomaly *ngIf="stateDashboard" [stateDashboard]="stateDashboard" (isDataChanges)="checkStatus($event)"></app-anomaly>
      </mat-tab>
      
      <!-- OCR Result -->
      <mat-tab *ngIf="!isView" class="tab-nav" translate>
        <ng-template mat-tab-label>
          <div [ngClass]="{'high-warning': warningStatus.ocrResultWarningStatus == '1'}"> 
            {{ ocrResultTitle | translate }}  
            <mat-icon><i [ngClass]="warningStatus.ocrResultWarningStatus == '1' ? 'ft-alert-circle' : null"></i></mat-icon>
          </div>
        </ng-template>
        <app-ocr-result *ngIf="stateDashboard" [stateDashboard]="stateDashboard" (isDataChanges)="checkStatus($event)" [isShowAlert]="warningStatus.ocrResultWarningStatus"></app-ocr-result>
      </mat-tab>

      <!-- Transaction -->
      <mat-tab *ngIf="!isView" class="tab-nav" label="{{transactionTitle | translate}}" translate>
        <app-transaction></app-transaction>
      </mat-tab>

      <!-- Insights -->
      <mat-tab class="tab-nav" label="{{insightsTitle | translate}}" translate>
        <app-insight *ngIf="stateDashboard" [stateDashboard]="stateDashboard"></app-insight>
      </mat-tab>

      <!-- Settings -->
      <mat-tab *ngIf="!isView" class="tab-nav" label="{{settingsTitle | translate}}" translate>
        <app-settings></app-settings>
      </mat-tab>

      <!-- Audit Log -->
      <mat-tab *ngIf="!isView" class="tab-nav" label="{{auditLogTitle | translate}}" translate>
        <app-audit-log></app-audit-log>
      </mat-tab>
      
      <!-- Circular -->
      <mat-tab *ngIf="!isView" class="tab-nav" translate>
        <ng-template mat-tab-label>
          <div [ngClass]="{'high-warning': warningStatus.circularWarningStatus == '1'}">
            {{ circularTitle | translate }}
            <mat-icon><i [ngClass]="warningStatus.circularWarningStatus == '1' ? 'ft-alert-circle' : null"></i></mat-icon>
          </div>
        </ng-template>
        <app-circular *ngIf="stateDashboard" [stateDashboard]="stateDashboard" (isDataChanges)="checkStatus($event)" [isShowAlert]="warningStatus.circularWarningStatus"></app-circular>
      </mat-tab>
      
      <!-- Supplier Buyer -->
      <mat-tab *ngIf="!isView" class="tab-nav" label="{{supplierBuyerTitle | translate}}" translate>
           <app-supplier-buyer-group *ngIf="stateDashboard" [stateDashboard]="stateDashboard" (isDataChanges)="checkStatus($event)"></app-supplier-buyer-group> 
      </mat-tab>
      
      <!-- Business Trx -->
      <mat-tab *ngIf="!isView" class="tab-nav" label="{{nonBusinessTitle | translate}}" translate>
          <app-business-transaction-group *ngIf="stateDashboard" [stateDashboard]="stateDashboard" (isDataChanges)="checkStatus($event)"></app-business-transaction-group>
      </mat-tab>
      
      <!-- Non Business Trx -->
      <mat-tab *ngIf="!isView" class="tab-nav" label="{{businessTitle | translate}}" translate>
        <app-non-business-transaction-group *ngIf="stateDashboard" [stateDashboard]="stateDashboard" (isDataChanges)="checkStatus($event)"></app-non-business-transaction-group>
      </mat-tab> 
      
      

    </mat-tab-group>
  </div>
</div>
