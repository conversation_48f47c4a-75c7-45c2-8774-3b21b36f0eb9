import { Component, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { Tenant } from 'app/model/tenant';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { Table } from 'app/shared/components/msx-datatable/models/table';
import { Button } from 'app/shared/components/msx-view/models/Button';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { CommonConstant } from 'app/shared/constant/common.constant';

@Component({
  selector: 'app-tenant',
  templateUrl: './tenant.component.html',
  styleUrls: ['./tenant.component.scss']
})
export class TenantComponent implements OnInit {
  serviceTenant = URLConstant.ListTenant;
  view: MsxView;
  buttonList: Button[] = [];

  searchFormObj: FormModel<any> = new FormModel<any>();
  form: FormGroup;

  constructor(private router: Router) { }

  ngOnInit(): void {
    this.initView();
    this.buttonList = [
      {name: 'New', class: 'btn btn-primary', hide: false, icon: 'ft-plus'}
    ]
  }

  onBtnClickListener(event: any) {
    switch (event['name']) {
      case this.buttonList[0].name:
        this.router.navigate([PathConstant.ADD_TENANT]);
        break;
      default:
        break;
    }
  }

  onItemClickListener(event) {
    const data = event['data'];

    switch (event['act']['type']) {
      case Act.Edit:
        return this.gotoEditTenant(data);
    }

  }

  gotoEditTenant(data: Tenant) {
    this.router.navigate([PathConstant.EDIT_TENANT], {state: data});
  }

  initView() {
    this.searchFormObj = {
      name: 'searchFOrm',
      direction: FormConstant.DIRECTION_HORIZONTAL,
      autoload: true,
      colSize: 6,
      components: [
        {
          key: 'tenantName',
          label: 'Tenant Name',
          placeholder: 'Type tenant name here',
          controlType: FormConstant.TYPE_TEXT,
          value: ''
        },
        {
          key: 'status',
          label: 'Status',
          controlType: FormConstant.TYPE_DROPDOWN,
          placeholder: 'Select Status',
          options: [
            {key: '', value: 'All'},
            {key: '1', value: 'Active'},
            {key: '0', value: 'Inactive'}
          ],
          value: ''
        }
     ],
      params: [
        {
          key: 'page',
          controlType: FormConstant.TYPE_HIDDEN,
          value: 1
        }
      ]
    }

    const TenantTable: Table<Tenant> = {
      name: 'tenants',
      list: [],
      columns: [
        {
          type: ColumnType.Number,
          prop: 'row',
          label: 'No',
          width: 10
        },
        {
          type: ColumnType.Text,
          prop: 'tenantName',
          label: 'Tenant Name',
          width: 30
        },
        {
          type: ColumnType.Text,
          prop: 'tenantCode',
          label: 'Tenant Code',
          width: 30
        },
        {
          type: ColumnType.IsActive,
          prop: 'isActive',
          label: 'Status',
          width: 15
        },
        {
          type: ColumnType.Action,
          label: 'Action',
          width: 15,
          action: [
            {
              class: CommonConstant.TEXT_PRIMARY,
              icon: 'ft-user-plus',
              type: Act.Insert,
              descr: 'Add User'
            }
          ]
        }
      ]
    }

    this.view = {
      title: 'Tenant',
      components: [
        {
          type: WidgetType.SearchFilter,
          component: this.searchFormObj
        },
        {
          type: WidgetType.Datatable,
          component: TenantTable
        }
      ]
    }
  }

  onFormListener(form: FormGroup) {
    this.form = form;
  }
}
