<div class="row" style="margin-top: 15px">
    <div class="col-8 px-4">
      <div class="content-header" style="margin-top: 0 !important;">
        {{title}}
      </div>
    </div>
    <div class="col-4 px-4 text-right"> 
        <a class="msx-action" [ngClass]="'btn btn-secondary mr-2'" (click)="goBack()"><i [ngClass]="'ft-arrow-left-circle'"></i> {{'Back To Anomaly'}}</a>
    </div>
</div> 
<div class="row">
    <div [ngClass]="anomalyState === 'FRAUD_RULE_INCONSISTENT_METADATA' ? 'col-24' : 'col-16 col-md-6'">
        <div class="px-1"> 
            <div class="card">
                <app-anomaly-detail-transaction [state]="state" #anomalyDetailTransaction (onClickRow)="onClickedRow($event)"></app-anomaly-detail-transaction>
            </div> 
        </div> 
    </div>
    <div *ngIf="anomalyState !== 'FRAUD_RULE_INCONSISTENT_METADATA'" class="col-8 col-md-6">
        <ngx-spinner></ngx-spinner>
        <div class="pdf-frame py-3">
            <pdf-viewer [src]="pdfSrc" 
            [render-text]="true"   
            (page-rendered)="pageRendered($event)" 
            [(page)]="pageVariable"
            style="display: block; height: 95vh"></pdf-viewer>
        </div>
    </div>
</div>