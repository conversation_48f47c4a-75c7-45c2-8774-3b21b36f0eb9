 <div class="row">
  <div class="col-12">
    <div class="content-sub-header px-3"  style="margin-top: 15px !important;"> 
      Transaction History
    </div>  
  </div> 
</div> 
<div class="card-body pt-0">
<div class="row">
   <div class="col-12"> 
    <ng-container *ngFor="let widget of container.components" [ngSwitch]="widget.type">
      <app-search-filter *ngSwitchCase="WidgetType.SearchFilter" [formObj]="widget.component" [service]="serviceUrl" [pageNumber]="pageNumber" (result)="getResult($event)" (selectX)="onSelect($event)" (export)="onExport($event)"></app-search-filter>
      <app-msx-datatable-transaction-history *ngSwitchCase="WidgetType.Datatable" [hideAddButton]="false" [tableObj]="widget.component" [datasource]="datasource" (getPage)="getPage($event)" (itemClickListener)="onItemClick($event)" (selectedRow)="onClickedRow($event)"></app-msx-datatable-transaction-history>
    </ng-container> 
   </div>
</div>
</div>  

 