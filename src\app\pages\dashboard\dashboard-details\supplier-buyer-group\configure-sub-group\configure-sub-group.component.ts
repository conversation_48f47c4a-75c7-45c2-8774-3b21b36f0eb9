import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core'; 
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { EditMainGroupDetailRequest } from 'app/model/api/SupplierBuyerGroup/edit-main-group-detail-request';
import { GetListSubGroupRequest } from 'app/model/api/SupplierBuyerGroup/get-list-sub-group';
import { SubGroupContainer } from 'app/model/custom/sub-group-container';
import { DashboardService } from 'app/services/api/dashboard.service'; 
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { QuestionDropdown, QuestionTextbox } from 'app/shared/components/ms-form/questions';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { GlobalService } from 'app/shared/data/global.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-configure-sub-group',
  templateUrl: './configure-sub-group.component.html',
  styleUrls: ['./configure-sub-group.component.scss']
})
export class ConfigureSubGroupComponent implements OnInit {
  @Input() dashboardName: string;
  @Input() mainGroupName: string;
  @Input() type: string;
  @Output() result = new EventEmitter<void>();
  
  formObj: FormModel<any>;
  msxForm: FormGroup;

  //list for dropdown full from api | unchanged
  listSubGroupName: any[] = []; 

  //list of already saved sub group  | unchanged
  listSubMainGroupName: any[] = []; 

  //list of filtered dropdown from already used subgroup
  //= listSubGroupName - listSubMainGroupName
  subGroupFilteredOptions: any[] = [];

  //list for table
  listSubGroupForTable: any[] = [];

  //list of selected item = used in ngModel
  selectedOptions: any[] = []; 
 
  //to save and read the previous selected item in the dropdown
  //selectedOptions doesn't save the previous caused of the ngModel
  tempSelectedOptions: any[] = [];
  
  subGroups: SubGroupContainer[] = [];

  constructor(
    private global: GlobalService,
    private cdr: ChangeDetectorRef,
    public activeModal: NgbActiveModal, 
    private toastrService: ToastrService,
    private dashboardService: DashboardService, 
    private readonly formBuilder: FormBuilder,) { 
    }

  async ngOnInit(): Promise<void> { 
    this.msxForm = this.formBuilder.group({
      mainGroupName: [this.mainGroupName || '', [Validators.required]],
      mainGroupType: [this.type || '', [Validators.required]],
    });
    this.setupQuestion();

    await this.getListSubGroupDropdown();
    await this.getListSubGroupOfMainGroup();     
  }
 
  setupQuestion() { 
    this.formObj = {
      name: 'groupNameForm',
      mode: CommonConstant.MODE_EDIT,
      colSize: 6,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        new QuestionTextbox({
          key: 'mainGroupName',
            label: 'Main Group Name',
            placeholder: 'Type group name here',
            maxLength: 64,
            required: true, 
            validations: [
              {type: 'required', message: 'Group Name cannot be empty'},
            ]
        }),
        new QuestionDropdown({
          key: 'mainGroupType',
          label: 'Type',
          placeholder: 'Select group type', 
          value: '', 
          options: [
              {key: '', value: 'Select One'},
              {key: 'Supplier', value: 'Supplier'},
              {key: 'Buyer', value: 'Buyer'},
              {key: 'Related Parties', value: 'Related Parties'}
          ], 
          required: true,
          validations: [
            {type: 'required', message: 'Must Choose Group Type!'}
          ], 
        }),  
      ],
      params: []
    }
  }

  getQuestionkey(key:string) {
    return this.formObj.components.find(x => x.key === key);
  } 

  async saveData(){ 
    const hasSelectOne = this.selectedOptions.some(option => option.value === 'Select One');
    if (hasSelectOne) {
      this.toastrService.error('Please select all data!', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
      return;
    }

    // Add items from listSubMainGroupName
    this.listSubMainGroupName.forEach(mainGroup => {
      const isInTable = this.listSubGroupForTable.some(
        tableGroup => tableGroup.subGroupName === mainGroup.subGroupName
      );
      this.subGroups.push(new SubGroupContainer(mainGroup.subGroupName, !isInTable));
    });
    
    // Add items from listSubGroupForTable that are not in listSubMainGroupName
    this.listSubGroupForTable.forEach(tableGroup => {
      const isInMainGroup = this.listSubMainGroupName.some(
        mainGroup => mainGroup.subGroupName === tableGroup.subGroupName
      );
      if (!isInMainGroup) {
        this.subGroups.push(new SubGroupContainer(tableGroup.subGroupName, false)); 
      }
    });
    
    // Ensure uniqueness
    const uniqueList = this.subGroups.filter(
      (item, index, self) =>
        self.findIndex(t => t.subGroupName === item.subGroupName) === index
    );

    const request =  new EditMainGroupDetailRequest();
    request.dashboardName = this.dashboardName;
    request.oldMainGroupName = this.mainGroupName;
    request.newMainGroupName = this.msxForm.get('mainGroupName').value;
    request.mainGroupType = this.msxForm.get('mainGroupType').value;
    request.subGroups = uniqueList; 

    await this.dashboardService.editMainGroupDetail(request).toPromise().then(response => {
      if (response["status"]["code"] == 0) {
        this.toastrService.success('Group successfully added!', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        this.result.emit();
        this.activeModal.dismiss("cancel");
      } 
    });
  } 

  addRowform(){ 
    const index = this.listSubGroupForTable.length + 1;
    let selectOne = 'Select One ';
    if(index > -1){
      selectOne = selectOne + index;
    }
    this.listSubGroupForTable.push({
      subGroupName:selectOne,
      isInvalid: true
    }) 
    this.selectedOptions.push({
      value: 'Select One',
      label: selectOne, 
    });

    this.tempSelectedOptions.push({
      key: selectOne,
      value: 'Select One', 
    });  
    this.cdr.detectChanges();
  }
 
  deleteRow(selectedOption: any, index: number) {
    //input back to available options   
    if (!selectedOption.subGroupName.includes('Select One')) { 
      this.subGroupFilteredOptions = [
        ...this.subGroupFilteredOptions,
        {
          key: selectedOption.subGroupName,
          value: selectedOption.subGroupName,
        }
      ];
    }
    this.cdr.detectChanges();
    //remove from list
    this.selectedOptions.splice(index, 1);
    this.tempSelectedOptions.splice(index, 1);
    this.listSubGroupForTable.splice(index, 1);
    this.cdr.detectChanges(); 
  }


  async getListSubGroupDropdown(){
    const request = new GetListSubGroupRequest();
    request.tenantCode = this.global.user.role.tenantCode;
    request.mainGroupName = this.mainGroupName;
    request.dashboardName = this.dashboardName;
   
    await this.dashboardService.getListSubGroupDropdown(request).toPromise().then(
      async (response) => {
         if (response['status']['code'] == 0) {
          this.listSubGroupName = response['listSubGroupName'];   
          this.cdr.detectChanges();
         }
      }
    )
  } 

  async getListSubGroupOfMainGroup(){
    const request = new GetListSubGroupRequest();
    request.tenantCode = this.global.user.role.tenantCode;
    request.mainGroupName = this.mainGroupName;
    request.dashboardName = this.dashboardName;
   
    await this.dashboardService.getListSubGroupOfMainGroup(request).toPromise().then(
      async (response) => {
         if (response['status']['code'] == 0) { 
          this.listSubMainGroupName = response['subGroups'];
          this.listSubGroupForTable = response['subGroups'].map(subGroup => ({
           ...subGroup,
           isInvalid: false // Set default to false, or use a condition if needed
          }));
          this.fillDropdownOption();  
          this.cdr.detectChanges();
         }
      }
    )
  }

  fillDropdownOption(){ 
    const filteredList = this.listSubGroupName.filter(item1 => 
      !this.listSubGroupForTable.some(item2 => item2.subGroupName === item1.subGroupName)
    );

    // Reset the options to avoid duplicates
    this.subGroupFilteredOptions = [
      //  { key: 'Select One 0', value: 'Select One' } // Default option
    ];

    for(let i = 0; i<this.listSubGroupForTable.length; i++){
      this.selectedOptions.push({
        value: this.listSubGroupForTable[i].subGroupName,
        label: this.listSubGroupForTable[i].subGroupName
      });
      this.tempSelectedOptions.push({
        key: this.listSubGroupForTable[i].subGroupName,
        value: this.listSubGroupForTable[i].subGroupName, 
      }); 
    } 
    
    for(let i = 0; i<filteredList.length; i++){
      this.subGroupFilteredOptions.push({
        key: filteredList[i].subGroupName,
        value: filteredList[i].subGroupName
      });
    } 
    this.cdr.detectChanges();
  } 
 
  onSelectChange(selectedKey : any, index: number) {  
    //add selected to list
    this.listSubGroupForTable[index]= {
      subGroupName: selectedKey,  
      isInvalid: false
    };

    this.selectedOptions[index]= {
      value: selectedKey,
      label: selectedKey,
    };

    this.tempSelectedOptions.push({
      key: selectedKey,
      value: selectedKey, 
    }); 
     
    //input the previous selected back to available options   
    const uniqueOptions = this.tempSelectedOptions.filter(tempItem =>
      !this.selectedOptions.some(selectedItem => selectedItem.label === tempItem.key) // Or any other unique property like `name`
    );
    if(uniqueOptions.length != 0){
      if(uniqueOptions[0].value !== 'Select One'){
        this.subGroupFilteredOptions.push({
          key: uniqueOptions[0].key,
          value: uniqueOptions[0].value, 
        }); 
      } 
      this.tempSelectedOptions = this.tempSelectedOptions.filter(option => option.key !== uniqueOptions[0].key);
    } 

    // Remove the selected option from the available options   
    this.subGroupFilteredOptions = this.subGroupFilteredOptions.filter(option => option.key !== selectedKey);
    this.cdr.detectChanges();
  } 

  isMissingOption(selectedKey: string): boolean {
    return (
      selectedKey &&
      !this.subGroupFilteredOptions?.some(option => option.key === selectedKey)
    );
  }
  
  
}
