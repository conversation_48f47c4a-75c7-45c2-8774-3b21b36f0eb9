import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import * as chartsData from './diversity-of-supplier-charts.charts';
import { Dashboard } from 'app/model/dashboard';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { DashboardService } from 'app/services/api/dashboard.service';
import { UtilsService } from 'app/services/api/utils.service';
import { GlobalService } from 'app/shared/data/global.service'; 

@Component({
  selector: 'app-diversity-of-supplier-charts',
  templateUrl: './diversity-of-supplier-charts.component.html',
  styleUrls: ['./diversity-of-supplier-charts.component.scss']
})
export class DiversityOfSupplierChartsComponent implements OnInit {
  @Input() stateDashboard: Dashboard;
  
  HorbarChartType = CommonConstant.HOR_BAR_CHART_TYPE;
  HorbarChartLegend = false;
  pieChartType = CommonConstant.PIE_CHART_TYPE;
  pieChartLegend = true; 
  chartColors = chartsData.ChartColors;

  // top Supplier By Amount 
  topSupplierByAmountLabels: any[] = [];
  topSupplierByAmountPieChartData: any[] = [];
  topSupplierByAmountBarChartData: any[] = [];
  topSupplierByAmountBarChartDataDetails: any[] = [];
  topSupplierByAmountBarChartOptions:any = {}; 

  // top Supplier By Freq 
  topSupplierByFreqLabels: any[] = [];
  topSupplierByFreqPieChartData: any[] = [];
  topSupplierByFreqBarChartData: any[] = [];
  topSupplierByFreqBarChartDataDetails: any[] = [];
  topSupplierByFreqBarChartDataOptions:any = {}; 

  // top Buyer By Amount 
  topBuyerByAmountLabels: any[] = [];
  topBuyerByAmountPieChartData: any[] = [];
  topBuyerByAmountBarChartData: any[] = [];
  topBuyerByAmountBarChartDataDetails: any[] = [];
  topBuyerByAmountBarChartOptions:any = {}; 

  // top Buyer By Freq 
  topBuyerByFreqLabels: any[] = [];
  topBuyerByFreqPieChartData: any[] = [];
  topBuyerByFreqBarChartData: any[] = [];
  topBuyerByFreqBarChartDataDetails: any[] = [];
  topBuyerByFreqBarChartDataOptions:any = {}; 

  pieChartOptionsByAmount = chartsData.pieChartOptionsByAmount; 
  pieChartOptionsByFreq = chartsData.pieChartOptionsByFreq; 
   

  isLoaded:boolean = false;

  constructor(private global: GlobalService, 
    private dashboardService: DashboardService,  
    private utilsService: UtilsService,  
    private cdr: ChangeDetectorRef,) { }

  async ngOnInit(): Promise<void> {
    await this.supplierBuyerTopFive();
    this.setupBarChartOptionsTopSupplierByAmount();
    this.setupBarChartOptionsTopSupplierByFreq();
    this.setupBarChartOptionsTopBuyerByAmount();
    this.setupBarChartOptionsTopBuyerByFreq();
    console.log('Options Debug:', this.topSupplierByAmountBarChartOptions);
    this.isLoaded = true;
  }

  async supplierBuyerTopFive(){
    const request = {
      tenantCode : this.global.user.role.tenantCode,
      dashboardName : this.stateDashboard.dashboardName,
    };
    await this.dashboardService.supplierBuyerTopFive(request).toPromise().then(
      (response) => {
        if (response['status']['code'] == 0) {
           // top Supplier By Amount Labels
           this.topSupplierByAmountLabels = response.topSupplierByAmountLabels; 
           this.topSupplierByAmountPieChartData = response.topSupplierByAmountPieChartData; 
           this.topSupplierByAmountBarChartData = response.topSupplierByAmountBarChartData; 
           this.topSupplierByAmountBarChartDataDetails = response.topSupplierByAmountBarChartDataDetails; 

           // top Supplier By Freq Labels
           this.topSupplierByFreqLabels = response.topSupplierByFreqLabels; 
           this.topSupplierByFreqPieChartData = response.topSupplierByFreqPieChartData; 
           this.topSupplierByFreqBarChartData = response.topSupplierByFreqBarChartData; 
           this.topSupplierByFreqBarChartDataDetails = response.topSupplierByFreqBarChartDataDetails; 

           // top Buyer By Amount Labels
           this.topBuyerByAmountLabels = response.topBuyerByAmountLabels; 
           this.topBuyerByAmountPieChartData = response.topBuyerByAmountPieChartData; 
           this.topBuyerByAmountBarChartData = response.topBuyerByAmountBarChartData; 
           this.topBuyerByAmountBarChartDataDetails = response.topBuyerByAmountBarChartDataDetails; 

           // top Buyer By Freq Labels
           this.topBuyerByFreqLabels = response.topBuyerByFreqLabels; 
           this.topBuyerByFreqPieChartData = response.topBuyerByFreqPieChartData; 
           this.topBuyerByFreqBarChartData = response.topBuyerByFreqBarChartData; 
           this.topBuyerByFreqBarChartDataDetails = response.topBuyerByFreqBarChartDataDetails; 
        }
      }
    )
  } 

  setupBarChartOptionsTopSupplierByAmount(){
    const topSupplierByAmountBarChartDataDetails = this.topSupplierByAmountBarChartDataDetails;
  
    const suggestedMax = this.topSupplierByAmountPieChartData.reduce((max, current) => Math.max(max, current), -Infinity) * 1.5;
    this.topSupplierByAmountBarChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        xAxes: [{ 
          ticks: {
            beginAtZero: true, // Start at 0 for better readability
            min: 0,  
            max: suggestedMax,  
            callback: function (value: number) {
              return UtilsService.abbreviateNumber(value); // Display absolute values on Y-axis
            } 
          },
          scaleLabel: {
            display: true,
            labelString: 'Total Amount'
          },  
        }],
      },
      plugins: {
        datalabels: {
          display: true,
          formatter: (value, context) => {
            const total = context.dataset.data.reduce((sum: number, val: number) => sum + val, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            const valueAbbreviate = UtilsService.abbreviateNumber(value);
            return `${[valueAbbreviate]} (${percentage}%)`;
          },
          color: '#000', // Label color
          font: {
            weight: 'bold'
          },
          anchor: 'end',
          align: 'end',
          clamp:true,
        },
      },
      tooltips: {
        callbacks: {
          label: function (tooltipItem, data) {
            const value = data.datasets[0].data[tooltipItem.index]; // Get amount
            const valueFormatted = value.toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            });
            const accounts = topSupplierByAmountBarChartDataDetails[tooltipItem.index]; // Get account value
            return `${valueFormatted} in ${accounts} transaction(s)`;
          }
        }
      },
    };
  }

  setupBarChartOptionsTopSupplierByFreq(){
    const topSupplierByFreqBarChartDataDetails = this.topSupplierByFreqBarChartDataDetails;
    const suggestedMax = this.topSupplierByFreqPieChartData.reduce((max, current) => Math.max(max, current), -Infinity) * 1.3;
    this.topSupplierByFreqBarChartDataOptions = {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        xAxes: [{ 
          ticks: {
            beginAtZero: true, // Start at 0 for better readability 
            min: 0,  
            max: suggestedMax,  
            autoSkip: true, // Automatically skip some labels to prevent overlap
          },
          scaleLabel: {
            display: true,
            labelString: 'Total Amount'
          }
        }],
      },
      plugins: {
        datalabels: {
          formatter: (value, context) => {
            const total = context.dataset.data.reduce((sum: number, val: number) => sum + val, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${[value]} (${percentage}%)`;
          },
          color: '#000', // Label color
          font: {
            weight: 'bold'
          },
          anchor: 'end',
          align: 'end',
          clamp:true,
        },
      },
      tooltips: {
        callbacks: {
          label: function (tooltipItem, data) {
            const value = data.datasets[0].data[tooltipItem.index]; // Get amount
            const accounts = topSupplierByFreqBarChartDataDetails[tooltipItem.index]; // Get account value
            const accountsFormatted = accounts.toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            });
            return `${accountsFormatted} in ${value} transaction(s)`;
          }
        }
      },
    };
  }

  setupBarChartOptionsTopBuyerByAmount(){
    const topBuyerByAmountBarChartDataDetails = this.topBuyerByAmountBarChartDataDetails;
    const suggestedMax = this.topBuyerByAmountPieChartData.reduce((max, current) => Math.max(max, current), -Infinity) * 1.5;
    
    this.topBuyerByAmountBarChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        xAxes: [{ 
          ticks: { 
            beginAtZero: true, // Start at 0 for better readability
            min: 0,  
            max: suggestedMax,
            callback: function (value: number) {
              return UtilsService.abbreviateNumber(value); // Display absolute values on Y-axis
            } 
          },
          scaleLabel: {
            display: true,
            labelString: 'Total Amount'
          }
        }],
      },
      plugins: {
        datalabels: {
          formatter: (value, context) => {
            const total = context.dataset.data.reduce((sum: number, val: number) => sum + val, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            const valueAbbreviate = UtilsService.abbreviateNumber(value);
            return `${[valueAbbreviate]} (${percentage}%)`;
          },
          color: '#000', // Label color
          font: {
            weight: 'bold'
          },
          anchor: 'end',
          align: 'end',
          clamp: true, 
        },
      },
      tooltips: {
        callbacks: {
          label: function (tooltipItem, data) {
            const value = data.datasets[0].data[tooltipItem.index]; // Get amount
            const valueFormatted = value.toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            });
            const accounts = topBuyerByAmountBarChartDataDetails[tooltipItem.index]; // Get account value
            return `${valueFormatted} in ${accounts} transaction(s)`;
          }
        }
      },
    };
  }

  setupBarChartOptionsTopBuyerByFreq(){
    const topBuyerByFreqBarChartDataDetails = this.topBuyerByFreqBarChartDataDetails;
    const suggestedMax = this.topBuyerByFreqPieChartData.reduce((max, current) => Math.max(max, current), -Infinity) * 1.3;
    this.topBuyerByFreqBarChartDataOptions = {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        xAxes: [{ 
          ticks: {
            beginAtZero: true, // Start at 0 for better readability 
            min: 0,  
            max: suggestedMax,  
          },
          scaleLabel: {
            display: true,
            labelString: 'Total Amount'
          }
        }],
      },
      plugins: {
        datalabels: {
          formatter: (value, context) => {
            const total = context.dataset.data.reduce((sum: number, val: number) => sum + val, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${[value]} (${percentage}%)`;
          },
          color: '#000', // Label color
          font: {
            weight: 'bold'
          },
          anchor: 'end',
          align: 'end',
          clamp:true,
        },
      },
      tooltips: {
        callbacks: {
          label: function (tooltipItem, data) { 
            const value = data.datasets[0].data[tooltipItem.index]; // Get amount
            const accounts = topBuyerByFreqBarChartDataDetails[tooltipItem.index]; // Get account value
            const accountsFormatted = accounts.toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            });
            return `${accountsFormatted} in ${value} transaction(s)`;
          }
        }
      },
    };
  }

  chartClicked(e: any): void {
    //your code here
  }

  chartHovered(e: any): void {
    //your code here
  }

}
