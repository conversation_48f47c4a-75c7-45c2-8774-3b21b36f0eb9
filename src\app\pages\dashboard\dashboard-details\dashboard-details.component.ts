import { PlatformLocation } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Dashboard } from 'app/model/dashboard';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { ConfigService } from 'app/shared/services/config.service';
import { ToastrService } from 'ngx-toastr';
import * as swalFunction from 'app/shared/data/sweet-alerts';
import { NavigationService } from 'app/services/api/navigation.service';
import { OcrResultComponent } from './ocr-result/ocr-result.component';
import { SupplierBuyerGroupComponent } from './supplier-buyer-group/supplier-buyer-group.component';
import { InsightComponent } from './insight/insight.component';
import { AnomalyComponent } from './anomaly/anomaly.component';
import { NonBusinessTransactionGroupComponent } from './non-business-transaction-group/non-business-transaction-group.component';
import { BusinessTransactionGroupComponent } from './business-transaction-group/business-transaction-group.component';
import { CircularComponent } from './circular/circular.component';
import { DashboardService } from 'app/services/api/dashboard.service';
import { GlobalService } from 'app/shared/data/global.service';
import { WarningStatus } from 'app/model/warning-status';
import { CommonConstant } from 'app/shared/constant/common.constant';

@Component({
  selector: 'app-dashboard-details',
  templateUrl: './dashboard-details.component.html',
  styleUrls: ['./dashboard-details.component.scss']
})
export class DashboardDetailsComponent implements OnInit {

  title: string = 'Dashboard Detail' 
  stateDashboard: Dashboard;
  config: any = {};
  selectedTabIndex:number;
  popStateListener: any;
  isView: boolean = false; 
  warningStatus: WarningStatus = new WarningStatus();
  basicInfo: any;

  swal = swalFunction;

  //button consolidate
  consolidateBtn: string = 'Consolidate';
  consolidateDisabled: boolean = false;
  consolidateHide: boolean = true;
  consolidateStatus: string;

  //title tab
  anomalyTitle: string = CommonConstant.ANOMALY_TITLE;
  ocrResultTitle: string = CommonConstant.OCR_RESULT_TITLE;
  transactionTitle: string =  CommonConstant.TRANSACTION_TITLE;
  insightsTitle: string = CommonConstant.INSIGHTS_TITLE;
  settingsTitle: string = CommonConstant.SETTINGS_TITLE;
  auditLogTitle: string = CommonConstant.AUDIT_LOG_TITLE;
  
  supplierBuyerTitle: string = CommonConstant.SUPPLIER_BUYER_TITLE;
  businessTitle: string = CommonConstant.NON_BUSINESS_TRANSACTION_TITLE;
  nonBusinessTitle: string = CommonConstant.BUSINESS_TRANSACTION_TITLE;
  circularTitle: string = CommonConstant.CIRCULAR_TRANSACTION_TITLE;

  @ViewChild(InsightComponent) insightComponent!: InsightComponent; 
  @ViewChild(NonBusinessTransactionGroupComponent) NonBusinessTransactionGroupComponent!: NonBusinessTransactionGroupComponent;
  @ViewChild(BusinessTransactionGroupComponent) BusinessTransactionGroupComponent!: BusinessTransactionGroupComponent;
  
  constructor(
    private router: Router,
    private configService: ConfigService,
    private platformLocation: PlatformLocation,
    private navigationService: NavigationService,
    private dashboardService: DashboardService,
    private global: GlobalService,
    private cdr: ChangeDetectorRef,
    private toastrService: ToastrService,
  ) {
    const navigation = this.router.getCurrentNavigation();
    this.stateDashboard = navigation.extras.state?.state as Dashboard;
    this.selectedTabIndex = navigation.extras.state?.indexTab;
    this.isView = navigation.extras.state?.isView;
  }

  async ngOnInit(){  
    await this.toggleSidebar(true);
    this.checkStateDashboard();
    this.checkClickedBackButton();

    await this.getWarningStatus(); 
 
    if(!this.isView){
      await this.checkConsolidateButton(); 
    }
  }

  checkStateDashboard(){
    if (this.stateDashboard == undefined) {
      this.toastrService.error('Dashboard Name not Found!');
    }
  }

  ngOnDestroy() {
    if (this.popStateListener) {
        this.popStateListener(); // Remove the listener on component destroy
    }
  }
 
  checkStatus(isChanged:boolean){
    if(isChanged){
      this.getWarningStatus();
      this.checkConsolidateButton()
    }
  }

  async getWarningStatus(){  
    //save data 
    const request = { 
      tenantCode: this.global.user.role.tenantCode,
      dashboardName: this.stateDashboard.dashboardName
    };
    await this.dashboardService.getWarningStatus(request).toPromise().then(response => {
      if (response.status.code == 0) {
        this.warningStatus.ocrResultWarningStatus = response.ocrResultWarningStatus;
        this.warningStatus.circularWarningStatus = response.circularWarningStatus;
        this.warningStatus.anomalyWarningStatus = response.anomalyWarningStatus;
        if(response.anomalyWarningSeverity !== undefined){
          this.warningStatus.anomalyWarningSeverity = response.anomalyWarningSeverity;
        } 
        console.log('Warning Status',this.warningStatus);
        this.cdr.detectChanges();
      }
    });
  }  

  checkClickedBackButton(){
    // Listen for back button clicks
    this.popStateListener = this.platformLocation.onPopState(async () => {
      // Check if the previous route was the dashboard
    const currentUrl = this.navigationService.getCurrentUrl(); 
    if (currentUrl === '/dashboard') {
      this.swal.ErrorWithPromise('Cannot Go Back To Dashboard Detail!').
      then((result) => {
        this.router.navigate([PathConstant.DASHBOARD]);
      });
    }
    });
  }

  onTabChange(event:any){
   console.log('onTabChange', event);

   // to refresh hit api 
    if(event.index == 0){
      this.insightComponent.refreshPage();
    } else if(event.index == 3){
      this.BusinessTransactionGroupComponent.refreshPage();
    } else if(event.index == 4){
      this.NonBusinessTransactionGroupComponent.refreshPage();
    } 

    setTimeout(() => {
      window.dispatchEvent(new Event('resize')); // Force UI to recalculate layout
      this.cdr.detectChanges();
    }, 100);
  }

  async onBtnClick(){
    await this.toggleSidebar(false);
    this.router.navigate([PathConstant.DASHBOARD]);
  }

  async toggleSidebar(isCollapsed:boolean) {
    this.configService.templateConf$.subscribe((templateConf) => {
      if (templateConf) {
        this.config = templateConf;
      } 
    });
    const conf = this.config;
    conf.layout.sidebar.collapsed = isCollapsed;
    this.configService.applyTemplateConfigChange({ layout: conf.layout });

    setTimeout(() => {
      this.fireRefreshEventOnWindow();
    }, 300);
  }

  fireRefreshEventOnWindow = function () {
    const evt = document.createEvent('HTMLEvents');
    evt.initEvent('resize', true, false);
    window.dispatchEvent(evt);
  };

  async getInsightBasicInformation(){
    const request = {
      tenantCode : this.global.user.role.tenantCode,
      dashboardName : this.stateDashboard.dashboardName,
    };
    await this.dashboardService.getInsightBasicInformation(request).toPromise().then(
      (response) => {
        if (response['status']['code'] == 0) {
           this.basicInfo = response; 
        }
      }
    )
  } 

  async getConsolidateStatus() {
    const request = {
      dashboardName : this.stateDashboard.dashboardName,
      tenantCode : this.global.user.role.tenantCode,
    };
    await this.dashboardService.consolidateStatus(request).toPromise().then(response => {
      if (response.status.code === 0) {
        this.consolidateStatus = response['consolidateStatus'];
      }  
    })
  }

  async startConsolidate() {
    const request = {
      dashboardName : this.stateDashboard.dashboardName,
      tenantCode : this.global.user.role.tenantCode,
    };
    await this.dashboardService.startConsolidate(request).toPromise().then(response => {
      if (response.status.code === 0) {
        this.swal.Success('Consolidate Started!');
        this.router.navigate([PathConstant.DASHBOARD]);
      }  
    })
  }

  async checkConsolidateButton(){
    await this.getInsightBasicInformation();
    await this.getConsolidateStatus(); 

    //disable
    if (this.basicInfo.isDataChangesAfterConsolidate == 0 &&
      (this.consolidateStatus == CommonConstant.CANNOT_CONSOLIDATE ||
       this.consolidateStatus == CommonConstant.CONSOLIDATE_COMPLETE)) {
       this.consolidateDisabled = true;
    } else {
      this.consolidateDisabled = false;
    }
  
    //to show button only that hit api done
    this.consolidateHide = false;
    
    this.cdr.detectChanges();
  }
}
