
import { CircularTransaction } from "app/model/circular-transaction";
import { Act } from "app/shared/components/msx-datatable/enums/act";
import { Align } from "app/shared/components/msx-datatable/enums/align";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { CommonConstant } from "app/shared/constant/common.constant";

export const CircularTransactionTable: Table<CircularTransaction> = {
    name: 'transactions',  
    list: [],
    columns: [
        {
            type: ColumnType.Number,
            prop: 'num',
            label: 'No',
            width: 50
        }, 
        {
            type: ColumnType.Text,
            prop: 'accountNo',
            label: 'Account No',
            width: 50
        },  
        {
            type: ColumnType.Text,
            prop: 'trxDate',
            label: 'Date',
            width: 50
        },
        {
            type: ColumnType.Text,
            prop: 'description',
            label: 'Description',
            width: 50
        },
        {
            type: ColumnType.Number,
            prop: 'amount',
            label: 'Amount',
            width: 50
        },  
        {
            type: ColumnType.Text,
            prop: 'type',
            label: 'Type',
            width: 50
        },  
    ]
}