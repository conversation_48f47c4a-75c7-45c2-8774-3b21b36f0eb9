import { ChangeDetectorRef, Component, Input, NgZone, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { DashboardService } from 'app/services/api/dashboard.service';
import { HttpService } from 'app/services/http.service';
import { MsxFormControlService } from 'app/shared/components/ms-form/msx-form-control.service';
import { ToastrService } from 'ngx-toastr';
import { ConsolidatedBankStatementTable } from './consolidated-bank-statement-list-view';
import { BehaviorSubject } from 'rxjs';
import { Page } from 'app/shared/components/msx-datatable/models/page';

@Component({
  selector: 'app-consolidated-bank-statement',
  templateUrl: './consolidated-bank-statement.component.html',
  styleUrls: ['./consolidated-bank-statement.component.scss']
})
export class ConsolidatedBankStatementComponent implements OnInit {
  @Input() dashboardName: string;
  @Input() tenantCode: string;

  table = ConsolidatedBankStatementTable;
  datasource: BehaviorSubject<any> = new BehaviorSubject<any>([]);

  constructor(
    private fcs: MsxFormControlService,
    public activeModal: NgbActiveModal, 
    public http: HttpService,
    private ngZone: NgZone, 
    private cdr: ChangeDetectorRef,
    private dashboardService: DashboardService,
    private toastrService: ToastrService,) { }

  
  async ngOnInit() { 
      await this.consolidatedBankStatementList().then(() => {
        this.ngZone.run(() => {
          this.cdr.markForCheck();
        })
      });
  }

  async getPage(page: Page) {
    console.log('requestPage', page);
    await this.consolidatedBankStatementList(page.pageNumber + 1).then(() => {
      this.ngZone.run(() => {
        this.cdr.markForCheck();
      })
    });
  }  

  async consolidatedBankStatementList(pageNumber: number = 1) {  
    const request = {
      tenantCode : this.tenantCode,
      dashboardName : this.dashboardName, 
    }; 

    await this.dashboardService.consolidatedBankStatementList(request).toPromise().then(
      (response) => {
        if (response['status']['code'] == 0) {
          this.datasource.next(response);
        }
      }
    )
  } 

}
