
import { Anomalies } from "app/model/anomalies";
import { Act } from "app/shared/components/msx-datatable/enums/act";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { CommonConstant } from "app/shared/constant/common.constant";

export const AnomalyTable: Table<Anomalies> = {
    name: 'anomalies', 
      list: [],
      columns: [
          {
              type: ColumnType.Number,
              prop: 'no',
              label: 'No',
              width: 100
          },
          {
              type: ColumnType.Text,
              prop: 'file',
              label: 'File',
              width: 100
          },
          {
              type: ColumnType.Text,
              prop: 'accountNo',
              label: 'Account No',
              width: 100
          },
          {
              type: ColumnType.Date,
              format: CommonConstant.FORMAT_DATE,
              prop: 'date',
              label: 'Date',
              width: 100
          },
          {
              type: ColumnType.Text,
              prop: 'description',
              label: 'Description',
              width: 100
          },
          {
              type: ColumnType.Currency,
              prop: 'amount',
              label: 'Amount',
              width: 100
          },
          {
              type: ColumnType.Text,
              prop: 'type',
              label: 'Type',
              width: 100
          },
          {
            type: ColumnType.Text,
            class:'text-capitalize',
            prop: 'risk',
            label: 'Risk',
            width: 100
          },
          {
            type: ColumnType.Action,
            label: 'Action',
            width: 100,
            action: [  
                {
                    class: CommonConstant.TEXT_DANGER,
                    icon: 'ft-trash-2',
                    descr: 'Delete',
                    type: Act.Delete, 
                }
            ]
        }
    ]
}