import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Dashboard } from 'app/model/dashboard';
import { DashboardService } from 'app/services/api/dashboard.service';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { MsxPagingComponent } from 'app/shared/components/msx-view/msx-paging.component';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { GlobalService } from 'app/shared/data/global.service';
import { ConfigService } from 'app/shared/services/config.service';
import { ToastrService } from 'ngx-toastr';
import { Button } from 'app/shared/components/msx-view/models/Button';
import * as swalFunction from 'app/shared/data/sweet-alerts'; 
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType'; 
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { QuestionDropdown, QuestionDate } from 'app/shared/components/ms-form/questions';
import { AuditLogTable } from './audit-log.list.view';
import { FormModel } from 'app/shared/components/ms-form/models';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { AddTransactionGroupComponent } from '../add-transaction-group/add-transaction-group.component';
import { AuditLogGroup } from 'app/model/audit-log-group';
import { PathConstant } from 'app/shared/constant/PathConstant';

@Component({
  selector: 'app-audit-log',
  templateUrl: './audit-log.component.html',
  styleUrls: ['./audit-log.component.scss']
})
export class AuditLogComponent implements OnInit {
  @Input() stateDashboard: Dashboard;
  @Output() isDataChanges = new EventEmitter<boolean>();
  
  view: MsxView;
  buttonList: Button[];
  serviceUrl = URLConstant.ListAuditLog;
  searchFilter: FormModel<string>;
  
  swal = swalFunction;

  @ViewChild(MsxPagingComponent) msxPagingComponent!: MsxPagingComponent;

  constructor(
    private modalService: NgbModal, 
    private router: Router,  
    private dashboardService: DashboardService,
    private toastrService: ToastrService,
    private global: GlobalService,
    private cdr: ChangeDetectorRef,
    private configService: ConfigService,) { }

  ngOnInit(): void {
    this.initSearchFilter();
    this.initView();
  }

  initView() {
    this.view = {
      title: ' - Audit Log',
      titleDynamic: this.stateDashboard.dashboardName,
      isTabTitle: true,
      components: [
        {
          type: WidgetType.SearchFilter,
          component: this.searchFilter
        },
        {
          type: WidgetType.Datatable,
          component: AuditLogTable
        }
      ]
    }; 
    
    this.buttonList = [
     {name: 'Add New Audit Log', class: 'btn btn-primary', hide: false, icon: 'ft-plus'}
    ];
  }

  onBtnClickListener($event) {
    const buttonName = $event['name'];
    if (buttonName == 'Add New Audit Log') {
      this.goToAddTransaction();
    } 
  }

  onItemClickListener(event) {
    const data = event['data'];
    switch (event['act']['type']) {
      case Act.Edit:
        return this.editData(data);
    }
  }

  initSearchFilter() {
    this.searchFilter = {
      name: 'AddTransactionGroupSearchForm',
      autoload: true,
      direction: FormConstant.DIRECTION_HORIZONTAL,
      colSize: 6,
      components: [
        new QuestionDate({
          key: 'dateTime',
          label: 'Date Time',
          placeholder: 'Transaction start date',
          required: false,
          value: ''
        }),
        new QuestionDate({
          key: 'dateTime',
          label: 'Date Time',
          placeholder: 'Transaction end date',
          required: false,
          value: ''
        }),
        new QuestionDropdown({
          key: 'module',
          label: 'Module',
          placeholder: 'Select module',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          params: {
            dashboardName: this.stateDashboard.dashboardName,
            tenantCode: this.global.user.role.tenantCode
          }
        }),
        new QuestionDropdown({
          key: 'user',
          label: 'User',
          placeholder: 'Select user',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'userList',
            key: 'user',
            value: 'userList'
          },
          params: {
            dashboardName: this.stateDashboard.dashboardName,
            tenantCode: this.global.user.role.tenantCode
          }
        }),
        new QuestionDropdown({
          key: 'action',
          label: 'Action',
          placeholder: 'Select action',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          params: {
            dashboardName: this.stateDashboard.dashboardName,
            tenantCode: this.global.user.role.tenantCode
          }
        }),
      ],
      params: [ 
          {
            key: 'tenantCode',
            controlType: FormConstant.TYPE_HIDDEN,
            value: this.global.user.role.tenantCode 
          },
          {
            key: 'dashboardName',
            controlType: FormConstant.TYPE_HIDDEN,
            value: this.stateDashboard.dashboardName 
          }, 
          {
            key: 'page',
            controlType: FormConstant.TYPE_HIDDEN,
            value: 1
        }
      ]
    }
  }  

  goToAddTransaction(){  
    const modal = this.modalService.open(AddTransactionGroupComponent, {
      centered: true,
      backdrop: 'static',
      size: 'xl'
    });  
    modal.componentInstance.formSource = CommonConstant.AUDIT_LOG;
    modal.componentInstance.dashboardName = this.stateDashboard.dashboardName;  

    modal.componentInstance.result.subscribe(() => {
      this.msxPagingComponent.refreshSearch(); // Refresh data  
      this.isDataChanges.emit(true);
      this.cdr.detectChanges();
    }); 
  }

  // deleteData(data: any) {
  //   this.swal.Confirm('Are you sure? This Audit Log will be permanently lost.').then(
  //     async (result) => {
  //       if (result.isConfirmed) {
  //         const request = { 
  //           action: data.actionCode,
  //           tenantCode: this.global.user.role.tenantCode,
  //           dashboardName: this.stateDashboard.dashboardName
  //         };
  //         await this.dashboardService.deleteGroupAuditLog(request).toPromise().then(
  //           async (response) => {
  //              if (response['status']['code'] == 0) {
  //               this.toastrService.success('Data successfully deleted!', null, {
  //                 positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
  //               });
  //                this.msxPagingComponent.refreshSearch(); // Refresh data 
  //                this.isDataChanges.emit(true);
  //                this.cdr.detectChanges();
  //              }
  //           }
  //         )
  //       }
  //     }
  //   );
  // }

  // viewData(data: any) {
  //   // Since you don't want detail functionality, we can either remove this or show a simple view
  //   this.toastrService.info('Audit Log details: ' + data.description, null, {
  //     positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
  //   });
  // }

}
