import {AfterViewInit, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {FormModel} from '../../models';
import {AbstractControl, FormGroup, Validators} from '@angular/forms';
import {MsxFormControlService} from '../../msx-form-control.service';
import {FormConstant} from '../../constants/form.constant';
import {BehaviorSubject, Subscription} from 'rxjs';
import * as moment from 'moment';
import { DeviceDetectorService } from 'ngx-device-detector';
import { DashboardService } from 'app/services/api/dashboard.service';
import { HttpService } from 'app/services/http.service';

@Component({
  selector: 'app-search-filter-transaction',
  templateUrl: './search-filter-transaction.component.html',
  styleUrls: ['./search-filter-transaction.component.scss']
})
export class SearchFilterTransactionComponent implements OnInit, AfterViewInit {

  @Input() formObj: FormModel<any>;
  @Input() service: string;
  @Input() pageNumber: BehaviorSubject<number> = new BehaviorSubject(1);

  @Output() data: EventEmitter<any> = new EventEmitter<any>();
  @Output() result: EventEmitter<any> = new EventEmitter<any>();
  @Output() export: EventEmitter<any> = new EventEmitter<any>();
  @Output() form: EventEmitter<FormGroup> = new EventEmitter<FormGroup>();

  @Output()
  selectX: EventEmitter<{prop: string, data: {key: string, value: string}}> = new EventEmitter<{prop: string, data: {key: string, value: string}}>();

  selected: any;
  searchForm: FormGroup;
  isCollapsed = false;
  firstInit = true;
  isVerticalLayout: boolean;
  subscription: Subscription;
  isMobile = false;
  exportExcelLabel = 'Export Excel';

  constructor(
    private readonly formControlService: MsxFormControlService, 
    private readonly http: HttpService,
    private deviceService: DeviceDetectorService, 
    private cdr: ChangeDetectorRef,
    private dashboardService: DashboardService,) {
    if (deviceService.isMobile()) {
      this.isMobile = true;
    }
  }

  ngOnInit(): void {
    this.initSearchForm();

    if (this.isMobile) {
      this.formObj.colSize = 12;
    }

    if (this.formObj.autoload) {
      console.log('autoload page', this.formObj.autoload);
      this.firstInit = false;
    }

    if (this.formObj.exportExcel && !this.formObj.exportExcelLabel) {
      this.formObj.exportExcelLabel = 'Export Excel';
    }
  }

  doSearch(initial?: boolean) {

    this.firstInit = initial;
    this.data.emit(this.searchForm.value);
    console.log('searchForm', this.searchForm.value);
    this.searchForm.markAllAsTouched();

    if(this.searchForm.invalid){
      return
    }

    const requestObj = this.searchForm.value;
    const dateQuestions = this.formObj.components.filter(x => x.controlType === FormConstant.TYPE_DATE);
    if (dateQuestions) {
      for (const mQuest of dateQuestions) {
        if (!requestObj[mQuest.key] || moment(requestObj[mQuest.key], 'YYYY-MM-DD').isValid()) {
          continue;
        }

        // Fix Parse date value
        const value = requestObj[mQuest.key];
        const date  = new Date(value['year'], value['month'] - 1, value['day']);
        requestObj[mQuest.key] = moment(date).format('YYYY-MM-DD');
      }
    }

    if (this.service) {
      this.http.post(this.service, this.searchForm.value).subscribe(response => {
        this.result.emit(response);
      });
    }
  }

  doFilter() {
    this.pageNumber.next(1);
    if (this.firstInit) {
      this.doSearch();
    }
  }

  initSearchForm() {
    const controls  = [...this.formObj.params, ...this.formObj.components];
    console.log('searchFilter Control', controls);
    this.searchForm = this.formControlService.toFormGroup(controls);
    this.form.emit(this.searchForm);
    this.result.emit({});
    this.checkDirection();

    //reset period
    this.getListPeriod('');
    this.setValidators();
  }

  doExport() {
    this.export.emit(this.searchForm.value);
  }

  checkDirection() {
    this.isVerticalLayout = this.formObj.direction === FormConstant.DIRECTION_VERTICAL;
  }

  onSelect(result) {
    this.selectX.emit(result);
  }

  ngAfterViewInit(): void {
    this.subscription = this.pageNumber.subscribe(value => {
      this.searchForm.patchValue({page: value});
      if (!this.firstInit) {
        this.doSearch();
      }
    });
  }

  getQuestion(key: string) {
    return this.formObj.components.find(q => q.key === key);
  }

  getOptions(key: string) {
    const question = this.getQuestion(key);
    return question.options;
  }

  onSelectDropdown($event) {
    console.log('select event', $event);
    switch ($event.prop) {
      case 'accountNo':
        this.getListPeriod($event.data.key);
        break;
      default:
        console.log('default action');
        break;
    }
    this.setValidators();
  }

  onSelectDropdownX($event) { 
    this.setValidators();
  }

  getListPeriod(key:string){
    const question = this.formObj.components.find(c => c.key === 'period');
    this.searchForm.get('period').setValue('');
   
    const request = { 
      groupName: this.selected,
      dashboardName: question['params'].dashboardName,
      tenantCode: question['params'].tenantCode,  
      accountNo: key
    };
   this.dashboardService.getListPeriod(request).subscribe(res => {
    console.log('getListPeriod', res); 
    const defaultAnswer = [{ key: '', value: 'Select One' }];
    let options = [];

    for (const period of res['periods']) {
      const formattedPeriod = this.formatPeriod(period.period);
      const option = {key: period.period, value: formattedPeriod};
      options.push(option);
    }

    options = defaultAnswer.concat(options);
    
    
    question.options = options;
    console.log(question.key, question.options);

    // if (res['periods'].length === 1) {
    //   this.searchForm.get('period').setValue(res['periods'][0].period);
    // }

    this.cdr.detectChanges();
    })
  } 

  formatPeriod(period: string): string {
    const [year, month] = period.split('-');
    const date = new Date(+year, +month - 1); // Month is zero-based
    return date.toLocaleString('en-US', { month: 'short', year: 'numeric' }).toUpperCase();
  }

  setValidators() {
    const accountNo = this.searchForm.get('accountNo').value;
    const periodControl = this.searchForm.get('period');
    const categoryControl = this.searchForm.get('category');
  
    // Only proceed if accountNo is not empty
    if (accountNo === '') {
      return;
    }
  
    // Define the condition mappings
    const conditions = {
      'bothEmpty': periodControl.value === '' && categoryControl.value === '',
      'periodNotEmpty': periodControl.value !== '' && categoryControl.value === '',
      'categoryNotEmpty': periodControl.value === '' && categoryControl.value !== ''
    };
  
    // Define actions based on the conditions
    const actions = {
      'bothEmpty': () => this.setRequiredValidators(periodControl, categoryControl),
      'periodNotEmpty': () => this.clearValidators(categoryControl),
      'categoryNotEmpty': () => this.clearValidators(periodControl)
    };
  
    // Loop through the conditions and apply corresponding actions
    Object.keys(conditions).forEach((condition) => {
      if (conditions[condition]) {
        actions[condition]();
      }
    });
  
    // Optionally log the search form to see the current state
    console.log('select-v2 this.searchFormX', this.searchForm); 
  }
  
  private setRequiredValidators(periodControl: AbstractControl, categoryControl: AbstractControl) {
    periodControl.setValidators([Validators.required]);
    periodControl.updateValueAndValidity();
  
    categoryControl.setValidators([Validators.required]);
    categoryControl.updateValueAndValidity();
  }
  
  private clearValidators(control: AbstractControl) {
    control.clearValidators();
    control.updateValueAndValidity();
  }
  


}
