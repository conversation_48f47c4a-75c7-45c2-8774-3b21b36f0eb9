import { Injectable } from '@angular/core';
import {Paths} from './paths';
import {HttpService} from '../http.service';
import {GlobalService} from '../../shared/data/global.service';
import {BaseRequest} from '../../model/api/base.request';

@Injectable({
  providedIn: 'root'
})
export class AppService {
  protected path: Paths;

  defaultLocale = 'en';

  constructor(public readonly client: HttpService, public readonly global: GlobalService) {
    this.path = new Paths();
  }

  protected build(request: BaseRequest) {
    request.audit = { callerId: this.global.user?.loginId || (request['email'] || '') };
    return request;
  }
}
