<div class="row text-left" *ngIf="isLoaded">
  <div class="col-12" >
    <div class="card card-chart">
      <div class="card-header">
        <h3 class="card-title-2 font-weight-bold text-center">Diversity Of Supplier</h3>
      </div> 
        <div class="chart-wrap mx-3 mt-2 mb-3" style="border: 3px solid #692e2e;border-radius: 10px 10px 0px 0px;">
          <div class="card-header">
            <div class="card-header">
              <h4 class="card-title font-weight-bold text-center">Top 5 Supplier by Transaction Amount</h4>
            </div> 
          </div> 
          <div class="card-body">
            <div class="row">
              <!-- HorizontalBar Chart Starts -->
              <div class="col-sm-6 card-block">
                <div id="bar-bar2" class="height-300">
                  <canvas baseChart class="chart" [datasets]="topSupplierByAmountBarChartData" [labels]="topSupplierByAmountLabels" [options]="topSupplierByAmountBarChartOptions" [colors]="chartColors" [legend]="HorbarChartLegend"
                     [chartType]="HorbarChartType" (chartHover)="chartHovered($event)" (chartClick)="chartClicked($event)"></canvas>
               </div>
              </div>
              <!-- HorizontalBar Chart Ends -->

              <!-- Pie Chart Starts -->
               <div class="col-sm-6 card-block">
                 <div id="pie-chart1" class="height-300">
                     <canvas baseChart class="chart" [data]="topSupplierByAmountPieChartData" [labels]="topSupplierByAmountLabels" [options]="pieChartOptionsByAmount" [chartType]="pieChartType" (chartHover)="chartHovered($event)" [colors]="chartColors"
                     [legend]="pieChartLegend" (chartClick)="chartClicked($event)"></canvas>
                 </div>
               </div>
               <!-- Pie Chart Ends --> 
             </div>
          </div>
        </div>

        <div class="chart-wrap mx-3 mb-3" style="border: 3px solid #692e2e;border-radius: 0px 0px 10px 10px;">
          <div class="card-header">
            <div class="card-header">
              <h4 class="card-title font-weight-bold text-center">Top 5 Supplier by Transaction Frequency</h4>
            </div> 
          </div> 
          <div class="card-body">
            <div class="row">
              <!-- HorizontalBar Chart Starts -->
              <div class="col-sm-6 card-block">
                <div id="bar-bar2" class="height-300">
                  <canvas baseChart class="chart" [datasets]="topSupplierByFreqBarChartData" [labels]="topSupplierByFreqLabels" [options]="topSupplierByFreqBarChartDataOptions" [colors]="chartColors" [legend]="HorbarChartLegend"
                     [chartType]="HorbarChartType" (chartHover)="chartHovered($event)" (chartClick)="chartClicked($event)"></canvas>
               </div>
              </div>
              <!-- HorizontalBar Chart Ends -->

              <!-- Pie Chart Starts -->
               <div class="col-sm-6 card-block">
                 <div id="pie-chart1" class="height-300">
                     <canvas baseChart class="chart" [data]="topSupplierByFreqPieChartData" [labels]="topSupplierByFreqLabels" [options]="pieChartOptionsByFreq" [chartType]="pieChartType" (chartHover)="chartHovered($event)" [colors]="chartColors"
                             [legend]="pieChartLegend" (chartClick)="chartClicked($event)"></canvas>
                 </div>
               </div>
               <!-- Pie Chart Ends --> 
             </div>
          </div>
        </div>
    </div>
  </div> 
</div> 

<div class="row text-left" *ngIf="isLoaded">
  <div class="col-12" >
    <div class="card card-chart">
      <div class="card-header">
        <h3 class="card-title-2 font-weight-bold text-center">Diversity Of Buyer</h3>
      </div> 
        <div class="chart-wrap mx-3 mt-2 mb-3" style="border: 3px solid #692e2e;border-radius: 10px 10px 0px 0px;">
          <div class="card-header">
            <div class="card-header">
              <h4 class="card-title font-weight-bold text-center">Top 5 Buyer by Transaction Amount</h4>
            </div> 
          </div> 
          <div class="card-body">
            <div class="row">
              <!-- HorizontalBar Chart Starts -->
              <div class="col-sm-6 card-block">
                <div id="bar-bar2" class="height-300">
                  <canvas baseChart class="chart" [datasets]="topBuyerByAmountBarChartData" [labels]="topBuyerByAmountLabels" [options]="topBuyerByAmountBarChartOptions" [colors]="chartColors" [legend]="HorbarChartLegend"
                     [chartType]="HorbarChartType" (chartHover)="chartHovered($event)" (chartClick)="chartClicked($event)"></canvas>
               </div>
              </div>
              <!-- HorizontalBar Chart Ends -->

              <!-- Pie Chart Starts -->
               <div class="col-sm-6 card-block">
                 <div id="pie-chart1" class="height-300">
                     <canvas baseChart class="chart" [data]="topBuyerByAmountPieChartData" [labels]="topBuyerByAmountLabels" [options]="pieChartOptionsByAmount" [chartType]="pieChartType" (chartHover)="chartHovered($event)" [colors]="chartColors"
                             [legend]="pieChartLegend" (chartClick)="chartClicked($event)"></canvas>
                 </div>
               </div>
               <!-- Pie Chart Ends --> 
             </div>
          </div>
        </div>

        <div class="chart-wrap mx-3 mb-3" style="border: 3px solid #692e2e;border-radius: 0px 0px 10px 10px;">
          <div class="card-header">
            <div class="card-header">
              <h4 class="card-title font-weight-bold text-center">Top 5 Buyer by Transaction Frequency</h4>
            </div> 
          </div> 
          <div class="card-body">
            <div class="row">
              <!-- HorizontalBar Chart Starts -->
              <div class="col-sm-6 card-block">
                <div id="bar-bar2" class="height-300">
                  <canvas baseChart class="chart" [datasets]="topBuyerByFreqBarChartData" [labels]="topBuyerByFreqLabels" [options]="topBuyerByFreqBarChartDataOptions" [colors]="chartColors" [legend]="HorbarChartLegend"
                     [chartType]="HorbarChartType" (chartHover)="chartHovered($event)" (chartClick)="chartClicked($event)"></canvas>
               </div>
              </div>
              <!-- HorizontalBar Chart Ends -->

              <!-- Pie Chart Starts -->
               <div class="col-sm-6 card-block">
                 <div id="pie-chart1" class="height-300">
                     <canvas baseChart class="chart" [data]="topBuyerByFreqPieChartData" [labels]="topBuyerByFreqLabels" [options]="pieChartOptionsByFreq" [chartType]="pieChartType" (chartHover)="chartHovered($event)" [colors]="chartColors"
                             [legend]="pieChartLegend" (chartClick)="chartClicked($event)"></canvas>
                 </div>
               </div>
               <!-- Pie Chart Ends --> 
             </div>
          </div>
        </div>
    </div>
  </div> 
</div> 